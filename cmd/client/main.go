package main

import (
	"bufio"
	"fmt"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"cyber-bastion/internal/client"
	"cyber-bastion/pkg/config"
	"cyber-bastion/pkg/logger"

	"github.com/spf13/cobra"
	"go.uber.org/zap"
)

var (
	configFile  string
	logLevel    string
	serverHost  string
	serverPort  int
	interactive bool
)

func main() {
	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}

var rootCmd = &cobra.Command{
	Use:   "client",
	Short: "Cyber Bastion TCP Client",
	Long:  `A TCP client application for cyber bastion system with authentication and heartbeat support.`,
	RunE:  runClient,
}

func init() {
	rootCmd.PersistentFlags().StringVarP(&configFile, "config", "c", "", "config file path")
	rootCmd.PersistentFlags().StringVarP(&logLevel, "log-level", "l", "", "log level (debug, info, warn, error)")
	rootCmd.PersistentFlags().StringVarP(&serverHost, "server", "s", "", "server host")
	rootCmd.PersistentFlags().IntVarP(&serverPort, "port", "p", 0, "server port")
	rootCmd.PersistentFlags().BoolVarP(&interactive, "interactive", "i", false, "run in interactive mode")
}

func runClient(cmd *cobra.Command, args []string) error {
	// 加载配置
	cfg, err := config.LoadClientConfig(configFile)
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	// 命令行参数覆盖配置文件
	if logLevel != "" {
		cfg.Logger.Level = logLevel
	}
	if serverHost != "" {
		cfg.ServerHost = serverHost
	}
	if serverPort != 0 {
		cfg.ServerPort = serverPort
	}

	// 初始化日志
	log, err := logger.NewLogger(cfg.Logger)
	if err != nil {
		return fmt.Errorf("failed to initialize logger: %w", err)
	}
	defer log.Sync()

	// 构建启动日志字段
	logFields := []zap.Field{
		zap.String("version", "1.0.0"),
		zap.String("server", fmt.Sprintf("%s:%d", cfg.ServerHost, cfg.ServerPort)),
		zap.String("log_level", cfg.Logger.Level),
	}

	// 添加TLS状态
	if cfg.EnableTLS {
		logFields = append(logFields, zap.Bool("tls_enabled", true))
	} else {
		logFields = append(logFields, zap.Bool("tls_enabled", false))
	}

	// 添加加密状态
	if cfg.EnableEncryption {
		logFields = append(logFields,
			zap.Bool("encryption_enabled", true),
			zap.String("encryption_type", "AES-256-GCM"))

		// 检查加密密钥是否为默认值
		if cfg.EncryptionKey == "change-this-encryption-key-in-production-32-bytes" ||
			cfg.EncryptionKey == "default-encryption-key-change-in-production" {
			log.Warn("Using default encryption key - CHANGE THIS IN PRODUCTION!",
				zap.String("key_hint", cfg.EncryptionKey[:16]+"..."))
		} else {
			log.Info("Custom encryption key configured",
				zap.String("key_hint", cfg.EncryptionKey[:8]+"..."))
		}
	} else {
		logFields = append(logFields, zap.Bool("encryption_enabled", false))
	}

	log.Info("Starting Cyber Bastion Client", logFields...)

	// 创建客户端
	cli := client.NewClient(cfg, log)

	// 启动客户端
	if err := cli.Start(); err != nil {
		return fmt.Errorf("failed to start client: %w", err)
	}

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	if interactive {
		// 交互模式
		return runInteractiveMode(cli, log, sigChan)
	} else {
		// 非交互模式，发送测试数据
		return runTestMode(cli, log, sigChan)
	}
}

func runInteractiveMode(cli *client.Client, log *zap.Logger, sigChan chan os.Signal) error {
	log.Info("Running in interactive mode. Type 'quit' to exit.")

	scanner := bufio.NewScanner(os.Stdin)

	for {
		select {
		case sig := <-sigChan:
			log.Info("Received signal, shutting down", zap.String("signal", sig.String()))
			return cli.Stop()
		default:
			fmt.Print("Enter message (or 'quit' to exit): ")
			if !scanner.Scan() {
				// 检查扫描器错误
				if err := scanner.Err(); err != nil {
					log.Error("Scanner error", zap.Error(err))
					return err
				}
				// 如果没有错误，说明到达了EOF（如Ctrl+D），正常退出
				log.Info("EOF received, shutting down")
				return cli.Stop()
			}

			input := strings.TrimSpace(scanner.Text())
			if input == "quit" {
				log.Info("User requested quit")
				return cli.Stop()
			}

			if input == "" {
				continue
			}

			if !cli.IsConnected() || !cli.IsAuthorized() {
				fmt.Println("Client not connected or not authorized")
				continue
			}

			if err := cli.SendData([]byte(input)); err != nil {
				log.Error("Failed to send data", zap.Error(err))
				fmt.Printf("Error sending data: %v\n", err)
			} else {
				fmt.Println("Message sent successfully")
			}
		}
	}
}

func runTestMode(cli *client.Client, log *zap.Logger, sigChan chan os.Signal) error {
	log.Info("Running in test mode. Sending periodic test messages.")

	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	messageCount := 0

	for {
		select {
		case sig := <-sigChan:
			log.Info("Received signal, shutting down", zap.String("signal", sig.String()))
			return cli.Stop()
		case <-ticker.C:
			if !cli.IsConnected() || !cli.IsAuthorized() {
				log.Warn("Client not connected or not authorized, skipping message")
				continue
			}

			messageCount++
			testData := fmt.Sprintf("Test message #%d at %s", messageCount, time.Now().Format(time.RFC3339))

			if err := cli.SendData([]byte(testData)); err != nil {
				log.Error("Failed to send test data", zap.Error(err))
			} else {
				log.Info("Test message sent", zap.Int("count", messageCount))
			}
		}
	}
}
