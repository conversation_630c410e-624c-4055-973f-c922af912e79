package main

import (
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"cyber-bastion/internal/server"
	"cyber-bastion/pkg/config"
	"cyber-bastion/pkg/logger"

	"github.com/spf13/cobra"
	"go.uber.org/zap"
)

var (
	configFile string
	logLevel   string
	host       string
	port       int
)

func main() {
	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}

var rootCmd = &cobra.Command{
	Use:   "server",
	Short: "Cyber Bastion TCP Server",
	Long:  `A TCP server application for cyber bastion system with authentication and heartbeat support.`,
	RunE:  runServer,
}

func init() {
	rootCmd.PersistentFlags().StringVarP(&configFile, "config", "c", "", "config file path")
	rootCmd.PersistentFlags().StringVarP(&logLevel, "log-level", "l", "", "log level (debug, info, warn, error)")
	rootCmd.PersistentFlags().StringVarP(&host, "host", "H", "", "server host")
	rootCmd.PersistentFlags().IntVarP(&port, "port", "p", 0, "server port")
}

func runServer(cmd *cobra.Command, args []string) error {
	// 加载配置
	cfg, err := config.LoadServerConfig(configFile)
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	// 命令行参数覆盖配置文件
	if logLevel != "" {
		cfg.Logger.Level = logLevel
	}
	if host != "" {
		cfg.Host = host
	}
	if port != 0 {
		cfg.Port = port
	}

	// 初始化日志
	log, err := logger.NewLogger(cfg.Logger)
	if err != nil {
		return fmt.Errorf("failed to initialize logger: %w", err)
	}
	defer log.Sync()

	// 构建启动日志字段
	logFields := []zap.Field{
		zap.String("version", "1.0.0"),
		zap.String("host", cfg.Host),
		zap.Int("port", cfg.Port),
		zap.String("log_level", cfg.Logger.Level),
	}

	// 添加TLS状态
	if cfg.Security != nil && cfg.Security.EnableTLS {
		logFields = append(logFields, zap.Bool("tls_enabled", true))
	} else {
		logFields = append(logFields, zap.Bool("tls_enabled", false))
	}

	// 添加加密状态
	if cfg.Security != nil && cfg.Security.EnableEncryption {
		logFields = append(logFields,
			zap.Bool("encryption_enabled", true),
			zap.String("encryption_type", "AES-256-GCM"))

		// 检查加密密钥是否为默认值
		if cfg.Security.EncryptionKey == "change-this-encryption-key-in-production-32-bytes" ||
			cfg.Security.EncryptionKey == "default-encryption-key-change-in-production" {
			log.Warn("Using default encryption key - CHANGE THIS IN PRODUCTION!",
				zap.String("key_hint", cfg.Security.EncryptionKey[:16]+"..."))
		} else {
			log.Info("Custom encryption key configured",
				zap.String("key_hint", cfg.Security.EncryptionKey[:8]+"..."))
		}
	} else {
		logFields = append(logFields, zap.Bool("encryption_enabled", false))
	}

	log.Info("Starting Cyber Bastion Server", logFields...)

	// 创建服务器
	srv := server.NewServer(cfg, log)

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动服务器
	errChan := make(chan error, 1)
	go func() {
		if err := srv.Start(); err != nil {
			errChan <- err
		}
	}()

	// 等待信号或错误
	select {
	case err := <-errChan:
		log.Error("Server error", zap.Error(err))
		return err
	case sig := <-sigChan:
		log.Info("Received signal, shutting down", zap.String("signal", sig.String()))
		if err := srv.Stop(); err != nil {
			log.Error("Error stopping server", zap.Error(err))
			return err
		}
	}

	log.Info("Server shutdown complete")
	return nil
}
