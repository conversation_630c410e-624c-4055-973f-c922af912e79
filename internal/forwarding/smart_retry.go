// Package forwarding provides smart retry and error handling mechanisms
package forwarding

import (
	"context"
	"fmt"
	"math"
	"math/rand"
	"net"
	"sync"
	"time"

	"go.uber.org/zap"
)

// ErrorType 错误类型
type ErrorType int

const (
	ErrorTypeTemporary ErrorType = iota
	ErrorTypePermanent
	ErrorTypeTimeout
	ErrorTypeNetwork
	ErrorTypePermission
	ErrorTypeRateLimit
)

// SmartRetryPolicy 智能重试策略
type SmartRetryPolicy struct {
	MaxRetries      int
	BaseDelay       time.Duration
	MaxDelay        time.Duration
	BackoffFactor   float64
	Jitter          bool
	RetryableErrors map[ErrorType]bool
	logger          *zap.Logger
}

// SmartCircuitBreaker 智能断路器
type SmartCircuitBreaker struct {
	state               CircuitState
	failureCount        int64
	successCount        int64
	consecutiveFailures int64
	lastFailTime        time.Time
	lastSuccessTime     time.Time
	timeout             time.Duration
	maxFailures         int64
	halfOpenMaxCalls    int64
	halfOpenCalls       int64
	errorClassifier     func(error) ErrorType
	mu                  sync.RWMutex
	logger              *zap.Logger
	metrics             *CircuitBreakerMetrics
}

// CircuitBreakerMetrics 断路器指标
type CircuitBreakerMetrics struct {
	TotalRequests     int64
	SuccessfulCalls   int64
	FailedCalls       int64
	CircuitOpenCount  int64
	CircuitCloseCount int64
	HalfOpenCalls     int64
	mu                sync.RWMutex
}

// ErrorClassifier 错误分类器
type ErrorClassifier struct {
	logger *zap.Logger
}

// NewSmartRetryPolicy 创建智能重试策略
func NewSmartRetryPolicy(logger *zap.Logger) *SmartRetryPolicy {
	return &SmartRetryPolicy{
		MaxRetries:    3,
		BaseDelay:     time.Millisecond * 100,
		MaxDelay:      time.Second * 10,
		BackoffFactor: 2.0,
		Jitter:        true,
		RetryableErrors: map[ErrorType]bool{
			ErrorTypeTemporary:  true,
			ErrorTypeTimeout:    true,
			ErrorTypeNetwork:    true,
			ErrorTypeRateLimit:  true,
			ErrorTypePermanent:  false,
			ErrorTypePermission: false,
		},
		logger: logger,
	}
}

// NewSmartCircuitBreaker 创建智能断路器
func NewSmartCircuitBreaker(maxFailures int64, timeout time.Duration, logger *zap.Logger) *SmartCircuitBreaker {
	return &SmartCircuitBreaker{
		state:            StateClosed,
		maxFailures:      maxFailures,
		timeout:          timeout,
		halfOpenMaxCalls: 5,
		errorClassifier:  NewErrorClassifier(logger).ClassifyError,
		logger:           logger,
		metrics:          &CircuitBreakerMetrics{},
	}
}

// NewErrorClassifier 创建错误分类器
func NewErrorClassifier(logger *zap.Logger) *ErrorClassifier {
	return &ErrorClassifier{logger: logger}
}

// ClassifyError 分类错误
func (ec *ErrorClassifier) ClassifyError(err error) ErrorType {
	if err == nil {
		return ErrorTypeTemporary // 不应该发生，但作为默认值
	}

	// 网络错误
	if netErr, ok := err.(net.Error); ok {
		if netErr.Timeout() {
			return ErrorTypeTimeout
		}
		// 不再使用已弃用的 Temporary() 方法
		return ErrorTypeNetwork
	}

	// 权限错误
	errStr := err.Error()
	if containsAny(errStr, []string{
		"permission denied",
		"operation not permitted",
		"access denied",
		"socket: operation not permitted",
	}) {
		return ErrorTypePermission
	}

	// 速率限制错误
	if containsAny(errStr, []string{
		"rate limit",
		"too many requests",
		"quota exceeded",
		"throttled",
	}) {
		return ErrorTypeRateLimit
	}

	// 网络连接错误
	if containsAny(errStr, []string{
		"connection refused",
		"connection reset",
		"network unreachable",
		"host unreachable",
		"no route to host",
	}) {
		return ErrorTypeNetwork
	}

	// 永久错误
	if containsAny(errStr, []string{
		"invalid argument",
		"not supported",
		"protocol not supported",
		"address family not supported",
	}) {
		return ErrorTypePermanent
	}

	// 默认为临时错误
	return ErrorTypeTemporary
}

// Execute 执行带重试的操作
func (srp *SmartRetryPolicy) Execute(ctx context.Context, operation func() error) error {
	var lastErr error

	for attempt := 0; attempt <= srp.MaxRetries; attempt++ {
		if ctx.Err() != nil {
			return ctx.Err()
		}

		err := operation()
		if err == nil {
			if attempt > 0 {
				srp.logger.Debug("Operation succeeded after retry",
					zap.Int("attempt", attempt))
			}
			return nil
		}

		lastErr = err
		errorType := NewErrorClassifier(srp.logger).ClassifyError(err)

		// 检查是否应该重试
		if !srp.shouldRetry(errorType, attempt) {
			srp.logger.Debug("Not retrying due to error type or max attempts",
				zap.Error(err),
				zap.String("error_type", errorType.String()),
				zap.Int("attempt", attempt))
			break
		}

		// 计算延迟
		delay := srp.calculateDelay(attempt)

		srp.logger.Debug("Retrying operation",
			zap.Error(err),
			zap.String("error_type", errorType.String()),
			zap.Int("attempt", attempt),
			zap.Duration("delay", delay))

		// 等待重试
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(delay):
			// 继续重试
		}
	}

	return fmt.Errorf("operation failed after %d attempts: %w", srp.MaxRetries+1, lastErr)
}

// shouldRetry 判断是否应该重试
func (srp *SmartRetryPolicy) shouldRetry(errorType ErrorType, attempt int) bool {
	if attempt >= srp.MaxRetries {
		return false
	}

	retryable, exists := srp.RetryableErrors[errorType]
	return exists && retryable
}

// calculateDelay 计算重试延迟
func (srp *SmartRetryPolicy) calculateDelay(attempt int) time.Duration {
	delay := float64(srp.BaseDelay) * math.Pow(srp.BackoffFactor, float64(attempt))

	if delay > float64(srp.MaxDelay) {
		delay = float64(srp.MaxDelay)
	}

	// 添加抖动
	if srp.Jitter {
		jitter := delay * 0.1 * (rand.Float64()*2 - 1) // ±10% 抖动
		delay += jitter
	}

	return time.Duration(delay)
}

// Allow 检查断路器是否允许请求
func (scb *SmartCircuitBreaker) Allow() bool {
	scb.mu.Lock()
	defer scb.mu.Unlock()

	scb.metrics.TotalRequests++

	switch scb.state {
	case StateClosed:
		return true
	case StateOpen:
		if time.Since(scb.lastFailTime) > scb.timeout {
			scb.state = StateHalfOpen
			scb.halfOpenCalls = 0
			scb.logger.Info("Circuit breaker transitioning to half-open")
			return true
		}
		return false
	case StateHalfOpen:
		if scb.halfOpenCalls < scb.halfOpenMaxCalls {
			scb.halfOpenCalls++
			scb.metrics.HalfOpenCalls++
			return true
		}
		return false
	default:
		return false
	}
}

// RecordSuccess 记录成功
func (scb *SmartCircuitBreaker) RecordSuccess() {
	scb.mu.Lock()
	defer scb.mu.Unlock()

	scb.successCount++
	scb.consecutiveFailures = 0
	scb.lastSuccessTime = time.Now()
	scb.metrics.SuccessfulCalls++

	if scb.state == StateHalfOpen {
		if scb.halfOpenCalls >= scb.halfOpenMaxCalls {
			scb.state = StateClosed
			scb.failureCount = 0
			scb.metrics.CircuitCloseCount++
			scb.logger.Info("Circuit breaker closed after successful half-open test")
		}
	}
}

// RecordFailure 记录失败
func (scb *SmartCircuitBreaker) RecordFailure(err error) {
	scb.mu.Lock()
	defer scb.mu.Unlock()

	scb.failureCount++
	scb.consecutiveFailures++
	scb.lastFailTime = time.Now()
	scb.metrics.FailedCalls++

	errorType := scb.errorClassifier(err)

	// 根据错误类型决定是否触发断路器
	shouldTrip := false
	switch errorType {
	case ErrorTypePermanent, ErrorTypePermission:
		// 永久错误和权限错误不应该触发断路器
		shouldTrip = false
	case ErrorTypeTimeout, ErrorTypeNetwork:
		// 超时和网络错误更容易触发断路器
		shouldTrip = scb.consecutiveFailures >= scb.maxFailures/2
	default:
		shouldTrip = scb.consecutiveFailures >= scb.maxFailures
	}

	if shouldTrip && scb.state != StateOpen {
		scb.state = StateOpen
		scb.metrics.CircuitOpenCount++
		scb.logger.Warn("Circuit breaker opened",
			zap.Int64("consecutive_failures", scb.consecutiveFailures),
			zap.String("error_type", errorType.String()),
			zap.Error(err))
	}
}

// GetMetrics 获取断路器指标
func (scb *SmartCircuitBreaker) GetMetrics() CircuitBreakerMetrics {
	scb.metrics.mu.RLock()
	defer scb.metrics.mu.RUnlock()

	return CircuitBreakerMetrics{
		TotalRequests:     scb.metrics.TotalRequests,
		SuccessfulCalls:   scb.metrics.SuccessfulCalls,
		FailedCalls:       scb.metrics.FailedCalls,
		CircuitOpenCount:  scb.metrics.CircuitOpenCount,
		CircuitCloseCount: scb.metrics.CircuitCloseCount,
		HalfOpenCalls:     scb.metrics.HalfOpenCalls,
	}
}

// String 返回错误类型的字符串表示
func (et ErrorType) String() string {
	switch et {
	case ErrorTypeTemporary:
		return "temporary"
	case ErrorTypePermanent:
		return "permanent"
	case ErrorTypeTimeout:
		return "timeout"
	case ErrorTypeNetwork:
		return "network"
	case ErrorTypePermission:
		return "permission"
	case ErrorTypeRateLimit:
		return "rate_limit"
	default:
		return "unknown"
	}
}

// containsAny 检查字符串是否包含任何指定的子字符串
func containsAny(s string, substrings []string) bool {
	for _, substr := range substrings {
		if len(s) >= len(substr) {
			for i := 0; i <= len(s)-len(substr); i++ {
				if s[i:i+len(substr)] == substr {
					return true
				}
			}
		}
	}
	return false
}
