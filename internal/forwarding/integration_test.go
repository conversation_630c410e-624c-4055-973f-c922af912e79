package forwarding

import (
	"net"
	"testing"
	"time"

	"go.uber.org/zap"
)

// MockAddressMappingCache 模拟地址映射缓存
type MockAddressMappingCache struct {
	mappings map[string]*AddressMapping
}

func NewMockAddressMappingCache() *MockAddressMappingCache {
	return &MockAddressMappingCache{
		mappings: make(map[string]*AddressMapping),
	}
}

func (m *MockAddressMappingCache) Set(clientID string, clientRealIP, serverLocalIP, targetIP net.IP) {
	m.mappings[clientID] = &AddressMapping{
		ClientRealIP:  clientRealIP,
		ServerLocalIP: serverLocalIP,
		TargetIP:      targetIP,
		LastUsed:      time.Now(),
		PacketCount:   1,
	}
}

func (m *MockAddressMappingCache) Get(clientID string) (*AddressMapping, bool) {
	mapping, exists := m.mappings[clientID]
	return mapping, exists
}

func (m *MockAddressMappingCache) Update(clientID string) {
	if mapping, exists := m.mappings[clientID]; exists {
		mapping.LastUsed = time.Now()
		mapping.PacketCount++
	}
}

func (m *MockAddressMappingCache) Cleanup() {
	// 简单实现，不做实际清理
}

// TestForwardingEngineWithAddressMapping 测试转发引擎的地址映射功能
func TestForwardingEngineWithAddressMapping(t *testing.T) {
	logger := zap.NewNop()
	connPool := NewConnectionPool(10, 100, time.Minute*5, time.Second*10, logger)
	defer connPool.Close()

	// 创建转发引擎
	engine := NewForwardingEngine(connPool, logger)

	// 创建模拟地址映射缓存
	mockCache := NewMockAddressMappingCache()
	engine.SetAddressMappingCache(mockCache)

	// 测试数据
	clientID := "*************:12345"
	clientRealIP := net.ParseIP("*************")
	serverLocalIP := net.ParseIP("********")
	targetIP := net.ParseIP("*******")

	// 预设地址映射
	mockCache.Set(clientID, clientRealIP, serverLocalIP, targetIP)

	// 创建测试ICMP数据包
	icmpPacket := createTestICMPPacket(clientRealIP, targetIP)

	// 测试转发功能
	response, err := engine.ForwardPacket(icmpPacket, clientID)
	if err != nil {
		t.Logf("Forward packet failed (expected for test environment): %v", err)
	}

	// 验证地址映射是否被记录
	mapping, exists := mockCache.Get(clientID)
	if !exists {
		t.Error("Address mapping should be recorded")
	}

	if mapping != nil {
		if !mapping.ClientRealIP.Equal(clientRealIP) {
			t.Errorf("Expected client real IP %s, got %s", clientRealIP, mapping.ClientRealIP)
		}
		if !mapping.TargetIP.Equal(targetIP) {
			t.Errorf("Expected target IP %s, got %s", targetIP, mapping.TargetIP)
		}
	}

	// 测试响应包修正（如果有响应）
	if response != nil && len(response) >= 20 {
		// 检查响应包的目标IP是否被修正为客户端真实IP
		responseDestIP := net.IP(response[16:20])
		if !responseDestIP.Equal(clientRealIP) {
			t.Errorf("Response packet destination IP should be %s, got %s", clientRealIP, responseDestIP)
		}
	}

	t.Log("ForwardingEngine address mapping integration test completed")
}

// TestOptimizedForwardingHandlerIntegration 测试优化转发处理器集成
func TestOptimizedForwardingHandlerIntegration(t *testing.T) {
	logger := zap.NewNop()

	// 创建处理器配置
	config := &HandlerConfig{
		WorkerCount:     2,
		QueueSize:       100,
		MaxIdleConns:    5,
		MaxActiveConns:  50,
		IdleTimeout:     time.Minute,
		DialTimeout:     time.Second * 5,
		EnableMetrics:   true,
		MetricsInterval: time.Second * 10,
		EnableCache:     true,
		CacheSize:       100,
		CacheTTL:        time.Minute,
	}

	// 创建优化转发处理器
	handler, err := NewOptimizedForwardingHandler(config, logger)
	if err != nil {
		t.Fatalf("Failed to create optimized forwarding handler: %v", err)
	}
	defer handler.Stop()

	// 创建模拟地址映射缓存
	mockCache := NewMockAddressMappingCache()
	handler.SetAddressMappingCache(mockCache)

	// 启动处理器
	if err := handler.Start(); err != nil {
		t.Fatalf("Failed to start optimized forwarding handler: %v", err)
	}

	// 测试数据
	clientID := "*************:54321"
	clientRealIP := net.ParseIP("*************")
	targetIP := net.ParseIP("*******")

	// 创建测试UDP数据包
	udpPacket := createTestUDPPacket(clientRealIP, targetIP, 53, 12345)

	// 测试异步处理
	done := make(chan bool, 1)

	err = handler.HandlePacket(udpPacket, clientID, func(response []byte, err error) {
		if err != nil {
			t.Logf("Packet handling failed (expected for test environment): %v", err)
		}
		done <- true
	})

	if err != nil {
		t.Fatalf("Failed to submit packet: %v", err)
	}

	// 等待处理完成
	select {
	case <-done:
		// 处理完成
	case <-time.After(time.Second * 10):
		t.Fatal("Packet processing timeout")
	}

	// 验证地址映射是否被记录
	time.Sleep(time.Millisecond * 100) // 给一点时间让映射被记录
	mapping, exists := mockCache.Get(clientID)
	if !exists {
		t.Error("Address mapping should be recorded by optimized handler")
	}

	if mapping != nil {
		if !mapping.ClientRealIP.Equal(clientRealIP) {
			t.Errorf("Expected client real IP %s, got %s", clientRealIP, mapping.ClientRealIP)
		}
		if !mapping.TargetIP.Equal(targetIP) {
			t.Errorf("Expected target IP %s, got %s", targetIP, mapping.TargetIP)
		}
	}

	t.Log("OptimizedForwardingHandler integration test completed")
}

// createTestICMPPacket 创建测试ICMP数据包
func createTestICMPPacket(srcIP, dstIP net.IP) []byte {
	packet := make([]byte, 28) // IP头(20) + ICMP头(8)

	// IP头部
	packet[0] = 0x45 // 版本和头长度
	packet[1] = 0x00 // 服务类型
	packet[2] = 0x00 // 总长度高字节
	packet[3] = 0x1C // 总长度低字节 (28)
	packet[8] = 0x40 // TTL
	packet[9] = 0x01 // 协议 (ICMP)

	// 源IP
	copy(packet[12:16], srcIP.To4())
	// 目标IP
	copy(packet[16:20], dstIP.To4())

	// ICMP头部
	packet[20] = 0x08 // ICMP类型 (Echo Request)
	packet[21] = 0x00 // ICMP代码

	return packet
}

// createTestUDPPacket 创建测试UDP数据包
func createTestUDPPacket(srcIP, dstIP net.IP, dstPort, srcPort uint16) []byte {
	packet := make([]byte, 28) // IP头(20) + UDP头(8)

	// IP头部
	packet[0] = 0x45 // 版本和头长度
	packet[1] = 0x00 // 服务类型
	packet[2] = 0x00 // 总长度高字节
	packet[3] = 0x1C // 总长度低字节 (28)
	packet[8] = 0x40 // TTL
	packet[9] = 0x11 // 协议 (UDP)

	// 源IP
	copy(packet[12:16], srcIP.To4())
	// 目标IP
	copy(packet[16:20], dstIP.To4())

	// UDP头部
	packet[20] = byte(srcPort >> 8)   // 源端口高字节
	packet[21] = byte(srcPort & 0xFF) // 源端口低字节
	packet[22] = byte(dstPort >> 8)   // 目标端口高字节
	packet[23] = byte(dstPort & 0xFF) // 目标端口低字节
	packet[24] = 0x00                 // UDP长度高字节
	packet[25] = 0x08                 // UDP长度低字节 (8)

	return packet
}
