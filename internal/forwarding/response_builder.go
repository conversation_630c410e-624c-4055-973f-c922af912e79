package forwarding

import (
	"encoding/binary"
	"net"

	"go.uber.org/zap"
)

// buildICMPResponsePacket 构造ICMP响应包
func (fe *ForwardingEngine) buildICMPResponsePacket(clientRealIP, targetIP net.IP, icmpData []byte) []byte {
	// 构造IP头部
	ipHeader := make([]byte, 20)

	// IP版本和头部长度
	ipHeader[0] = 0x45 // IPv4, 20字节头部

	// 服务类型
	ipHeader[1] = 0x00

	// 总长度
	totalLen := 20 + len(icmpData)
	binary.BigEndian.PutUint16(ipHeader[2:4], uint16(totalLen))

	// 标识
	ipHeader[4] = 0x00
	ipHeader[5] = 0x00

	// 标志和片偏移
	ipHeader[6] = 0x40 // Don't Fragment
	ipHeader[7] = 0x00

	// TTL
	ipHeader[8] = 64

	// 协议
	ipHeader[9] = 1 // ICMP

	// 校验和（稍后计算）
	ipHeader[10] = 0x00
	ipHeader[11] = 0x00

	// 源IP（目标IP）
	copy(ipHeader[12:16], targetIP.To4())

	// 目标IP（客户端真实IP）
	copy(ipHeader[16:20], clientRealIP.To4())

	// 计算IP头部校验和
	checksum := fe.calculateIPChecksum(ipHeader)
	binary.BigEndian.PutUint16(ipHeader[10:12], checksum)

	// 组合完整数据包
	responsePacket := make([]byte, len(ipHeader)+len(icmpData))
	copy(responsePacket, ipHeader)
	copy(responsePacket[len(ipHeader):], icmpData)

	fe.logger.Debug("Built ICMP response packet",
		zap.String("src_ip", targetIP.String()),
		zap.String("dst_ip", clientRealIP.String()),
		zap.Int("total_len", len(responsePacket)))

	return responsePacket
}

// buildTCPResponsePacket 构造TCP响应包
func (fe *ForwardingEngine) buildTCPResponsePacket(clientRealIP net.IP, originalPacket, responseData []byte) []byte {
	if len(originalPacket) < 40 || len(responseData) < 20 {
		return responseData
	}

	// 提取原始TCP信息
	originalDstIP := net.IP(originalPacket[16:20])
	originalSrcPort := binary.BigEndian.Uint16(originalPacket[20:22])
	originalDstPort := binary.BigEndian.Uint16(originalPacket[22:24])

	// 构造新的IP头部
	ipHeader := make([]byte, 20)

	// IP版本和头部长度
	ipHeader[0] = 0x45 // IPv4, 20字节头部

	// 服务类型
	ipHeader[1] = 0x00

	// 总长度
	totalLen := len(responseData)
	binary.BigEndian.PutUint16(ipHeader[2:4], uint16(totalLen))

	// 标识
	binary.BigEndian.PutUint16(ipHeader[4:6], binary.BigEndian.Uint16(originalPacket[4:6]))

	// 标志和片偏移
	ipHeader[6] = 0x40 // Don't Fragment
	ipHeader[7] = 0x00

	// TTL
	ipHeader[8] = 64

	// 协议
	ipHeader[9] = 6 // TCP

	// 校验和（稍后计算）
	ipHeader[10] = 0x00
	ipHeader[11] = 0x00

	// 源IP（原始目标IP）
	copy(ipHeader[12:16], originalDstIP.To4())

	// 目标IP（客户端真实IP）
	copy(ipHeader[16:20], clientRealIP.To4())

	// 计算IP头部校验和
	checksum := fe.calculateIPChecksum(ipHeader)
	binary.BigEndian.PutUint16(ipHeader[10:12], checksum)

	// 修改TCP头部的端口
	if len(responseData) >= 24 {
		// 交换源端口和目标端口
		binary.BigEndian.PutUint16(responseData[20:22], originalDstPort) // 源端口
		binary.BigEndian.PutUint16(responseData[22:24], originalSrcPort) // 目标端口
	}

	// 组合完整数据包
	responsePacket := make([]byte, len(ipHeader)+len(responseData)-20)
	copy(responsePacket, ipHeader)
	copy(responsePacket[len(ipHeader):], responseData[20:])

	fe.logger.Debug("Built TCP response packet",
		zap.String("src_ip", originalDstIP.String()),
		zap.String("dst_ip", clientRealIP.String()),
		zap.Uint16("src_port", originalDstPort),
		zap.Uint16("dst_port", originalSrcPort),
		zap.Int("total_len", len(responsePacket)))

	return responsePacket
}

// buildUDPResponsePacket 构造UDP响应包
func (fe *ForwardingEngine) buildUDPResponsePacket(clientRealIP net.IP, originalPacket, responseData []byte) []byte {
	if len(originalPacket) < 28 || len(responseData) < 20 {
		return responseData
	}

	// 提取原始UDP信息
	originalDstIP := net.IP(originalPacket[16:20])
	originalSrcPort := binary.BigEndian.Uint16(originalPacket[20:22])
	originalDstPort := binary.BigEndian.Uint16(originalPacket[22:24])

	// 构造新的IP头部
	ipHeader := make([]byte, 20)

	// IP版本和头部长度
	ipHeader[0] = 0x45 // IPv4, 20字节头部

	// 服务类型
	ipHeader[1] = 0x00

	// 总长度
	totalLen := len(responseData)
	binary.BigEndian.PutUint16(ipHeader[2:4], uint16(totalLen))

	// 标识
	binary.BigEndian.PutUint16(ipHeader[4:6], binary.BigEndian.Uint16(originalPacket[4:6]))

	// 标志和片偏移
	ipHeader[6] = 0x40 // Don't Fragment
	ipHeader[7] = 0x00

	// TTL
	ipHeader[8] = 64

	// 协议
	ipHeader[9] = 17 // UDP

	// 校验和（稍后计算）
	ipHeader[10] = 0x00
	ipHeader[11] = 0x00

	// 源IP（原始目标IP）
	copy(ipHeader[12:16], originalDstIP.To4())

	// 目标IP（客户端真实IP）
	copy(ipHeader[16:20], clientRealIP.To4())

	// 计算IP头部校验和
	checksum := fe.calculateIPChecksum(ipHeader)
	binary.BigEndian.PutUint16(ipHeader[10:12], checksum)

	// 修改UDP头部的端口
	if len(responseData) >= 24 {
		// 交换源端口和目标端口
		binary.BigEndian.PutUint16(responseData[20:22], originalDstPort) // 源端口
		binary.BigEndian.PutUint16(responseData[22:24], originalSrcPort) // 目标端口
	}

	// 组合完整数据包
	responsePacket := make([]byte, len(ipHeader)+len(responseData)-20)
	copy(responsePacket, ipHeader)
	copy(responsePacket[len(ipHeader):], responseData[20:])

	fe.logger.Debug("Built UDP response packet",
		zap.String("src_ip", originalDstIP.String()),
		zap.String("dst_ip", clientRealIP.String()),
		zap.Uint16("src_port", originalDstPort),
		zap.Uint16("dst_port", originalSrcPort),
		zap.Int("total_len", len(responsePacket)))

	return responsePacket
}

// calculateIPChecksum 计算IP头部校验和
func (fe *ForwardingEngine) calculateIPChecksum(header []byte) uint16 {
	var sum uint32

	// 将头部按16位分组求和
	for i := 0; i < len(header); i += 2 {
		if i+1 < len(header) {
			sum += uint32(header[i])<<8 + uint32(header[i+1])
		} else {
			sum += uint32(header[i]) << 8
		}
	}

	// 处理进位
	for sum>>16 != 0 {
		sum = (sum & 0xFFFF) + (sum >> 16)
	}

	// 取反
	return uint16(^sum)
}
