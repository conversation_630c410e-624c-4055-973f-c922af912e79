package forwarding

import (
	"net"
	"time"
)

// AddressMappingCacheInterface 地址映射缓存接口
type AddressMappingCacheInterface interface {
	// Set 设置地址映射
	Set(clientID string, clientRealIP, serverLocalIP, targetIP net.IP)
	
	// Get 获取地址映射
	Get(clientID string) (*AddressMapping, bool)
	
	// Update 更新地址映射的使用时间和计数
	Update(clientID string)
	
	// Cleanup 清理过期的映射
	Cleanup()
}

// AddressMapping 地址映射关系
type AddressMapping struct {
	ClientRealIP  net.IP    // 客户端真实IP
	ServerLocalIP net.IP    // 服务器本地IP
	TargetIP      net.IP    // 目标IP
	LastUsed      time.Time // 最后使用时间
	PacketCount   int64     // 数据包计数
}

// ForwardingInterface 转发接口（扩展版本）
type ForwardingInterface interface {
	// ForwardPacket 转发数据包
	ForwardPacket(packet []byte, clientID string) ([]byte, error)
	
	// ForwardPacketWithMapping 带地址映射的转发数据包
	ForwardPacketWithMapping(packet []byte, clientID string, mappingCache AddressMappingCacheInterface) ([]byte, error)
	
	// SetAddressMappingCache 设置地址映射缓存
	SetAddressMappingCache(cache AddressMappingCacheInterface)
	
	// GetMetrics 获取转发指标
	GetMetrics() ForwardingMetrics
	
	// Close 关闭转发引擎
	Close() error
}

// ResponsePacketBuilder 响应包构造器接口
type ResponsePacketBuilder interface {
	// BuildICMPResponsePacket 构造ICMP响应包
	BuildICMPResponsePacket(clientRealIP, targetIP net.IP, icmpData []byte) ([]byte, error)
	
	// BuildTCPResponsePacket 构造TCP响应包
	BuildTCPResponsePacket(clientRealIP net.IP, originalPacket, responseData []byte) ([]byte, error)
	
	// BuildUDPResponsePacket 构造UDP响应包
	BuildUDPResponsePacket(clientRealIP net.IP, originalPacket, responseData []byte) ([]byte, error)
}
