package forwarding

import (
	"context"
	"fmt"
	"net"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
)

// 注意：ForwardingInterface 现在定义在 interfaces.go 中

// ForwardingEngine 高性能转发引擎
type ForwardingEngine struct {
	connPool       *ConnectionPool
	cache          *RoutingCache
	logger         *zap.Logger
	metrics        *ForwardingMetrics
	retryPolicy    *SmartRetryPolicy
	circuitBreaker *SmartCircuitBreaker
	addressMapping AddressMappingCacheInterface // 新增：地址映射缓存
	mu             sync.RWMutex
}

// ForwardingMetrics 转发指标
type ForwardingMetrics struct {
	PacketsForwarded    int64
	PacketsFailed       int64
	BytesForwarded      int64
	AverageLatency      time.Duration
	CacheHitRate        float64
	ConnectionErrors    int64
	RetryAttempts       int64
	CircuitBreakerTrips int64
	mu                  sync.RWMutex
}

// RoutingCache 路由缓存
type RoutingCache struct {
	cache   map[string]*CacheEntry
	mu      sync.RWMutex
	maxSize int
	ttl     time.Duration
	hits    int64
	misses  int64
}

// CacheEntry 缓存条目
type CacheEntry struct {
	Route     *Route
	ExpiresAt time.Time
	HitCount  int64
	CreatedAt time.Time
}

// Route 路由信息
type Route struct {
	Protocol    uint8
	Destination net.IP
	Port        uint16
	Strategy    ForwardingStrategy
	LastUsed    time.Time
}

// ForwardingStrategy 转发策略
type ForwardingStrategy int

const (
	StrategyRawSocket ForwardingStrategy = iota
	StrategyTCPProxy
	StrategyUDPProxy
	StrategyHybrid
)

// RetryPolicy 重试策略
type RetryPolicy struct {
	MaxRetries    int
	InitialDelay  time.Duration
	MaxDelay      time.Duration
	BackoffFactor float64
	Jitter        bool
}

// CircuitBreaker 断路器
type CircuitBreaker struct {
	state        CircuitState
	failureCount int64
	successCount int64
	lastFailTime time.Time
	timeout      time.Duration
	maxFailures  int64
	mu           sync.RWMutex
}

// CircuitState 断路器状态
type CircuitState int

const (
	StateClosed CircuitState = iota
	StateOpen
	StateHalfOpen
)

// NewForwardingEngine 创建新的转发引擎
func NewForwardingEngine(connPool *ConnectionPool, logger *zap.Logger) *ForwardingEngine {
	return &ForwardingEngine{
		connPool:       connPool,
		cache:          NewRoutingCache(1000, time.Minute*5),
		logger:         logger,
		metrics:        &ForwardingMetrics{},
		retryPolicy:    NewSmartRetryPolicy(logger),
		circuitBreaker: NewSmartCircuitBreaker(10, time.Minute, logger),
	}
}

// ForwardPacket 转发数据包（支持地址映射）
func (fe *ForwardingEngine) ForwardPacket(packet []byte, clientID string) ([]byte, error) {
	startTime := time.Now()

	// 解析数据包
	route, err := fe.parsePacket(packet)
	if err != nil {
		fe.updateMetrics(false, 0, time.Since(startTime))
		return nil, fmt.Errorf("failed to parse packet: %w", err)
	}

	// 记录地址映射（如果缓存可用）
	if fe.addressMapping != nil {
		fe.recordAddressMapping(packet, clientID)
	}

	// 检查断路器状态
	if !fe.circuitBreaker.Allow() {
		fe.updateMetrics(false, 0, time.Since(startTime))
		return nil, fmt.Errorf("circuit breaker is open")
	}

	// 执行转发（带重试）
	response, err := fe.executeWithRetry(func() ([]byte, error) {
		return fe.forwardWithStrategy(packet, route, clientID)
	})

	latency := time.Since(startTime)

	if err != nil {
		fe.circuitBreaker.RecordFailure(err)
		fe.updateMetrics(false, len(packet), latency)
		return nil, err
	}

	// 修正响应包IP地址（如果缓存可用）
	if fe.addressMapping != nil && response != nil {
		response = fe.fixResponsePacketIP(response, packet, clientID)
	}

	fe.circuitBreaker.RecordSuccess()
	fe.updateMetrics(true, len(packet), latency)

	return response, nil
}

// parsePacket 解析数据包
func (fe *ForwardingEngine) parsePacket(packet []byte) (*Route, error) {
	if len(packet) < 20 {
		return nil, fmt.Errorf("packet too short")
	}

	// 解析IP头部
	version := packet[0] >> 4
	var protocol uint8
	var dstIP net.IP
	var port uint16

	switch version {
	case 4:
		if len(packet) < 20 {
			return nil, fmt.Errorf("IPv4 packet too short")
		}
		protocol = packet[9]
		dstIP = net.IP(packet[16:20])

		// 解析端口（如果是TCP/UDP）
		if (protocol == 6 || protocol == 17) && len(packet) >= 24 {
			port = uint16(packet[22])<<8 | uint16(packet[23])
		}

	case 6:
		if len(packet) < 40 {
			return nil, fmt.Errorf("IPv6 packet too short")
		}
		protocol = packet[6]
		dstIP = net.IP(packet[24:40])

		// 解析端口（如果是TCP/UDP）
		if (protocol == 6 || protocol == 17) && len(packet) >= 44 {
			port = uint16(packet[42])<<8 | uint16(packet[43])
		}

	default:
		return nil, fmt.Errorf("unsupported IP version: %d", version)
	}

	// 确定转发策略
	strategy := fe.determineStrategy(protocol, dstIP, port)

	return &Route{
		Protocol:    protocol,
		Destination: dstIP,
		Port:        port,
		Strategy:    strategy,
		LastUsed:    time.Now(),
	}, nil
}

// determineStrategy 确定转发策略
func (fe *ForwardingEngine) determineStrategy(protocol uint8, dstIP net.IP, port uint16) ForwardingStrategy {
	switch protocol {
	case 1: // ICMP
		return StrategyRawSocket
	case 6: // TCP
		return StrategyHybrid // 使用混合策略
	case 17: // UDP
		return StrategyRawSocket
	default:
		return StrategyRawSocket
	}
}

// forwardWithStrategy 根据策略转发
func (fe *ForwardingEngine) forwardWithStrategy(packet []byte, route *Route, clientID string) ([]byte, error) {
	switch route.Strategy {
	case StrategyRawSocket:
		return fe.forwardRawSocket(packet, route, clientID)
	case StrategyTCPProxy:
		return fe.forwardTCPProxy(packet, route, clientID)
	case StrategyUDPProxy:
		return fe.forwardUDPProxy(packet, route, clientID)
	case StrategyHybrid:
		return fe.forwardHybrid(packet, route, clientID)
	default:
		return nil, fmt.Errorf("unknown forwarding strategy: %d", route.Strategy)
	}
}

// forwardRawSocket 原始套接字转发（带权限检测和降级策略）
func (fe *ForwardingEngine) forwardRawSocket(packet []byte, route *Route, clientID string) ([]byte, error) {
	// 创建原始套接字连接
	var protocolName string
	var payloadOffset int

	switch route.Protocol {
	case 1: // ICMP
		protocolName = "ip4:icmp"
		payloadOffset = 20
	case 17: // UDP
		protocolName = "ip4:udp"
		payloadOffset = 20
	default:
		protocolName = fmt.Sprintf("ip4:%d", route.Protocol)
		payloadOffset = 20
	}

	// 尝试创建原始套接字，如果失败则降级到用户空间实现
	conn, err := fe.createRawSocketWithFallback(protocolName, route.Destination.String(), route.Protocol)
	if err != nil {
		return nil, fmt.Errorf("failed to create socket connection: %w", err)
	}
	defer conn.Close()

	// 发送数据
	payload := packet[payloadOffset:]
	_, err = conn.Write(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to send packet: %w", err)
	}

	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(time.Second * 5))

	// 读取响应
	responseBuffer := make([]byte, 1500)
	n, err := conn.Read(responseBuffer)
	if err != nil {
		// 对于某些协议，没有响应是正常的
		return nil, nil
	}

	return responseBuffer[:n], nil
}

// forwardTCPProxy TCP代理转发
func (fe *ForwardingEngine) forwardTCPProxy(packet []byte, route *Route, clientID string) ([]byte, error) {
	address := fmt.Sprintf("%s:%d", route.Destination.String(), route.Port)

	// 从连接池获取连接
	conn, err := fe.connPool.Get(address)
	if err != nil {
		return nil, fmt.Errorf("failed to get connection: %w", err)
	}
	defer conn.Close()

	// 提取TCP载荷
	payload, err := fe.extractTCPPayload(packet)
	if err != nil {
		return nil, fmt.Errorf("failed to extract TCP payload: %w", err)
	}

	if len(payload) > 0 {
		_, err = conn.Write(payload)
		if err != nil {
			return nil, fmt.Errorf("failed to send TCP data: %w", err)
		}
	}

	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(time.Second * 5))

	// 读取响应
	responseBuffer := make([]byte, 1500)
	n, err := conn.Read(responseBuffer)
	if err != nil {
		return nil, nil // TCP可能没有立即响应
	}

	return responseBuffer[:n], nil
}

// forwardUDPProxy UDP代理转发
func (fe *ForwardingEngine) forwardUDPProxy(packet []byte, route *Route, clientID string) ([]byte, error) {
	// UDP代理实现
	return fe.forwardRawSocket(packet, route, clientID)
}

// forwardHybrid 混合转发策略
func (fe *ForwardingEngine) forwardHybrid(packet []byte, route *Route, clientID string) ([]byte, error) {
	// 检查是否是TCP控制包
	if fe.isTCPControlPacket(packet) {
		return fe.forwardRawSocket(packet, route, clientID)
	} else {
		return fe.forwardTCPProxy(packet, route, clientID)
	}
}

// isTCPControlPacket 检查是否是TCP控制包
func (fe *ForwardingEngine) isTCPControlPacket(packet []byte) bool {
	if len(packet) < 40 {
		return false
	}

	tcpHeader := packet[20:]
	if len(tcpHeader) < 14 {
		return false
	}

	tcpFlags := tcpHeader[13]
	isSyn := (tcpFlags & 0x02) != 0
	isAck := (tcpFlags & 0x10) != 0
	isFin := (tcpFlags & 0x01) != 0
	isRst := (tcpFlags & 0x04) != 0
	isPsh := (tcpFlags & 0x08) != 0

	return isSyn || isFin || isRst || (isAck && !isPsh)
}

// extractTCPPayload 提取TCP载荷
func (fe *ForwardingEngine) extractTCPPayload(packet []byte) ([]byte, error) {
	if len(packet) < 40 {
		return nil, fmt.Errorf("packet too short for TCP")
	}

	ipHeaderLen := int(packet[0]&0x0F) * 4
	tcpHeader := packet[ipHeaderLen:]
	if len(tcpHeader) < 20 {
		return nil, fmt.Errorf("TCP header too short")
	}

	tcpHeaderLen := int(tcpHeader[12]>>4) * 4
	payloadStart := ipHeaderLen + tcpHeaderLen

	if payloadStart >= len(packet) {
		return []byte{}, nil // 没有载荷
	}

	return packet[payloadStart:], nil
}

// executeWithRetry 执行带重试的操作
func (fe *ForwardingEngine) executeWithRetry(fn func() ([]byte, error)) ([]byte, error) {
	var result []byte

	err := fe.retryPolicy.Execute(context.Background(), func() error {
		var execErr error
		result, execErr = fn()
		if execErr != nil {
			fe.metrics.mu.Lock()
			fe.metrics.RetryAttempts++
			fe.metrics.mu.Unlock()
		}
		return execErr
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// updateMetrics 更新指标
func (fe *ForwardingEngine) updateMetrics(success bool, bytes int, latency time.Duration) {
	fe.metrics.mu.Lock()
	defer fe.metrics.mu.Unlock()

	if success {
		fe.metrics.PacketsForwarded++
		fe.metrics.BytesForwarded += int64(bytes)
	} else {
		fe.metrics.PacketsFailed++
	}

	// 更新平均延迟（简化计算）
	fe.metrics.AverageLatency = (fe.metrics.AverageLatency + latency) / 2
}

// GetMetrics 获取转发指标
func (fe *ForwardingEngine) GetMetrics() ForwardingMetrics {
	fe.metrics.mu.RLock()
	defer fe.metrics.mu.RUnlock()
	return *fe.metrics
}

// Close 关闭转发引擎
func (fe *ForwardingEngine) Close() error {
	return nil
}

// SetAddressMappingCache 设置地址映射缓存
func (fe *ForwardingEngine) SetAddressMappingCache(cache AddressMappingCacheInterface) {
	fe.mu.Lock()
	defer fe.mu.Unlock()
	fe.addressMapping = cache
}

// ForwardPacketWithMapping 带地址映射的转发数据包
func (fe *ForwardingEngine) ForwardPacketWithMapping(packet []byte, clientID string, mappingCache AddressMappingCacheInterface) ([]byte, error) {
	// 临时设置地址映射缓存
	originalMapping := fe.addressMapping
	fe.addressMapping = mappingCache
	defer func() {
		fe.addressMapping = originalMapping
	}()

	return fe.ForwardPacket(packet, clientID)
}

// NewRoutingCache 创建路由缓存
func NewRoutingCache(maxSize int, ttl time.Duration) *RoutingCache {
	return &RoutingCache{
		cache:   make(map[string]*CacheEntry),
		maxSize: maxSize,
		ttl:     ttl,
	}
}

// NewCircuitBreaker 创建断路器
func NewCircuitBreaker(maxFailures int64, timeout time.Duration) *CircuitBreaker {
	return &CircuitBreaker{
		state:       StateClosed,
		maxFailures: maxFailures,
		timeout:     timeout,
	}
}

// Allow 检查断路器是否允许请求
func (cb *CircuitBreaker) Allow() bool {
	cb.mu.RLock()
	defer cb.mu.RUnlock()

	switch cb.state {
	case StateClosed:
		return true
	case StateOpen:
		return time.Since(cb.lastFailTime) > cb.timeout
	case StateHalfOpen:
		return true
	default:
		return false
	}
}

// RecordSuccess 记录成功
func (cb *CircuitBreaker) RecordSuccess() {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	cb.successCount++
	if cb.state == StateHalfOpen {
		cb.state = StateClosed
		cb.failureCount = 0
	}
}

// RecordFailure 记录失败
func (cb *CircuitBreaker) RecordFailure() {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	cb.failureCount++
	cb.lastFailTime = time.Now()

	if cb.failureCount >= cb.maxFailures {
		cb.state = StateOpen
	}
}

// createRawSocketWithFallback 创建原始套接字，支持权限检测和降级策略
func (fe *ForwardingEngine) createRawSocketWithFallback(protocol, dest string, protocolNum uint8) (net.Conn, error) {
	// 首先尝试创建原始套接字
	conn, err := net.Dial(protocol, dest)
	if err != nil {
		// 检查是否是权限问题
		if fe.isPermissionError(err) {
			fe.logger.Warn("Raw socket permission denied, falling back to user-space implementation",
				zap.String("protocol", protocol),
				zap.String("dest", dest),
				zap.Error(err))

			// 根据协议类型选择降级策略
			return fe.createUserSpaceSocket(protocolNum, dest)
		}

		// 其他错误直接返回
		return nil, err
	}

	return conn, nil
}

// isPermissionError 检查是否是权限相关错误
func (fe *ForwardingEngine) isPermissionError(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()
	return strings.Contains(errStr, "operation not permitted") ||
		strings.Contains(errStr, "permission denied") ||
		strings.Contains(errStr, "access denied") ||
		strings.Contains(errStr, "socket: operation not permitted")
}

// createUserSpaceSocket 创建用户空间套接字作为降级方案
func (fe *ForwardingEngine) createUserSpaceSocket(protocolNum uint8, dest string) (net.Conn, error) {
	switch protocolNum {
	case 1: // ICMP - 使用UDP套接字模拟
		return fe.createICMPFallbackSocket(dest)
	case 6: // TCP - 使用标准TCP连接
		return fe.createTCPFallbackSocket(dest)
	case 17: // UDP - 使用标准UDP连接
		return fe.createUDPFallbackSocket(dest)
	default:
		// 对于其他协议，尝试使用TCP连接
		fe.logger.Warn("Unknown protocol, falling back to TCP",
			zap.Uint8("protocol", protocolNum),
			zap.String("dest", dest))
		return fe.createTCPFallbackSocket(dest)
	}
}

// createICMPFallbackSocket 创建ICMP降级套接字（使用UDP模拟）
func (fe *ForwardingEngine) createICMPFallbackSocket(dest string) (net.Conn, error) {
	// 对于ICMP，我们可以尝试使用UDP连接到目标主机的一个不太可能开放的端口
	// 这样可以触发ICMP响应
	fallbackPort := "33434" // traceroute常用端口
	udpAddr := net.JoinHostPort(dest, fallbackPort)

	conn, err := net.Dial("udp", udpAddr)
	if err != nil {
		return nil, fmt.Errorf("failed to create ICMP fallback socket: %w", err)
	}

	fe.logger.Debug("Created ICMP fallback socket using UDP",
		zap.String("dest", dest),
		zap.String("fallback_addr", udpAddr))

	return conn, nil
}

// createTCPFallbackSocket 创建TCP降级套接字
func (fe *ForwardingEngine) createTCPFallbackSocket(dest string) (net.Conn, error) {
	// 尝试连接到常见的TCP端口
	commonPorts := []string{"80", "443", "22", "21", "25", "53"}

	for _, port := range commonPorts {
		addr := net.JoinHostPort(dest, port)
		conn, err := net.DialTimeout("tcp", addr, time.Second*2)
		if err == nil {
			fe.logger.Debug("Created TCP fallback socket",
				zap.String("dest", dest),
				zap.String("port", port))
			return conn, nil
		}
	}

	// 如果所有常见端口都失败，返回错误
	return nil, fmt.Errorf("failed to create TCP fallback socket to %s: no accessible ports", dest)
}

// createUDPFallbackSocket 创建UDP降级套接字
func (fe *ForwardingEngine) createUDPFallbackSocket(dest string) (net.Conn, error) {
	// 尝试连接到常见的UDP端口
	commonPorts := []string{"53", "123", "161", "514"}

	for _, port := range commonPorts {
		addr := net.JoinHostPort(dest, port)
		conn, err := net.Dial("udp", addr)
		if err == nil {
			fe.logger.Debug("Created UDP fallback socket",
				zap.String("dest", dest),
				zap.String("port", port))
			return conn, nil
		}
	}

	// 如果失败，使用默认端口53（DNS）
	addr := net.JoinHostPort(dest, "53")
	conn, err := net.Dial("udp", addr)
	if err != nil {
		return nil, fmt.Errorf("failed to create UDP fallback socket: %w", err)
	}

	return conn, nil
}

// recordAddressMapping 记录地址映射
func (fe *ForwardingEngine) recordAddressMapping(packet []byte, clientID string) {
	if len(packet) < 20 {
		return
	}

	srcIP := net.IP(packet[12:16])
	dstIP := net.IP(packet[16:20])
	serverLocalIP := net.ParseIP("********")

	// 从客户端ID提取真实IP或使用数据包源IP
	var clientRealIP net.IP
	if colonIndex := strings.LastIndex(clientID, ":"); colonIndex > 0 {
		clientRealIPStr := clientID[:colonIndex]
		clientRealIP = net.ParseIP(clientRealIPStr)
	}
	if clientRealIP == nil {
		clientRealIP = srcIP
	}

	fe.addressMapping.Set(clientID, clientRealIP, serverLocalIP, dstIP)

	fe.logger.Debug("Recorded address mapping",
		zap.String("client_id", clientID),
		zap.String("client_real_ip", clientRealIP.String()),
		zap.String("packet_src_ip", srcIP.String()),
		zap.String("target_ip", dstIP.String()))
}

// fixResponsePacketIP 修正响应包IP地址
func (fe *ForwardingEngine) fixResponsePacketIP(response, originalPacket []byte, clientID string) []byte {
	if len(response) < 20 || len(originalPacket) < 20 {
		return response
	}

	// 获取地址映射
	mapping, exists := fe.addressMapping.Get(clientID)
	if !exists {
		return response
	}

	// 根据协议类型修正响应包
	protocol := originalPacket[9]
	switch protocol {
	case 1: // ICMP
		return fe.buildICMPResponsePacket(mapping.ClientRealIP, net.IP(originalPacket[16:20]), response[20:])
	case 6: // TCP
		return fe.buildTCPResponsePacket(mapping.ClientRealIP, originalPacket, response)
	case 17: // UDP
		return fe.buildUDPResponsePacket(mapping.ClientRealIP, originalPacket, response)
	default:
		return response
	}
}
