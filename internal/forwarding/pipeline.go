package forwarding

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"go.uber.org/zap"
)

// PacketProcessor 异步数据包处理器
type PacketProcessor struct {
	inputQueue  chan *PacketJob
	outputQueue chan *PacketResult
	workers     []*Worker
	workerCount int
	connPool    *ConnectionPool
	logger      *zap.Logger
	ctx         context.Context
	cancel      context.CancelFunc
	wg          sync.WaitGroup
	metrics     *ProcessorMetrics
	rateLimiter *RateLimiter
}

// PacketJob 数据包处理任务
type PacketJob struct {
	ID        string
	Packet    []byte
	ClientID  string
	Timestamp time.Time
	Callback  func(*PacketResult)
	Context   context.Context
	Priority  int
}

// PacketResult 数据包处理结果
type PacketResult struct {
	ID          string
	ClientID    string
	Response    []byte
	Error       error
	Latency     time.Duration
	Timestamp   time.Time
	ProcessedBy int
	RetryCount  int
}

// Worker 工作协程
type Worker struct {
	id        int
	jobs      <-chan *PacketJob
	results   chan<- *PacketResult
	connPool  *ConnectionPool
	forwarder ForwardingInterface
	logger    *zap.Logger
	ctx       context.Context
	processed int64
	errors    int64
}

// ProcessorMetrics 处理器指标
type ProcessorMetrics struct {
	JobsQueued     int64
	JobsProcessed  int64
	JobsCompleted  int64
	JobsFailed     int64
	AverageLatency int64 // 纳秒
	QueueDepth     int64
	WorkersBusy    int64
	mu             sync.RWMutex
}

// RateLimiter 速率限制器
type RateLimiter struct {
	tokens   chan struct{}
	refill   time.Duration
	capacity int
	logger   *zap.Logger
}

// NewPacketProcessor 创建新的数据包处理器
func NewPacketProcessor(workerCount, queueSize int, connPool *ConnectionPool, logger *zap.Logger) *PacketProcessor {
	ctx, cancel := context.WithCancel(context.Background())

	processor := &PacketProcessor{
		inputQueue:  make(chan *PacketJob, queueSize),
		outputQueue: make(chan *PacketResult, queueSize),
		workerCount: workerCount,
		connPool:    connPool,
		logger:      logger,
		ctx:         ctx,
		cancel:      cancel,
		metrics:     &ProcessorMetrics{},
		rateLimiter: NewRateLimiter(1000, time.Second, logger), // 1000 req/sec
	}

	// 创建工作协程
	processor.workers = make([]*Worker, workerCount)
	for i := 0; i < workerCount; i++ {
		worker := &Worker{
			id:        i,
			jobs:      processor.inputQueue,
			results:   processor.outputQueue,
			connPool:  connPool,
			forwarder: NewForwardingEngine(connPool, logger),
			logger:    logger.With(zap.Int("worker_id", i)),
			ctx:       ctx,
		}
		processor.workers[i] = worker
	}

	return processor
}

// SetAddressMappingCache 为所有worker设置地址映射缓存
func (p *PacketProcessor) SetAddressMappingCache(cache AddressMappingCacheInterface) {
	for _, worker := range p.workers {
		if forwarder, ok := worker.forwarder.(*ForwardingEngine); ok {
			forwarder.SetAddressMappingCache(cache)
		}
	}
}

// Start 启动处理器
func (p *PacketProcessor) Start() error {
	p.logger.Info("Starting packet processor",
		zap.Int("worker_count", p.workerCount),
		zap.Int("queue_size", cap(p.inputQueue)))

	// 启动工作协程
	for _, worker := range p.workers {
		p.wg.Add(1)
		go worker.run(&p.wg)
	}

	// 启动指标收集协程
	p.wg.Add(1)
	go p.metricsCollector(&p.wg)

	return nil
}

// Stop 停止处理器
func (p *PacketProcessor) Stop() error {
	p.logger.Info("Stopping packet processor")

	// 取消上下文
	p.cancel()

	// 关闭输入队列
	close(p.inputQueue)

	// 等待所有工作协程完成
	p.wg.Wait()

	// 关闭输出队列
	close(p.outputQueue)

	p.logger.Info("Packet processor stopped")
	return nil
}

// Submit 提交数据包处理任务
func (p *PacketProcessor) Submit(job *PacketJob) error {
	// 速率限制检查
	if !p.rateLimiter.Allow() {
		return fmt.Errorf("rate limit exceeded")
	}

	// 更新指标
	atomic.AddInt64(&p.metrics.JobsQueued, 1)
	atomic.StoreInt64(&p.metrics.QueueDepth, int64(len(p.inputQueue)))

	select {
	case p.inputQueue <- job:
		p.logger.Debug("Job submitted",
			zap.String("job_id", job.ID),
			zap.String("client_id", job.ClientID),
			zap.Int("queue_depth", len(p.inputQueue)))
		return nil
	case <-p.ctx.Done():
		return fmt.Errorf("processor is shutting down")
	default:
		atomic.AddInt64(&p.metrics.JobsFailed, 1)
		return fmt.Errorf("queue is full")
	}
}

// GetResult 获取处理结果
func (p *PacketProcessor) GetResult() (*PacketResult, bool) {
	select {
	case result := <-p.outputQueue:
		return result, true
	default:
		return nil, false
	}
}

// GetMetrics 获取处理器指标
func (p *PacketProcessor) GetMetrics() ProcessorMetrics {
	p.metrics.mu.RLock()
	defer p.metrics.mu.RUnlock()

	metrics := *p.metrics
	metrics.QueueDepth = int64(len(p.inputQueue))

	// 计算忙碌的工作协程数量
	var busyWorkers int64
	for _, worker := range p.workers {
		if atomic.LoadInt64(&worker.processed) > 0 {
			busyWorkers++
		}
	}
	metrics.WorkersBusy = busyWorkers

	return metrics
}

// run 工作协程主循环
func (w *Worker) run(wg *sync.WaitGroup) {
	defer wg.Done()

	w.logger.Debug("Worker started")

	for {
		select {
		case job, ok := <-w.jobs:
			if !ok {
				w.logger.Debug("Worker stopping - input channel closed")
				return
			}

			w.processJob(job)

		case <-w.ctx.Done():
			w.logger.Debug("Worker stopping - context cancelled")
			return
		}
	}
}

// processJob 处理单个任务
func (w *Worker) processJob(job *PacketJob) {
	startTime := time.Now()
	atomic.AddInt64(&w.processed, 1)

	w.logger.Debug("Processing job",
		zap.String("job_id", job.ID),
		zap.String("client_id", job.ClientID),
		zap.Int("packet_size", len(job.Packet)))

	// 处理数据包
	response, err := w.forwarder.ForwardPacket(job.Packet, job.ClientID)

	latency := time.Since(startTime)

	result := &PacketResult{
		ID:          job.ID,
		ClientID:    job.ClientID,
		Response:    response,
		Error:       err,
		Latency:     latency,
		Timestamp:   time.Now(),
		ProcessedBy: w.id,
	}

	if err != nil {
		atomic.AddInt64(&w.errors, 1)
		w.logger.Error("Job processing failed",
			zap.String("job_id", job.ID),
			zap.Error(err),
			zap.Duration("latency", latency))
	} else {
		w.logger.Debug("Job processed successfully",
			zap.String("job_id", job.ID),
			zap.Duration("latency", latency),
			zap.Int("response_size", len(response)))
	}

	// 发送结果
	select {
	case w.results <- result:
		// 成功发送结果
	case <-w.ctx.Done():
		// 上下文已取消
		return
	default:
		// 结果队列已满，记录警告
		w.logger.Warn("Result queue full, dropping result",
			zap.String("job_id", job.ID))
	}

	// 调用回调函数
	if job.Callback != nil {
		go job.Callback(result)
	}
}

// metricsCollector 指标收集协程
func (p *PacketProcessor) metricsCollector(wg *sync.WaitGroup) {
	defer wg.Done()

	ticker := time.NewTicker(time.Second * 10)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			p.collectMetrics()
		case <-p.ctx.Done():
			return
		}
	}
}

// collectMetrics 收集指标
func (p *PacketProcessor) collectMetrics() {
	var totalProcessed, totalErrors int64
	var totalLatency int64

	for _, worker := range p.workers {
		processed := atomic.LoadInt64(&worker.processed)
		errors := atomic.LoadInt64(&worker.errors)

		totalProcessed += processed
		totalErrors += errors
	}

	p.metrics.mu.Lock()
	p.metrics.JobsProcessed = totalProcessed
	p.metrics.JobsFailed = totalErrors
	p.metrics.JobsCompleted = totalProcessed - totalErrors

	if totalProcessed > 0 {
		p.metrics.AverageLatency = totalLatency / totalProcessed
	}
	p.metrics.mu.Unlock()

	p.logger.Debug("Metrics collected",
		zap.Int64("processed", totalProcessed),
		zap.Int64("errors", totalErrors),
		zap.Int64("queue_depth", int64(len(p.inputQueue))))
}

// NewRateLimiter 创建速率限制器
func NewRateLimiter(capacity int, refill time.Duration, logger *zap.Logger) *RateLimiter {
	rl := &RateLimiter{
		tokens:   make(chan struct{}, capacity),
		refill:   refill,
		capacity: capacity,
		logger:   logger,
	}

	// 初始填充令牌
	for i := 0; i < capacity; i++ {
		rl.tokens <- struct{}{}
	}

	// 启动令牌补充协程
	go rl.refillTokens()

	return rl
}

// Allow 检查是否允许请求
func (rl *RateLimiter) Allow() bool {
	select {
	case <-rl.tokens:
		return true
	default:
		return false
	}
}

// refillTokens 补充令牌
func (rl *RateLimiter) refillTokens() {
	ticker := time.NewTicker(rl.refill / time.Duration(rl.capacity))
	defer ticker.Stop()

	for range ticker.C {
		select {
		case rl.tokens <- struct{}{}:
			// 成功添加令牌
		default:
			// 令牌桶已满
		}
	}
}
