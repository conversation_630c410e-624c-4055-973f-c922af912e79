package forwarding

import (
	"fmt"
	"net"
	"sync"
	"time"

	"go.uber.org/zap"
)

// ConnectionPool 高性能连接池管理器
type ConnectionPool struct {
	pools       map[string]*ConnPool
	mu          sync.RWMutex
	maxIdle     int
	maxActive   int
	idleTimeout time.Duration
	dialTimeout time.Duration
	logger      *zap.Logger
	metrics     *PoolMetrics
}

// ConnPool 单个目标地址的连接池
type ConnPool struct {
	address     string
	idle        []*PooledConn
	active      int
	mu          sync.Mutex
	lastUsed    time.Time
	dialTimeout time.Duration
	logger      *zap.Logger
	pool        *ConnectionPool // 引用父连接池以更新指标
}

// PooledConn 池化连接
type PooledConn struct {
	net.Conn
	pool     *ConnPool
	created  time.Time
	lastUsed time.Time
	inUse    bool
	mu       sync.Mutex
}

// PoolMetrics 连接池指标
type PoolMetrics struct {
	TotalConnections   int64
	ActiveConnections  int64
	IdleConnections    int64
	ConnectionHits     int64
	ConnectionMisses   int64
	ConnectionErrors   int64
	ConnectionTimeouts int64
	mu                 sync.RWMutex
}

// NewConnectionPool 创建新的连接池
func NewConnectionPool(maxIdle, maxActive int, idleTimeout, dialTimeout time.Duration, logger *zap.Logger) *ConnectionPool {
	pool := &ConnectionPool{
		pools:       make(map[string]*ConnPool),
		maxIdle:     maxIdle,
		maxActive:   maxActive,
		idleTimeout: idleTimeout,
		dialTimeout: dialTimeout,
		logger:      logger,
		metrics:     &PoolMetrics{},
	}

	// 启动清理协程
	go pool.cleanupRoutine()

	return pool
}

// Get 从连接池获取连接
func (p *ConnectionPool) Get(address string) (*PooledConn, error) {
	p.mu.RLock()
	connPool, exists := p.pools[address]
	p.mu.RUnlock()

	if !exists {
		p.mu.Lock()
		// 双重检查
		if connPool, exists = p.pools[address]; !exists {
			connPool = &ConnPool{
				address:     address,
				idle:        make([]*PooledConn, 0, p.maxIdle),
				dialTimeout: p.dialTimeout,
				logger:      p.logger,
				pool:        p,
			}
			p.pools[address] = connPool
		}
		p.mu.Unlock()
	}

	return connPool.get()
}

// Put 将连接归还到池中
func (p *ConnectionPool) Put(conn *PooledConn) error {
	if conn == nil || conn.pool == nil {
		return fmt.Errorf("invalid connection")
	}

	return conn.pool.put(conn)
}

// Close 关闭连接池
func (p *ConnectionPool) Close() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	var lastErr error
	for address, pool := range p.pools {
		if err := pool.close(); err != nil {
			p.logger.Error("Failed to close connection pool",
				zap.String("address", address),
				zap.Error(err))
			lastErr = err
		}
	}

	p.pools = make(map[string]*ConnPool)
	return lastErr
}

// GetMetrics 获取连接池指标
func (p *ConnectionPool) GetMetrics() PoolMetrics {
	p.metrics.mu.RLock()
	defer p.metrics.mu.RUnlock()

	// 创建副本避免锁复制
	metrics := PoolMetrics{
		TotalConnections:   p.metrics.TotalConnections,
		ActiveConnections:  p.metrics.ActiveConnections,
		IdleConnections:    p.metrics.IdleConnections,
		ConnectionHits:     p.metrics.ConnectionHits,
		ConnectionMisses:   p.metrics.ConnectionMisses,
		ConnectionErrors:   p.metrics.ConnectionErrors,
		ConnectionTimeouts: p.metrics.ConnectionTimeouts,
	}
	return metrics
}

// updateMetrics 更新连接池指标
func (cp *ConnPool) updateMetrics(updateFunc func(*PoolMetrics)) {
	if cp.pool != nil && cp.pool.metrics != nil {
		cp.pool.metrics.mu.Lock()
		updateFunc(cp.pool.metrics)
		cp.pool.metrics.mu.Unlock()
	}
}

// get 从单个连接池获取连接
func (cp *ConnPool) get() (*PooledConn, error) {
	cp.mu.Lock()
	defer cp.mu.Unlock()

	cp.lastUsed = time.Now()

	// 尝试从空闲连接中获取
	for len(cp.idle) > 0 {
		conn := cp.idle[len(cp.idle)-1]
		cp.idle = cp.idle[:len(cp.idle)-1]

		// 检查连接是否仍然有效
		if cp.isConnValid(conn) {
			conn.mu.Lock()
			conn.inUse = true
			conn.lastUsed = time.Now()
			conn.mu.Unlock()

			cp.active++

			// 更新指标
			cp.updateMetrics(func(m *PoolMetrics) {
				m.ConnectionHits++
				m.ActiveConnections = int64(cp.active)
				m.IdleConnections = int64(len(cp.idle))
			})

			cp.logger.Debug("Reused connection from pool",
				zap.String("address", cp.address),
				zap.Int("active", cp.active),
				zap.Int("idle", len(cp.idle)))

			return conn, nil
		} else {
			// 连接无效，关闭它
			conn.Conn.Close()

			// 更新指标
			cp.updateMetrics(func(m *PoolMetrics) {
				m.ConnectionErrors++
			})
		}
	}

	// 创建新连接
	rawConn, err := net.DialTimeout("tcp", cp.address, cp.dialTimeout)
	if err != nil {
		// 更新指标
		cp.updateMetrics(func(m *PoolMetrics) {
			m.ConnectionErrors++
			m.ConnectionMisses++
		})

		cp.logger.Error("Failed to create new connection",
			zap.String("address", cp.address),
			zap.Error(err))
		return nil, fmt.Errorf("failed to dial %s: %w", cp.address, err)
	}

	conn := &PooledConn{
		Conn:     rawConn,
		pool:     cp,
		created:  time.Now(),
		lastUsed: time.Now(),
		inUse:    true,
	}

	cp.active++

	// 更新指标
	cp.updateMetrics(func(m *PoolMetrics) {
		m.TotalConnections++
		m.ActiveConnections = int64(cp.active)
		m.IdleConnections = int64(len(cp.idle))
		m.ConnectionMisses++
	})

	cp.logger.Debug("Created new connection",
		zap.String("address", cp.address),
		zap.Int("active", cp.active),
		zap.Int("idle", len(cp.idle)))

	return conn, nil
}

// put 将连接归还到池中
func (cp *ConnPool) put(conn *PooledConn) error {
	if conn == nil {
		return fmt.Errorf("connection is nil")
	}

	cp.mu.Lock()
	defer cp.mu.Unlock()

	conn.mu.Lock()
	if !conn.inUse {
		conn.mu.Unlock()
		return fmt.Errorf("connection not in use")
	}
	conn.inUse = false
	conn.lastUsed = time.Now()
	conn.mu.Unlock()

	cp.active--

	// 检查连接是否仍然有效
	if !cp.isConnValid(conn) {
		conn.Conn.Close()
		cp.logger.Debug("Closed invalid connection",
			zap.String("address", cp.address))
		return nil
	}

	// 检查是否超过最大空闲连接数
	if len(cp.idle) >= cap(cp.idle) {
		conn.Conn.Close()
		cp.logger.Debug("Closed connection due to pool full",
			zap.String("address", cp.address),
			zap.Int("idle", len(cp.idle)))
		return nil
	}

	// 归还到空闲池
	cp.idle = append(cp.idle, conn)
	cp.logger.Debug("Returned connection to pool",
		zap.String("address", cp.address),
		zap.Int("active", cp.active),
		zap.Int("idle", len(cp.idle)))

	return nil
}

// isConnValid 检查连接是否有效
func (cp *ConnPool) isConnValid(conn *PooledConn) bool {
	if conn == nil || conn.Conn == nil {
		return false
	}

	// 检查连接是否超时
	if time.Since(conn.lastUsed) > time.Minute*5 {
		return false
	}

	// 检查连接是否过老
	if time.Since(conn.created) > time.Hour*24 {
		return false
	}

	// 对于TCP连接，使用更轻量级的检查方法
	if tcpConn, ok := conn.Conn.(*net.TCPConn); ok {
		// 尝试设置KeepAlive来检测连接状态
		if err := tcpConn.SetKeepAlive(true); err != nil {
			return false
		}
		if err := tcpConn.SetKeepAlivePeriod(time.Second * 30); err != nil {
			return false
		}
		return true
	}

	// 对于其他类型的连接，使用原来的方法
	// 尝试设置读取超时来测试连接
	conn.Conn.SetReadDeadline(time.Now().Add(time.Millisecond))
	one := make([]byte, 1)
	_, err := conn.Conn.Read(one)
	conn.Conn.SetReadDeadline(time.Time{}) // 清除超时

	// 如果是超时错误，说明连接是活跃的
	if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
		return true
	}

	// 其他错误说明连接有问题
	return err == nil
}

// close 关闭连接池
func (cp *ConnPool) close() error {
	cp.mu.Lock()
	defer cp.mu.Unlock()

	var lastErr error
	for _, conn := range cp.idle {
		if err := conn.Conn.Close(); err != nil {
			lastErr = err
		}
	}

	cp.idle = cp.idle[:0]
	cp.active = 0

	return lastErr
}

// cleanupRoutine 定期清理过期连接
func (p *ConnectionPool) cleanupRoutine() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		p.cleanup()
	}
}

// cleanup 清理过期连接
func (p *ConnectionPool) cleanup() {
	p.mu.RLock()
	pools := make([]*ConnPool, 0, len(p.pools))
	for _, pool := range p.pools {
		pools = append(pools, pool)
	}
	p.mu.RUnlock()

	for _, pool := range pools {
		pool.cleanupIdle()
	}
}

// cleanupIdle 清理空闲连接
func (cp *ConnPool) cleanupIdle() {
	cp.mu.Lock()
	defer cp.mu.Unlock()

	now := time.Now()
	validConns := make([]*PooledConn, 0, len(cp.idle))

	for _, conn := range cp.idle {
		if now.Sub(conn.lastUsed) > time.Minute*5 || !cp.isConnValid(conn) {
			conn.Conn.Close()
			cp.logger.Debug("Cleaned up expired connection",
				zap.String("address", cp.address))
		} else {
			validConns = append(validConns, conn)
		}
	}

	cp.idle = validConns
}

// Close 关闭池化连接
func (pc *PooledConn) Close() error {
	pc.mu.Lock()
	defer pc.mu.Unlock()

	if !pc.inUse {
		return fmt.Errorf("connection not in use")
	}

	// 归还到池中而不是直接关闭
	return pc.pool.put(pc)
}

// ForceClose 强制关闭连接
func (pc *PooledConn) ForceClose() error {
	pc.mu.Lock()
	defer pc.mu.Unlock()

	pc.inUse = false
	return pc.Conn.Close()
}
