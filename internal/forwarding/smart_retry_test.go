package forwarding

import (
	"context"
	"errors"
	"net"
	"testing"
	"time"

	"go.uber.org/zap"
)

func TestErrorClassifier(t *testing.T) {
	logger := zap.NewNop()
	classifier := NewErrorClassifier(logger)

	tests := []struct {
		name     string
		err      error
		expected ErrorType
	}{
		{
			name:     "timeout error",
			err:      &net.OpError{Op: "dial", Err: &timeoutError{}},
			expected: ErrorTypeTimeout,
		},
		{
			name:     "permission denied",
			err:      errors.New("socket: operation not permitted"),
			expected: ErrorTypePermission,
		},
		{
			name:     "connection refused",
			err:      errors.New("connection refused"),
			expected: ErrorTypeNetwork,
		},
		{
			name:     "rate limit",
			err:      errors.New("rate limit exceeded"),
			expected: ErrorTypeRateLimit,
		},
		{
			name:     "invalid argument",
			err:      errors.New("invalid argument"),
			expected: ErrorTypePermanent,
		},
		{
			name:     "unknown error",
			err:      errors.New("some unknown error"),
			expected: ErrorTypeTemporary,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := classifier.ClassifyError(tt.err)
			if result != tt.expected {
				t.Errorf("ClassifyError() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestSmartRetryPolicy(t *testing.T) {
	logger := zap.NewNop()
	policy := NewSmartRetryPolicy(logger)

	t.Run("success on first try", func(t *testing.T) {
		attempts := 0
		err := policy.Execute(context.Background(), func() error {
			attempts++
			return nil
		})

		if err != nil {
			t.Errorf("Expected no error, got %v", err)
		}
		if attempts != 1 {
			t.Errorf("Expected 1 attempt, got %d", attempts)
		}
	})

	t.Run("success after retries", func(t *testing.T) {
		attempts := 0
		err := policy.Execute(context.Background(), func() error {
			attempts++
			if attempts < 3 {
				return &net.OpError{Op: "dial", Err: &timeoutError{}}
			}
			return nil
		})

		if err != nil {
			t.Errorf("Expected no error, got %v", err)
		}
		if attempts != 3 {
			t.Errorf("Expected 3 attempts, got %d", attempts)
		}
	})

	t.Run("permanent error no retry", func(t *testing.T) {
		attempts := 0
		err := policy.Execute(context.Background(), func() error {
			attempts++
			return errors.New("invalid argument")
		})

		if err == nil {
			t.Error("Expected error, got nil")
		}
		if attempts != 1 {
			t.Errorf("Expected 1 attempt, got %d", attempts)
		}
	})

	t.Run("max retries exceeded", func(t *testing.T) {
		attempts := 0
		err := policy.Execute(context.Background(), func() error {
			attempts++
			return &net.OpError{Op: "dial", Err: &timeoutError{}}
		})

		if err == nil {
			t.Error("Expected error, got nil")
		}
		if attempts != policy.MaxRetries+1 {
			t.Errorf("Expected %d attempts, got %d", policy.MaxRetries+1, attempts)
		}
	})

	t.Run("context cancellation", func(t *testing.T) {
		ctx, cancel := context.WithCancel(context.Background())
		cancel() // Cancel immediately

		attempts := 0
		err := policy.Execute(ctx, func() error {
			attempts++
			return &net.OpError{Op: "dial", Err: &timeoutError{}}
		})

		if err != context.Canceled {
			t.Errorf("Expected context.Canceled, got %v", err)
		}
		if attempts > 1 {
			t.Errorf("Expected at most 1 attempt, got %d", attempts)
		}
	})
}

func TestSmartCircuitBreaker(t *testing.T) {
	logger := zap.NewNop()
	cb := NewSmartCircuitBreaker(3, time.Millisecond*100, logger)

	t.Run("initial state is closed", func(t *testing.T) {
		if !cb.Allow() {
			t.Error("Circuit breaker should allow requests initially")
		}
	})

	t.Run("opens after max failures", func(t *testing.T) {
		// Record failures to open the circuit
		for i := 0; i < 3; i++ {
			cb.Allow()
			cb.RecordFailure(errors.New("test error"))
		}

		if cb.Allow() {
			t.Error("Circuit breaker should be open after max failures")
		}
	})

	t.Run("transitions to half-open after timeout", func(t *testing.T) {
		// Wait for timeout
		time.Sleep(time.Millisecond * 150)

		if !cb.Allow() {
			t.Error("Circuit breaker should allow requests in half-open state")
		}
	})

	t.Run("closes after successful half-open test", func(t *testing.T) {
		// Reset circuit breaker
		cb = NewSmartCircuitBreaker(3, time.Millisecond*100, logger)

		// Open the circuit
		for i := 0; i < 3; i++ {
			cb.Allow()
			cb.RecordFailure(errors.New("test error"))
		}

		// Wait for timeout to enter half-open
		time.Sleep(time.Millisecond * 150)

		// Perform successful half-open tests
		for i := 0; i < 5; i++ {
			if cb.Allow() {
				cb.RecordSuccess()
			}
		}

		// Should be closed now
		if !cb.Allow() {
			t.Error("Circuit breaker should be closed after successful half-open test")
		}
	})

	t.Run("different error types", func(t *testing.T) {
		cb = NewSmartCircuitBreaker(4, time.Millisecond*100, logger)

		// Permission errors shouldn't trip the circuit as easily
		for i := 0; i < 3; i++ {
			cb.Allow()
			cb.RecordFailure(errors.New("permission denied"))
		}

		if !cb.Allow() {
			t.Error("Circuit breaker should not open for permission errors")
		}

		// Network errors should trip more easily
		for i := 0; i < 2; i++ {
			cb.Allow()
			cb.RecordFailure(errors.New("connection refused"))
		}

		if cb.Allow() {
			t.Error("Circuit breaker should open for network errors")
		}
	})
}

func TestCalculateDelay(t *testing.T) {
	logger := zap.NewNop()
	policy := NewSmartRetryPolicy(logger)
	policy.BaseDelay = time.Millisecond * 100
	policy.BackoffFactor = 2.0
	policy.MaxDelay = time.Second * 5
	policy.Jitter = false

	tests := []struct {
		attempt  int
		expected time.Duration
	}{
		{0, time.Millisecond * 100},
		{1, time.Millisecond * 200},
		{2, time.Millisecond * 400},
		{3, time.Millisecond * 800},
		{10, time.Second * 5}, // Should be capped at MaxDelay
	}

	for _, tt := range tests {
		t.Run("", func(t *testing.T) {
			delay := policy.calculateDelay(tt.attempt)
			if delay != tt.expected {
				t.Errorf("calculateDelay(%d) = %v, want %v", tt.attempt, delay, tt.expected)
			}
		})
	}
}

func TestCircuitBreakerMetrics(t *testing.T) {
	logger := zap.NewNop()
	cb := NewSmartCircuitBreaker(3, time.Millisecond*100, logger)

	// Record some operations
	cb.Allow()
	cb.RecordSuccess()

	cb.Allow()
	cb.RecordFailure(errors.New("test error"))

	metrics := cb.GetMetrics()

	if metrics.TotalRequests != 2 {
		t.Errorf("Expected 2 total requests, got %d", metrics.TotalRequests)
	}
	if metrics.SuccessfulCalls != 1 {
		t.Errorf("Expected 1 successful call, got %d", metrics.SuccessfulCalls)
	}
	if metrics.FailedCalls != 1 {
		t.Errorf("Expected 1 failed call, got %d", metrics.FailedCalls)
	}
}

// timeoutError implements net.Error with Timeout() returning true
type timeoutError struct{}

func (e *timeoutError) Error() string   { return "timeout" }
func (e *timeoutError) Timeout() bool   { return true }
func (e *timeoutError) Temporary() bool { return true }

// Benchmark tests
func BenchmarkErrorClassifier(b *testing.B) {
	logger := zap.NewNop()
	classifier := NewErrorClassifier(logger)
	err := errors.New("connection refused")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		classifier.ClassifyError(err)
	}
}

func BenchmarkCircuitBreakerAllow(b *testing.B) {
	logger := zap.NewNop()
	cb := NewSmartCircuitBreaker(10, time.Minute, logger)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		cb.Allow()
	}
}

func BenchmarkRetryPolicyCalculateDelay(b *testing.B) {
	logger := zap.NewNop()
	policy := NewSmartRetryPolicy(logger)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		policy.calculateDelay(i % 10)
	}
}
