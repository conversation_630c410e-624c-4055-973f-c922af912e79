package forwarding

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// OptimizedForwardingHandler 优化的转发处理器
type OptimizedForwardingHandler struct {
	processor      *PacketProcessor
	connPool       *ConnectionPool
	logger         *zap.Logger
	ctx            context.Context
	cancel         context.CancelFunc
	wg             sync.WaitGroup
	metrics        *HandlerMetrics
	config         *HandlerConfig
	addressMapping AddressMappingCacheInterface // 新增：地址映射缓存
}

// HandlerConfig 处理器配置
type HandlerConfig struct {
	// 工作协程数量
	WorkerCount int
	// 队列大小
	QueueSize int
	// 连接池配置
	MaxIdleConns   int
	MaxActiveConns int
	IdleTimeout    time.Duration
	DialTimeout    time.Duration
	// 性能配置
	EnableMetrics   bool
	MetricsInterval time.Duration
	// 缓存配置
	EnableCache bool
	CacheSize   int
	CacheTTL    time.Duration
}

// HandlerMetrics 处理器指标
type HandlerMetrics struct {
	TotalRequests             int64
	SuccessfulRequests        int64
	FailedRequests            int64
	AverageLatency            time.Duration
	ThroughputPerSec          float64
	QueueUtilization          float64
	ConnectionPoolUtilization float64
	CacheHitRate              float64
	ErrorRate                 float64
	mu                        sync.RWMutex
}

// DefaultHandlerConfig 返回默认配置
func DefaultHandlerConfig() *HandlerConfig {
	return &HandlerConfig{
		WorkerCount:     10,
		QueueSize:       1000,
		MaxIdleConns:    50,
		MaxActiveConns:  200,
		IdleTimeout:     time.Minute * 5,
		DialTimeout:     time.Second * 10,
		EnableMetrics:   true,
		MetricsInterval: time.Second * 30,
		EnableCache:     true,
		CacheSize:       1000,
		CacheTTL:        time.Minute * 5,
	}
}

// NewOptimizedForwardingHandler 创建优化的转发处理器
func NewOptimizedForwardingHandler(config *HandlerConfig, logger *zap.Logger) (*OptimizedForwardingHandler, error) {
	if config == nil {
		config = DefaultHandlerConfig()
	}

	ctx, cancel := context.WithCancel(context.Background())

	// 创建连接池
	connPool := NewConnectionPool(
		config.MaxIdleConns,
		config.MaxActiveConns,
		config.IdleTimeout,
		config.DialTimeout,
		logger,
	)

	// 创建数据包处理器
	processor := NewPacketProcessor(
		config.WorkerCount,
		config.QueueSize,
		connPool,
		logger,
	)

	handler := &OptimizedForwardingHandler{
		processor: processor,
		connPool:  connPool,
		logger:    logger,
		ctx:       ctx,
		cancel:    cancel,
		metrics:   &HandlerMetrics{},
		config:    config,
	}

	return handler, nil
}

// Start 启动处理器
func (h *OptimizedForwardingHandler) Start() error {
	h.logger.Info("Starting optimized forwarding handler",
		zap.Int("worker_count", h.config.WorkerCount),
		zap.Int("queue_size", h.config.QueueSize),
		zap.Int("max_idle_conns", h.config.MaxIdleConns),
		zap.Int("max_active_conns", h.config.MaxActiveConns))

	// 启动数据包处理器
	if err := h.processor.Start(); err != nil {
		return fmt.Errorf("failed to start packet processor: %w", err)
	}

	// 启动结果处理协程
	h.wg.Add(1)
	go h.resultHandler()

	// 启动指标收集协程
	if h.config.EnableMetrics {
		h.wg.Add(1)
		go h.metricsCollector()
	}

	h.logger.Info("Optimized forwarding handler started successfully")
	return nil
}

// Stop 停止处理器
func (h *OptimizedForwardingHandler) Stop() error {
	h.logger.Info("Stopping optimized forwarding handler")

	// 取消上下文
	h.cancel()

	// 停止数据包处理器
	if err := h.processor.Stop(); err != nil {
		h.logger.Error("Failed to stop packet processor", zap.Error(err))
	}

	// 关闭连接池
	if err := h.connPool.Close(); err != nil {
		h.logger.Error("Failed to close connection pool", zap.Error(err))
	}

	// 等待所有协程完成
	h.wg.Wait()

	h.logger.Info("Optimized forwarding handler stopped")
	return nil
}

// SetAddressMappingCache 设置地址映射缓存
func (h *OptimizedForwardingHandler) SetAddressMappingCache(cache AddressMappingCacheInterface) {
	h.addressMapping = cache

	// 传递给PacketProcessor
	if h.processor != nil {
		h.processor.SetAddressMappingCache(cache)
	}

	h.logger.Info("Address mapping cache set for optimized forwarding handler")
}

// HandlePacket 处理数据包（异步）
func (h *OptimizedForwardingHandler) HandlePacket(packet []byte, clientID string, callback func([]byte, error)) error {
	startTime := time.Now()

	// 更新请求指标
	h.updateRequestMetrics()

	// 创建处理任务
	job := &PacketJob{
		ID:        fmt.Sprintf("pkt-%d", time.Now().UnixNano()),
		Packet:    packet,
		ClientID:  clientID,
		Timestamp: startTime,
		Context:   h.ctx,
		Callback: func(result *PacketResult) {
			// 更新完成指标
			h.updateCompletionMetrics(result.Error == nil, time.Since(startTime))

			// 调用用户回调
			if callback != nil {
				callback(result.Response, result.Error)
			}
		},
	}

	// 提交任务到处理器
	if err := h.processor.Submit(job); err != nil {
		h.updateCompletionMetrics(false, time.Since(startTime))
		return fmt.Errorf("failed to submit packet job: %w", err)
	}

	return nil
}

// HandlePacketSync 处理数据包（同步）
func (h *OptimizedForwardingHandler) HandlePacketSync(packet []byte, clientID string, timeout time.Duration) ([]byte, error) {
	startTime := time.Now()

	// 更新请求指标
	h.updateRequestMetrics()

	// 创建结果通道
	resultChan := make(chan *PacketResult, 1)

	// 创建处理任务
	job := &PacketJob{
		ID:        fmt.Sprintf("sync-pkt-%d", time.Now().UnixNano()),
		Packet:    packet,
		ClientID:  clientID,
		Timestamp: startTime,
		Context:   h.ctx,
		Callback: func(result *PacketResult) {
			select {
			case resultChan <- result:
			default:
				// 通道已满或已关闭
			}
		},
	}

	// 提交任务
	if err := h.processor.Submit(job); err != nil {
		h.updateCompletionMetrics(false, time.Since(startTime))
		return nil, fmt.Errorf("failed to submit packet job: %w", err)
	}

	// 等待结果
	select {
	case result := <-resultChan:
		h.updateCompletionMetrics(result.Error == nil, time.Since(startTime))
		return result.Response, result.Error
	case <-time.After(timeout):
		h.updateCompletionMetrics(false, time.Since(startTime))
		return nil, fmt.Errorf("packet processing timeout")
	case <-h.ctx.Done():
		h.updateCompletionMetrics(false, time.Since(startTime))
		return nil, fmt.Errorf("handler is shutting down")
	}
}

// GetMetrics 获取处理器指标
func (h *OptimizedForwardingHandler) GetMetrics() HandlerMetrics {
	h.metrics.mu.RLock()
	defer h.metrics.mu.RUnlock()

	metrics := *h.metrics

	// 获取处理器指标
	procMetrics := h.processor.GetMetrics()
	metrics.QueueUtilization = float64(procMetrics.QueueDepth) / float64(cap(h.processor.inputQueue)) * 100

	// 获取连接池指标
	poolMetrics := h.connPool.GetMetrics()
	if poolMetrics.TotalConnections > 0 {
		metrics.ConnectionPoolUtilization = float64(poolMetrics.ActiveConnections) / float64(poolMetrics.TotalConnections) * 100
	}

	// 计算错误率
	if metrics.TotalRequests > 0 {
		metrics.ErrorRate = float64(metrics.FailedRequests) / float64(metrics.TotalRequests) * 100
	}

	return metrics
}

// GetDetailedStatus 获取详细状态
func (h *OptimizedForwardingHandler) GetDetailedStatus() map[string]interface{} {
	metrics := h.GetMetrics()
	procMetrics := h.processor.GetMetrics()
	poolMetrics := h.connPool.GetMetrics()

	return map[string]interface{}{
		"handler": map[string]interface{}{
			"total_requests":      metrics.TotalRequests,
			"successful_requests": metrics.SuccessfulRequests,
			"failed_requests":     metrics.FailedRequests,
			"average_latency_ms":  metrics.AverageLatency.Milliseconds(),
			"throughput_per_sec":  metrics.ThroughputPerSec,
			"error_rate_percent":  metrics.ErrorRate,
		},
		"processor": map[string]interface{}{
			"jobs_queued":               procMetrics.JobsQueued,
			"jobs_processed":            procMetrics.JobsProcessed,
			"jobs_completed":            procMetrics.JobsCompleted,
			"jobs_failed":               procMetrics.JobsFailed,
			"queue_depth":               procMetrics.QueueDepth,
			"workers_busy":              procMetrics.WorkersBusy,
			"queue_utilization_percent": metrics.QueueUtilization,
		},
		"connection_pool": map[string]interface{}{
			"total_connections":        poolMetrics.TotalConnections,
			"active_connections":       poolMetrics.ActiveConnections,
			"idle_connections":         poolMetrics.IdleConnections,
			"connection_hits":          poolMetrics.ConnectionHits,
			"connection_misses":        poolMetrics.ConnectionMisses,
			"connection_errors":        poolMetrics.ConnectionErrors,
			"pool_utilization_percent": metrics.ConnectionPoolUtilization,
		},
		"config": map[string]interface{}{
			"worker_count":     h.config.WorkerCount,
			"queue_size":       h.config.QueueSize,
			"max_idle_conns":   h.config.MaxIdleConns,
			"max_active_conns": h.config.MaxActiveConns,
			"idle_timeout_sec": h.config.IdleTimeout.Seconds(),
			"dial_timeout_sec": h.config.DialTimeout.Seconds(),
		},
	}
}

// resultHandler 结果处理协程
func (h *OptimizedForwardingHandler) resultHandler() {
	defer h.wg.Done()

	for {
		select {
		case result, ok := <-h.processor.outputQueue:
			if !ok {
				h.logger.Debug("Result handler stopping - output queue closed")
				return
			}

			// 处理结果（这里可以添加额外的结果处理逻辑）
			h.logger.Debug("Processing result",
				zap.String("job_id", result.ID),
				zap.String("client_id", result.ClientID),
				zap.Duration("latency", result.Latency),
				zap.Bool("success", result.Error == nil))

		case <-h.ctx.Done():
			h.logger.Debug("Result handler stopping - context cancelled")
			return
		}
	}
}

// metricsCollector 指标收集协程
func (h *OptimizedForwardingHandler) metricsCollector() {
	defer h.wg.Done()

	ticker := time.NewTicker(h.config.MetricsInterval)
	defer ticker.Stop()

	var lastRequests int64
	var lastTime time.Time = time.Now()

	for {
		select {
		case <-ticker.C:
			h.collectMetrics(&lastRequests, &lastTime)
		case <-h.ctx.Done():
			return
		}
	}
}

// collectMetrics 收集指标
func (h *OptimizedForwardingHandler) collectMetrics(lastRequests *int64, lastTime *time.Time) {
	h.metrics.mu.Lock()
	defer h.metrics.mu.Unlock()

	now := time.Now()
	duration := now.Sub(*lastTime)

	if duration > 0 {
		requestsDiff := h.metrics.TotalRequests - *lastRequests
		h.metrics.ThroughputPerSec = float64(requestsDiff) / duration.Seconds()
	}

	*lastRequests = h.metrics.TotalRequests
	*lastTime = now

	h.logger.Debug("Metrics collected",
		zap.Int64("total_requests", h.metrics.TotalRequests),
		zap.Int64("successful_requests", h.metrics.SuccessfulRequests),
		zap.Int64("failed_requests", h.metrics.FailedRequests),
		zap.Float64("throughput_per_sec", h.metrics.ThroughputPerSec),
		zap.Float64("error_rate", h.metrics.ErrorRate))
}

// updateRequestMetrics 更新请求指标
func (h *OptimizedForwardingHandler) updateRequestMetrics() {
	h.metrics.mu.Lock()
	h.metrics.TotalRequests++
	h.metrics.mu.Unlock()
}

// updateCompletionMetrics 更新完成指标
func (h *OptimizedForwardingHandler) updateCompletionMetrics(success bool, latency time.Duration) {
	h.metrics.mu.Lock()
	defer h.metrics.mu.Unlock()

	if success {
		h.metrics.SuccessfulRequests++
	} else {
		h.metrics.FailedRequests++
	}

	// 更新平均延迟（简化计算）
	if h.metrics.TotalRequests > 0 {
		h.metrics.AverageLatency = (h.metrics.AverageLatency + latency) / 2
	} else {
		h.metrics.AverageLatency = latency
	}
}
