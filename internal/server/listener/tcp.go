package listener

import (
	"context"
	"crypto/tls"
	"fmt"
	"net"
	"sync"
	"sync/atomic"
	"time"

	"go.uber.org/zap"
)

// TCPListener TCP监听器实现
type TCPListener struct {
	config   *ListenerConfig
	logger   *zap.Logger
	mu       sync.RWMutex
	
	listener net.Listener
	running  bool
	metrics  *ListenerMetrics
	
	// 连接管理
	connections map[string]*TCPConnection
	connMu      sync.RWMutex
	connID      int64
}

// TCPConnection TCP连接实现
type TCPConnection struct {
	id         string
	conn       net.Conn
	logger     *zap.Logger
	mu         sync.RWMutex
	closed     bool
	metrics    *ConnectionMetrics
	lastActive time.Time
}

// NewTCPListener 创建TCP监听器
func NewTCPListener(config *ListenerConfig, logger *zap.Logger) *TCPListener {
	return &TCPListener{
		config:      config,
		logger:      logger,
		connections: make(map[string]*TCPConnection),
		metrics: &ListenerMetrics{
			StartTime: time.Now(),
		},
	}
}

// Start 启动监听器
func (l *TCPListener) Start(ctx context.Context) error {
	l.mu.Lock()
	defer l.mu.Unlock()
	
	if l.running {
		return nil
	}
	
	address := fmt.Sprintf("%s:%d", l.config.Address, l.config.Port)
	l.logger.Info("Starting TCP listener", zap.String("address", address))
	
	var listener net.Listener
	var err error
	
	if l.config.EnableTLS {
		// TLS监听器
		listener, err = l.createTLSListener(address)
	} else {
		// 普通TCP监听器
		listener, err = net.Listen("tcp", address)
	}
	
	if err != nil {
		return fmt.Errorf("failed to start TCP listener: %w", err)
	}
	
	l.listener = listener
	l.running = true
	l.metrics.StartTime = time.Now()
	
	l.logger.Info("TCP listener started successfully", zap.String("address", address))
	return nil
}

// Stop 停止监听器
func (l *TCPListener) Stop() error {
	l.mu.Lock()
	defer l.mu.Unlock()
	
	if !l.running {
		return nil
	}
	
	l.logger.Info("Stopping TCP listener")
	
	// 关闭监听器
	if l.listener != nil {
		l.listener.Close()
	}
	
	// 关闭所有连接
	l.connMu.Lock()
	for _, conn := range l.connections {
		conn.Close()
	}
	l.connections = make(map[string]*TCPConnection)
	l.connMu.Unlock()
	
	l.running = false
	l.logger.Info("TCP listener stopped")
	return nil
}

// Accept 接受连接
func (l *TCPListener) Accept() (Connection, error) {
	l.mu.RLock()
	listener := l.listener
	running := l.running
	l.mu.RUnlock()
	
	if !running || listener == nil {
		return nil, fmt.Errorf("listener not running")
	}
	
	// 检查连接数限制
	if l.config.MaxConnections > 0 {
		l.connMu.RLock()
		activeCount := len(l.connections)
		l.connMu.RUnlock()
		
		if activeCount >= l.config.MaxConnections {
			l.metrics.RejectedConnections++
			return nil, fmt.Errorf("max connections reached")
		}
	}
	
	conn, err := listener.Accept()
	if err != nil {
		l.metrics.FailedConnections++
		return nil, fmt.Errorf("failed to accept connection: %w", err)
	}
	
	// 创建连接对象
	connID := fmt.Sprintf("tcp-%d", atomic.AddInt64(&l.connID, 1))
	tcpConn := &TCPConnection{
		id:     connID,
		conn:   conn,
		logger: l.logger.With(zap.String("conn_id", connID)),
		metrics: &ConnectionMetrics{
			ID:          connID,
			RemoteAddr:  conn.RemoteAddr().String(),
			LocalAddr:   conn.LocalAddr().String(),
			ConnectTime: time.Now(),
			LastActive:  time.Now(),
		},
		lastActive: time.Now(),
	}
	
	// 设置超时
	if l.config.ReadTimeout > 0 {
		tcpConn.SetReadTimeout(l.config.ReadTimeout)
	}
	if l.config.WriteTimeout > 0 {
		tcpConn.SetWriteTimeout(l.config.WriteTimeout)
	}
	
	// 添加到连接管理
	l.connMu.Lock()
	l.connections[connID] = tcpConn
	l.connMu.Unlock()
	
	// 更新指标
	l.metrics.TotalConnections++
	l.metrics.ActiveConnections = len(l.connections)
	l.metrics.LastConnection = time.Now()
	
	l.logger.Debug("New TCP connection accepted", 
		zap.String("conn_id", connID),
		zap.String("remote_addr", conn.RemoteAddr().String()))
	
	return tcpConn, nil
}

// GetMetrics 获取监听器指标
func (l *TCPListener) GetMetrics() *ListenerMetrics {
	l.mu.RLock()
	defer l.mu.RUnlock()
	
	// 复制指标
	metrics := *l.metrics
	
	l.connMu.RLock()
	metrics.ActiveConnections = len(l.connections)
	l.connMu.RUnlock()
	
	return &metrics
}

// GetProtocol 获取协议类型
func (l *TCPListener) GetProtocol() string {
	return "tcp"
}

// GetAddress 获取监听地址
func (l *TCPListener) GetAddress() string {
	return fmt.Sprintf("%s:%d", l.config.Address, l.config.Port)
}

// createTLSListener 创建TLS监听器
func (l *TCPListener) createTLSListener(address string) (net.Listener, error) {
	cert, err := tls.LoadX509KeyPair(l.config.TLSCertFile, l.config.TLSKeyFile)
	if err != nil {
		return nil, fmt.Errorf("failed to load TLS certificate: %w", err)
	}
	
	tlsConfig := &tls.Config{
		Certificates: []tls.Certificate{cert},
	}
	
	return tls.Listen("tcp", address, tlsConfig)
}

// removeConnection 移除连接
func (l *TCPListener) removeConnection(connID string) {
	l.connMu.Lock()
	delete(l.connections, connID)
	l.connMu.Unlock()
	
	l.metrics.ActiveConnections = len(l.connections)
}

// TCPConnection 方法实现

// GetID 获取连接ID
func (c *TCPConnection) GetID() string {
	return c.id
}

// GetRemoteAddr 获取远程地址
func (c *TCPConnection) GetRemoteAddr() net.Addr {
	return c.conn.RemoteAddr()
}

// GetLocalAddr 获取本地地址
func (c *TCPConnection) GetLocalAddr() net.Addr {
	return c.conn.LocalAddr()
}

// Read 读取数据
func (c *TCPConnection) Read(buffer []byte) (int, error) {
	c.mu.RLock()
	conn := c.conn
	closed := c.closed
	c.mu.RUnlock()
	
	if closed {
		return 0, fmt.Errorf("connection closed")
	}
	
	n, err := conn.Read(buffer)
	if err != nil {
		c.metrics.ReadErrors++
		return n, err
	}
	
	c.mu.Lock()
	c.metrics.BytesReceived += int64(n)
	c.metrics.PacketsRecv++
	c.lastActive = time.Now()
	c.metrics.LastActive = c.lastActive
	c.mu.Unlock()
	
	return n, nil
}

// Write 写入数据
func (c *TCPConnection) Write(data []byte) (int, error) {
	c.mu.RLock()
	conn := c.conn
	closed := c.closed
	c.mu.RUnlock()
	
	if closed {
		return 0, fmt.Errorf("connection closed")
	}
	
	n, err := conn.Write(data)
	if err != nil {
		c.metrics.WriteErrors++
		return n, err
	}
	
	c.mu.Lock()
	c.metrics.BytesSent += int64(n)
	c.metrics.PacketsSent++
	c.lastActive = time.Now()
	c.metrics.LastActive = c.lastActive
	c.mu.Unlock()
	
	return n, nil
}

// Close 关闭连接
func (c *TCPConnection) Close() error {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	if c.closed {
		return nil
	}
	
	err := c.conn.Close()
	c.closed = true
	
	c.logger.Debug("TCP connection closed", zap.String("conn_id", c.id))
	return err
}

// IsClosed 检查是否已关闭
func (c *TCPConnection) IsClosed() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.closed
}

// GetMetrics 获取连接指标
func (c *TCPConnection) GetMetrics() *ConnectionMetrics {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	// 复制指标
	metrics := *c.metrics
	return &metrics
}

// SetReadTimeout 设置读超时
func (c *TCPConnection) SetReadTimeout(timeout time.Duration) error {
	if timeout > 0 {
		return c.conn.SetReadDeadline(time.Now().Add(timeout))
	}
	return nil
}

// SetWriteTimeout 设置写超时
func (c *TCPConnection) SetWriteTimeout(timeout time.Duration) error {
	if timeout > 0 {
		return c.conn.SetWriteDeadline(time.Now().Add(timeout))
	}
	return nil
}
