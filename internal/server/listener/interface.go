package listener

import (
	"context"
	"net"
	"time"
)

// Listener 监听器接口
type Listener interface {
	// Start 启动监听器
	Start(ctx context.Context) error
	
	// Stop 停止监听器
	Stop() error
	
	// Accept 接受连接
	Accept() (Connection, error)
	
	// GetMetrics 获取监听器指标
	GetMetrics() *ListenerMetrics
	
	// GetProtocol 获取协议类型
	GetProtocol() string
	
	// GetAddress 获取监听地址
	GetAddress() string
}

// Connection 连接接口
type Connection interface {
	// GetID 获取连接ID
	GetID() string
	
	// GetRemoteAddr 获取远程地址
	GetRemoteAddr() net.Addr
	
	// GetLocalAddr 获取本地地址
	GetLocalAddr() net.Addr
	
	// Read 读取数据
	Read(buffer []byte) (int, error)
	
	// Write 写入数据
	Write(data []byte) (int, error)
	
	// Close 关闭连接
	Close() error
	
	// IsClosed 检查是否已关闭
	IsClosed() bool
	
	// GetMetrics 获取连接指标
	GetMetrics() *ConnectionMetrics
	
	// SetReadTimeout 设置读超时
	SetReadTimeout(timeout time.Duration) error
	
	// SetWriteTimeout 设置写超时
	SetWriteTimeout(timeout time.Duration) error
}

// ListenerConfig 监听器配置
type ListenerConfig struct {
	Protocol string `json:"protocol"` // tcp, websocket, quic
	Address  string `json:"address"`
	Port     int    `json:"port"`
	
	// TLS配置
	EnableTLS     bool   `json:"enable_tls"`
	TLSCertFile   string `json:"tls_cert_file"`
	TLSKeyFile    string `json:"tls_key_file"`
	TLSCAFile     string `json:"tls_ca_file"`
	
	// 连接配置
	MaxConnections int           `json:"max_connections"`
	ReadTimeout    time.Duration `json:"read_timeout"`
	WriteTimeout   time.Duration `json:"write_timeout"`
	IdleTimeout    time.Duration `json:"idle_timeout"`
	
	// WebSocket特定配置
	WebSocketPath string            `json:"websocket_path,omitempty"`
	Headers       map[string]string `json:"headers,omitempty"`
}

// ListenerMetrics 监听器指标
type ListenerMetrics struct {
	// 连接指标
	TotalConnections    int64     `json:"total_connections"`
	ActiveConnections   int       `json:"active_connections"`
	FailedConnections   int64     `json:"failed_connections"`
	RejectedConnections int64     `json:"rejected_connections"`
	
	// 数据传输指标
	BytesReceived int64 `json:"bytes_received"`
	BytesSent     int64 `json:"bytes_sent"`
	PacketsRecv   int64 `json:"packets_received"`
	PacketsSent   int64 `json:"packets_sent"`
	
	// 时间指标
	StartTime       time.Time `json:"start_time"`
	LastConnection  time.Time `json:"last_connection"`
	
	// 错误指标
	ReadErrors      int64 `json:"read_errors"`
	WriteErrors     int64 `json:"write_errors"`
	TimeoutErrors   int64 `json:"timeout_errors"`
}

// ConnectionMetrics 连接指标
type ConnectionMetrics struct {
	// 基本信息
	ID          string    `json:"id"`
	RemoteAddr  string    `json:"remote_addr"`
	LocalAddr   string    `json:"local_addr"`
	ConnectTime time.Time `json:"connect_time"`
	LastActive  time.Time `json:"last_active"`
	
	// 数据传输指标
	BytesReceived int64 `json:"bytes_received"`
	BytesSent     int64 `json:"bytes_sent"`
	PacketsRecv   int64 `json:"packets_received"`
	PacketsSent   int64 `json:"packets_sent"`
	
	// 性能指标
	AverageLatency time.Duration `json:"average_latency"`
	
	// 错误指标
	ReadErrors    int64 `json:"read_errors"`
	WriteErrors   int64 `json:"write_errors"`
	TimeoutErrors int64 `json:"timeout_errors"`
}

// ListenerManager 监听器管理器接口
type ListenerManager interface {
	// AddListener 添加监听器
	AddListener(config *ListenerConfig) error
	
	// RemoveListener 移除监听器
	RemoveListener(protocol, address string) error
	
	// GetListener 获取监听器
	GetListener(protocol, address string) (Listener, bool)
	
	// GetListeners 获取所有监听器
	GetListeners() map[string]Listener
	
	// Start 启动所有监听器
	Start(ctx context.Context) error
	
	// Stop 停止所有监听器
	Stop() error
	
	// GetMetrics 获取聚合指标
	GetMetrics() *AggregatedMetrics
}

// AggregatedMetrics 聚合指标
type AggregatedMetrics struct {
	ListenerCount       int                            `json:"listener_count"`
	TotalConnections    int64                          `json:"total_connections"`
	ActiveConnections   int                            `json:"active_connections"`
	BytesReceived       int64                          `json:"bytes_received"`
	BytesSent           int64                          `json:"bytes_sent"`
	ListenerMetrics     map[string]*ListenerMetrics    `json:"listener_metrics"`
}

// ListenerFactory 监听器工厂接口
type ListenerFactory interface {
	// CreateListener 创建监听器
	CreateListener(config *ListenerConfig) (Listener, error)
	
	// SupportedProtocols 获取支持的协议列表
	SupportedProtocols() []string
}
