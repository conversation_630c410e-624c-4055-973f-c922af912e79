package listener

import (
	"context"
	"fmt"
	"sync"

	"go.uber.org/zap"
)

// DefaultListenerManager 默认监听器管理器实现
type DefaultListenerManager struct {
	logger    *zap.Logger
	mu        sync.RWMutex
	listeners map[string]Listener
	factory   ListenerFactory
	ctx       context.Context
	cancel    context.CancelFunc
}

// NewListenerManager 创建监听器管理器
func NewListenerManager(logger *zap.Logger) ListenerManager {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &DefaultListenerManager{
		logger:    logger,
		listeners: make(map[string]Listener),
		factory:   NewListenerFactory(logger),
		ctx:       ctx,
		cancel:    cancel,
	}
}

// AddListener 添加监听器
func (m *DefaultListenerManager) AddListener(config *ListenerConfig) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	key := fmt.Sprintf("%s:%s:%d", config.Protocol, config.Address, config.Port)
	
	// 检查是否已存在
	if _, exists := m.listeners[key]; exists {
		return fmt.Errorf("listener already exists: %s", key)
	}
	
	// 创建监听器
	listener, err := m.factory.CreateListener(config)
	if err != nil {
		return fmt.Errorf("failed to create listener: %w", err)
	}
	
	// 启动监听器
	if err := listener.Start(m.ctx); err != nil {
		return fmt.Errorf("failed to start listener: %w", err)
	}
	
	m.listeners[key] = listener
	
	m.logger.Info("Listener added successfully", 
		zap.String("protocol", config.Protocol),
		zap.String("address", config.Address),
		zap.Int("port", config.Port))
	
	return nil
}

// RemoveListener 移除监听器
func (m *DefaultListenerManager) RemoveListener(protocol, address string) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	// 查找匹配的监听器
	var keyToRemove string
	for key, listener := range m.listeners {
		if listener.GetProtocol() == protocol && 
		   (address == "" || listener.GetAddress() == address) {
			keyToRemove = key
			break
		}
	}
	
	if keyToRemove == "" {
		return fmt.Errorf("listener not found: %s %s", protocol, address)
	}
	
	listener := m.listeners[keyToRemove]
	
	// 停止监听器
	if err := listener.Stop(); err != nil {
		m.logger.Error("Failed to stop listener", 
			zap.String("key", keyToRemove), 
			zap.Error(err))
	}
	
	delete(m.listeners, keyToRemove)
	
	m.logger.Info("Listener removed", 
		zap.String("protocol", protocol),
		zap.String("address", address))
	
	return nil
}

// GetListener 获取监听器
func (m *DefaultListenerManager) GetListener(protocol, address string) (Listener, bool) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	for _, listener := range m.listeners {
		if listener.GetProtocol() == protocol && 
		   (address == "" || listener.GetAddress() == address) {
			return listener, true
		}
	}
	
	return nil, false
}

// GetListeners 获取所有监听器
func (m *DefaultListenerManager) GetListeners() map[string]Listener {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	result := make(map[string]Listener)
	for key, listener := range m.listeners {
		result[key] = listener
	}
	
	return result
}

// Start 启动所有监听器
func (m *DefaultListenerManager) Start(ctx context.Context) error {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	m.logger.Info("Starting all listeners", zap.Int("count", len(m.listeners)))
	
	for key, listener := range m.listeners {
		if err := listener.Start(ctx); err != nil {
			m.logger.Error("Failed to start listener", 
				zap.String("key", key), 
				zap.Error(err))
			return fmt.Errorf("failed to start listener %s: %w", key, err)
		}
	}
	
	m.logger.Info("All listeners started successfully")
	return nil
}

// Stop 停止所有监听器
func (m *DefaultListenerManager) Stop() error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.logger.Info("Stopping all listeners", zap.Int("count", len(m.listeners)))
	
	// 取消上下文
	m.cancel()
	
	var lastError error
	for key, listener := range m.listeners {
		if err := listener.Stop(); err != nil {
			m.logger.Error("Failed to stop listener", 
				zap.String("key", key), 
				zap.Error(err))
			lastError = err
		}
	}
	
	m.listeners = make(map[string]Listener)
	
	m.logger.Info("All listeners stopped")
	return lastError
}

// GetMetrics 获取聚合指标
func (m *DefaultListenerManager) GetMetrics() *AggregatedMetrics {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	metrics := &AggregatedMetrics{
		ListenerCount:   len(m.listeners),
		ListenerMetrics: make(map[string]*ListenerMetrics),
	}
	
	for key, listener := range m.listeners {
		listenerMetrics := listener.GetMetrics()
		metrics.ListenerMetrics[key] = listenerMetrics
		
		// 聚合指标
		metrics.TotalConnections += listenerMetrics.TotalConnections
		metrics.ActiveConnections += listenerMetrics.ActiveConnections
		metrics.BytesReceived += listenerMetrics.BytesReceived
		metrics.BytesSent += listenerMetrics.BytesSent
	}
	
	return metrics
}

// DefaultListenerFactory 默认监听器工厂
type DefaultListenerFactory struct {
	logger *zap.Logger
}

// NewListenerFactory 创建监听器工厂
func NewListenerFactory(logger *zap.Logger) ListenerFactory {
	return &DefaultListenerFactory{
		logger: logger,
	}
}

// CreateListener 创建监听器
func (f *DefaultListenerFactory) CreateListener(config *ListenerConfig) (Listener, error) {
	switch config.Protocol {
	case "tcp":
		return NewTCPListener(config, f.logger), nil
		
	case "websocket":
		// TODO: 实现WebSocket监听器
		return nil, fmt.Errorf("WebSocket listener not implemented yet")
		
	case "quic":
		// TODO: 实现QUIC监听器
		return nil, fmt.Errorf("QUIC listener not implemented yet")
		
	default:
		return nil, fmt.Errorf("unsupported protocol: %s", config.Protocol)
	}
}

// SupportedProtocols 获取支持的协议列表
func (f *DefaultListenerFactory) SupportedProtocols() []string {
	return []string{"tcp"}
}
