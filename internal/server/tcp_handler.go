package server

import (
	"context"
	"fmt"
	"net"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
)

// TCPConnectionState TCP连接状态
type TCPConnectionState int

const (
	TCPStateClosed TCPConnectionState = iota
	TCPStateListen
	TCPStateSynSent
	TCPStateSynReceived
	TCPStateEstablished
	TCPStateFinWait1
	TCPStateFinWait2
	TCPStateCloseWait
	TCPStateClosing
	TCPStateLastAck
	TCPStateTimeWait
)

// TCPConnection TCP连接管理
type TCPConnection struct {
	ID         string
	LocalAddr  net.Addr
	RemoteAddr net.Addr
	State      TCPConnectionState
	Conn       net.Conn
	LastUsed   time.Time
	CreatedAt  time.Time
	BytesSent  int64
	BytesRecv  int64
	mu         sync.RWMutex
}

// TCPConnectionPool TCP连接池
type TCPConnectionPool struct {
	connections map[string]*TCPConnection
	mu          sync.RWMutex
	maxIdle     int
	maxActive   int
	idleTimeout time.Duration
	logger      *zap.Logger
}

// NewTCPConnectionPool 创建TCP连接池
func NewTCPConnectionPool(maxIdle, maxActive int, idleTimeout time.Duration, logger *zap.Logger) *TCPConnectionPool {
	return &TCPConnectionPool{
		connections: make(map[string]*TCPConnection),
		maxIdle:     maxIdle,
		maxActive:   maxActive,
		idleTimeout: idleTimeout,
		logger:      logger,
	}
}

// GetConnection 获取或创建TCP连接 - 基于互联网最佳实践改进
func (pool *TCPConnectionPool) GetConnection(target string, timeout time.Duration) (*TCPConnection, error) {
	pool.mu.Lock()
	defer pool.mu.Unlock()

	// 🚀 互联网最佳实践：连接预检和健康验证
	if conn, exists := pool.connections[target]; exists {
		// 1. 基本状态检查
		if conn.State != TCPStateEstablished {
			pool.logger.Debug("Connection not in established state, removing",
				zap.String("target", target),
				zap.String("conn_id", conn.ID),
				zap.Int("state", int(conn.State)))
			pool.closeConnection(conn)
			delete(pool.connections, target)
		} else if time.Since(conn.LastUsed) >= pool.idleTimeout {
			// 2. 超时检查
			pool.logger.Debug("Connection idle timeout, removing",
				zap.String("target", target),
				zap.String("conn_id", conn.ID),
				zap.Duration("idle_time", time.Since(conn.LastUsed)))
			pool.closeConnection(conn)
			delete(pool.connections, target)
		} else if !pool.isConnectionHealthy(conn) {
			// 3. 健康状态检查
			pool.logger.Debug("Connection health check failed, removing",
				zap.String("target", target),
				zap.String("conn_id", conn.ID))
			pool.closeConnection(conn)
			delete(pool.connections, target)
		} else {
			// 连接可用，更新使用时间
			conn.LastUsed = time.Now()
			pool.logger.Debug("Reusing healthy TCP connection",
				zap.String("target", target),
				zap.String("conn_id", conn.ID),
				zap.Duration("age", time.Since(conn.CreatedAt)))
			return conn, nil
		}
	}

	// 检查连接数限制
	if len(pool.connections) >= pool.maxActive {
		return nil, fmt.Errorf("TCP connection pool is full (max: %d)", pool.maxActive)
	}

	// 创建新连接
	return pool.createConnection(target, timeout)
}

// createConnection 创建新的TCP连接
func (pool *TCPConnectionPool) createConnection(target string, timeout time.Duration) (*TCPConnection, error) {
	conn, err := net.DialTimeout("tcp", target, timeout)
	if err != nil {
		return nil, fmt.Errorf("failed to dial TCP connection to %s: %w", target, err)
	}

	tcpConn := &TCPConnection{
		ID:         fmt.Sprintf("tcp-%d", time.Now().UnixNano()),
		RemoteAddr: conn.RemoteAddr(),
		LocalAddr:  conn.LocalAddr(),
		State:      TCPStateEstablished,
		Conn:       conn,
		LastUsed:   time.Now(),
		CreatedAt:  time.Now(),
	}

	pool.connections[target] = tcpConn

	pool.logger.Debug("Created new TCP connection",
		zap.String("target", target),
		zap.String("conn_id", tcpConn.ID),
		zap.String("local_addr", tcpConn.LocalAddr.String()),
		zap.String("remote_addr", tcpConn.RemoteAddr.String()))

	return tcpConn, nil
}

// isConnectionHealthy 基于互联网最佳实践的连接健康检查
func (pool *TCPConnectionPool) isConnectionHealthy(conn *TCPConnection) bool {
	if conn.Conn == nil {
		return false
	}

	// 🚀 互联网最佳实践：多层次健康检查

	// 1. 基础连接状态检查
	if tcpConn, ok := conn.Conn.(*net.TCPConn); ok {
		// 尝试设置KeepAlive - 如果连接已断开，这会失败
		if err := tcpConn.SetKeepAlive(true); err != nil {
			pool.logger.Debug("Connection failed KeepAlive check",
				zap.String("conn_id", conn.ID),
				zap.Error(err))
			return false
		}

		// 设置短暂的读取超时进行非阻塞检查
		originalDeadline := time.Now().Add(time.Millisecond * 10)
		tcpConn.SetReadDeadline(originalDeadline)

		// 尝试读取0字节 - 这不会消耗数据，但能检测连接状态
		buffer := make([]byte, 1)
		_, err := tcpConn.Read(buffer)

		// 重置读取超时
		tcpConn.SetReadDeadline(time.Time{})

		// 分析读取结果
		if err != nil {
			if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
				// 超时是正常的，说明连接活跃但没有数据
				return true
			}
			// 其他错误表示连接问题
			if isConnectionBroken(err) {
				pool.logger.Debug("Connection failed health check",
					zap.String("conn_id", conn.ID),
					zap.Error(err))
				return false
			}
		}
	}

	// 2. 时间基础检查 - 连接不应该太老
	if time.Since(conn.CreatedAt) > time.Hour*2 {
		pool.logger.Debug("Connection too old, marking unhealthy",
			zap.String("conn_id", conn.ID),
			zap.Duration("age", time.Since(conn.CreatedAt)))
		return false
	}

	return true
}

// closeConnection 关闭TCP连接
func (pool *TCPConnectionPool) closeConnection(conn *TCPConnection) {
	if conn.Conn != nil {
		conn.Conn.Close()
		conn.State = TCPStateClosed
	}
}

// RemoveConnection 从连接池中移除指定的连接
func (pool *TCPConnectionPool) RemoveConnection(target string) {
	pool.mu.Lock()
	defer pool.mu.Unlock()

	if conn, exists := pool.connections[target]; exists {
		pool.closeConnection(conn)
		delete(pool.connections, target)
	}
}

// Cleanup 清理过期连接
func (pool *TCPConnectionPool) Cleanup() {
	pool.mu.Lock()
	defer pool.mu.Unlock()

	now := time.Now()
	var toDelete []string

	for target, conn := range pool.connections {
		if now.Sub(conn.LastUsed) > pool.idleTimeout {
			toDelete = append(toDelete, target)
			pool.closeConnection(conn)
		}
	}

	for _, target := range toDelete {
		delete(pool.connections, target)
	}

	if len(toDelete) > 0 {
		pool.logger.Debug("Cleaned up expired TCP connections",
			zap.Int("cleaned_count", len(toDelete)))
	}
}

// TCPHandler 完整的TCP处理器
type TCPHandler struct {
	server         *Server
	logger         *zap.Logger
	connectionPool *TCPConnectionPool
	ctx            context.Context
	cancel         context.CancelFunc
	wg             sync.WaitGroup

	// 配置参数
	dialTimeout  time.Duration
	readTimeout  time.Duration
	writeTimeout time.Duration
	keepAlive    time.Duration
	maxRetries   int
}

// NewTCPHandler 创建TCP处理器
func NewTCPHandler(server *Server, logger *zap.Logger) *TCPHandler {
	ctx, cancel := context.WithCancel(context.Background())

	return &TCPHandler{
		server:         server,
		logger:         logger,
		connectionPool: NewTCPConnectionPool(50, 200, time.Minute*5, logger),
		ctx:            ctx,
		cancel:         cancel,
		dialTimeout:    time.Second * 10,
		readTimeout:    time.Second * 30,
		writeTimeout:   time.Second * 10,
		keepAlive:      time.Minute * 2,
		maxRetries:     3,
	}
}

// Start 启动TCP处理器
func (h *TCPHandler) Start() error {
	h.logger.Info("Starting TCP handler")

	// 启动连接池清理协程
	h.wg.Add(1)
	go h.cleanupWorker()

	return nil
}

// Stop 停止TCP处理器
func (h *TCPHandler) Stop() error {
	h.logger.Info("Stopping TCP handler")
	h.cancel()
	h.wg.Wait()

	// 关闭所有连接
	h.connectionPool.mu.Lock()
	for _, conn := range h.connectionPool.connections {
		h.connectionPool.closeConnection(conn)
	}
	h.connectionPool.connections = make(map[string]*TCPConnection)
	h.connectionPool.mu.Unlock()

	return nil
}

// cleanupWorker 连接池清理工作协程
func (h *TCPHandler) cleanupWorker() {
	defer h.wg.Done()

	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			h.connectionPool.Cleanup()
		case <-h.ctx.Done():
			return
		}
	}
}

// HandleTCPPacket 处理TCP数据包
func (h *TCPHandler) HandleTCPPacket(packet []byte, clientID string) ([]byte, error) {
	if len(packet) < 40 {
		return nil, fmt.Errorf("TCP packet too short: %d bytes", len(packet))
	}

	// 解析TCP包
	tcpInfo, err := h.parseTCPPacket(packet)
	if err != nil {
		return nil, fmt.Errorf("failed to parse TCP packet: %w", err)
	}

	h.logger.Debug("Processing TCP packet",
		zap.String("client_id", clientID),
		zap.String("src_ip", tcpInfo.SrcIP.String()),
		zap.String("dst_ip", tcpInfo.DstIP.String()),
		zap.Uint16("src_port", tcpInfo.SrcPort),
		zap.Uint16("dst_port", tcpInfo.DstPort),
		zap.Bool("syn", tcpInfo.SYN),
		zap.Bool("ack", tcpInfo.ACK),
		zap.Bool("fin", tcpInfo.FIN),
		zap.Bool("rst", tcpInfo.RST),
		zap.Bool("psh", tcpInfo.PSH),
		zap.Int("payload_len", len(tcpInfo.Payload)))

	// 🔧 简化TCP处理策略：采用透明转发模式
	// 不管TCP标志位如何，都尝试建立连接并转发数据
	switch {
	case tcpInfo.RST:
		// RST包 - 重置连接
		return h.handleRSTPacket(tcpInfo, clientID)
	case tcpInfo.FIN:
		// FIN包 - 关闭连接
		return h.handleFINPacket(tcpInfo, clientID)
	default:
		// 所有其他包（包括SYN、ACK、数据包）都尝试建立连接并转发
		return h.handleConnectionAndForward(tcpInfo, clientID)
	}
}

// TCPPacketInfo TCP包信息
type TCPPacketInfo struct {
	SrcIP    net.IP
	DstIP    net.IP
	SrcPort  uint16
	DstPort  uint16
	SeqNum   uint32
	AckNum   uint32
	Flags    uint8
	SYN      bool
	ACK      bool
	FIN      bool
	RST      bool
	PSH      bool
	URG      bool
	Window   uint16
	Checksum uint16
	UrgPtr   uint16
	Options  []byte
	Payload  []byte
}

// parseTCPPacket 解析TCP数据包
func (h *TCPHandler) parseTCPPacket(packet []byte) (*TCPPacketInfo, error) {
	if len(packet) < 40 {
		return nil, fmt.Errorf("packet too short")
	}

	// IP头部
	srcIP := net.IP(packet[12:16])
	dstIP := net.IP(packet[16:20])

	// TCP头部
	tcpHeader := packet[20:]
	if len(tcpHeader) < 20 {
		return nil, fmt.Errorf("TCP header too short")
	}

	srcPort := uint16(tcpHeader[0])<<8 | uint16(tcpHeader[1])
	dstPort := uint16(tcpHeader[2])<<8 | uint16(tcpHeader[3])
	seqNum := uint32(tcpHeader[4])<<24 | uint32(tcpHeader[5])<<16 | uint32(tcpHeader[6])<<8 | uint32(tcpHeader[7])
	ackNum := uint32(tcpHeader[8])<<24 | uint32(tcpHeader[9])<<16 | uint32(tcpHeader[10])<<8 | uint32(tcpHeader[11])

	dataOffset := (tcpHeader[12] >> 4) * 4
	flags := tcpHeader[13]
	window := uint16(tcpHeader[14])<<8 | uint16(tcpHeader[15])
	checksum := uint16(tcpHeader[16])<<8 | uint16(tcpHeader[17])
	urgPtr := uint16(tcpHeader[18])<<8 | uint16(tcpHeader[19])

	// TCP选项
	var options []byte
	if dataOffset > 20 && len(tcpHeader) >= int(dataOffset) {
		options = tcpHeader[20:dataOffset]
	}

	// TCP载荷
	var payload []byte
	ipHeaderLen := int(packet[0]&0x0F) * 4
	tcpPayloadStart := ipHeaderLen + int(dataOffset)
	if tcpPayloadStart < len(packet) {
		payload = packet[tcpPayloadStart:]
	}

	return &TCPPacketInfo{
		SrcIP:    srcIP,
		DstIP:    dstIP,
		SrcPort:  srcPort,
		DstPort:  dstPort,
		SeqNum:   seqNum,
		AckNum:   ackNum,
		Flags:    flags,
		SYN:      (flags & 0x02) != 0,
		ACK:      (flags & 0x10) != 0,
		FIN:      (flags & 0x01) != 0,
		RST:      (flags & 0x04) != 0,
		PSH:      (flags & 0x08) != 0,
		URG:      (flags & 0x20) != 0,
		Window:   window,
		Checksum: checksum,
		UrgPtr:   urgPtr,
		Options:  options,
		Payload:  payload,
	}, nil
}

// handleConnectionAndForward 处理连接建立和数据转发
func (h *TCPHandler) handleConnectionAndForward(tcpInfo *TCPPacketInfo, clientID string) ([]byte, error) {
	target := net.JoinHostPort(tcpInfo.DstIP.String(), fmt.Sprintf("%d", tcpInfo.DstPort))

	h.logger.Debug("Handling TCP connection and forward",
		zap.String("client_id", clientID),
		zap.String("target", target),
		zap.Bool("syn", tcpInfo.SYN),
		zap.Bool("ack", tcpInfo.ACK),
		zap.Bool("psh", tcpInfo.PSH),
		zap.Int("payload_len", len(tcpInfo.Payload)))

	// 🔧 关键修复：对于SYN包，我们需要实际转发原始TCP包到目标服务器
	// 而不是建立应用层连接
	if tcpInfo.SYN && len(tcpInfo.Payload) == 0 {
		return h.handleTCPSYNForwarding(tcpInfo, clientID)
	}

	// 对于数据包，使用连接池转发
	if len(tcpInfo.Payload) > 0 {
		// 尝试获取或建立连接
		conn, err := h.connectionPool.GetConnection(target, h.dialTimeout)
		if err != nil {
			h.logger.Debug("Failed to establish TCP connection for data",
				zap.String("client_id", clientID),
				zap.String("target", target),
				zap.Error(err))
			return h.buildRSTResponse(tcpInfo, clientID)
		}

		h.logger.Debug("TCP connection available for data transfer",
			zap.String("client_id", clientID),
			zap.String("target", target),
			zap.String("conn_id", conn.ID))

		return h.forwardTCPData(conn, tcpInfo, clientID)
	}

	// 对于其他控制包（如纯ACK），不需要特殊处理
	h.logger.Debug("TCP control packet processed",
		zap.String("client_id", clientID),
		zap.String("target", target))
	return nil, nil
}

// handleTCPSYNForwarding 处理TCP SYN包的转发
func (h *TCPHandler) handleTCPSYNForwarding(tcpInfo *TCPPacketInfo, clientID string) ([]byte, error) {
	target := net.JoinHostPort(tcpInfo.DstIP.String(), fmt.Sprintf("%d", tcpInfo.DstPort))

	h.logger.Debug("Handling TCP SYN forwarding",
		zap.String("client_id", clientID),
		zap.String("target", target))

	// 🔧 关键修复：对于SYN包，建立连接并立即返回SYN-ACK
	// 这样可以让客户端完成TCP握手

	// 尝试建立到目标服务器的连接
	conn, err := h.connectionPool.GetConnection(target, h.dialTimeout)
	if err != nil {
		h.logger.Debug("Failed to establish TCP connection for SYN",
			zap.String("client_id", clientID),
			zap.String("target", target),
			zap.Error(err))
		return h.buildRSTResponse(tcpInfo, clientID)
	}

	h.logger.Debug("TCP connection established for SYN",
		zap.String("client_id", clientID),
		zap.String("target", target),
		zap.String("conn_id", conn.ID))

	// 🔧 关键修复：返回正确的SYN-ACK响应
	return h.buildProperSYNACKResponse(tcpInfo, clientID)
}

// buildProperSYNACKResponse 构造正确的SYN-ACK响应包
func (h *TCPHandler) buildProperSYNACKResponse(tcpInfo *TCPPacketInfo, clientID string) ([]byte, error) {
	// 获取客户端真实IP
	clientRealIP := h.getClientRealIP(tcpInfo.SrcIP, clientID)

	// 🔧 关键修复：使用随机序列号和正确的确认号
	serverSeqNum := uint32(time.Now().UnixNano() & 0xFFFFFFFF) // 随机序列号
	ackNum := tcpInfo.SeqNum + 1                               // 确认号是客户端序列号+1

	h.logger.Debug("Building proper SYN-ACK response",
		zap.String("client_id", clientID),
		zap.Uint32("client_seq", tcpInfo.SeqNum),
		zap.Uint32("server_seq", serverSeqNum),
		zap.Uint32("ack_num", ackNum))

	// 构造完整的TCP SYN-ACK响应包
	// IP头部 (20字节) + TCP头部 (20字节)
	response := make([]byte, 40)

	// IP头部
	response[0] = 0x45  // 版本(4) + 头长度(5*4=20)
	response[1] = 0x00  // 服务类型
	response[2] = 0x00  // 总长度高字节
	response[3] = 0x28  // 总长度低字节 (40字节)
	response[4] = 0x00  // 标识高字节
	response[5] = 0x00  // 标识低字节
	response[6] = 0x40  // 标志(DF=1) + 片偏移高3位
	response[7] = 0x00  // 片偏移低8位
	response[8] = 0x40  // TTL (64)
	response[9] = 0x06  // 协议 (TCP=6)
	response[10] = 0x00 // 头校验和高字节
	response[11] = 0x00 // 头校验和低字节

	// 源IP (目标服务器IP)
	dstIP := tcpInfo.DstIP.To4()
	copy(response[12:16], dstIP)

	// 目标IP (客户端IP)
	srcIP := clientRealIP.To4()
	copy(response[16:20], srcIP)

	// TCP头部
	response[20] = byte(tcpInfo.DstPort >> 8) // 源端口高字节
	response[21] = byte(tcpInfo.DstPort)      // 源端口低字节
	response[22] = byte(tcpInfo.SrcPort >> 8) // 目标端口高字节
	response[23] = byte(tcpInfo.SrcPort)      // 目标端口低字节

	// 序列号
	response[24] = byte(serverSeqNum >> 24)
	response[25] = byte(serverSeqNum >> 16)
	response[26] = byte(serverSeqNum >> 8)
	response[27] = byte(serverSeqNum)

	// 确认号
	response[28] = byte(ackNum >> 24)
	response[29] = byte(ackNum >> 16)
	response[30] = byte(ackNum >> 8)
	response[31] = byte(ackNum)

	response[32] = 0x50 // 头长度(5*4=20) + 保留位
	response[33] = 0x12 // 标志位: SYN(1) + ACK(1)
	response[34] = 0xFF // 窗口大小高字节 (65535)
	response[35] = 0xFF // 窗口大小低字节
	response[36] = 0x00 // 校验和高字节
	response[37] = 0x00 // 校验和低字节
	response[38] = 0x00 // 紧急指针高字节
	response[39] = 0x00 // 紧急指针低字节

	// 计算IP头校验和
	h.calculateIPChecksum(response[:20])

	// 计算TCP校验和
	h.calculateTCPChecksum(response)

	h.logger.Debug("SYN-ACK response packet built",
		zap.String("client_id", clientID),
		zap.Int("packet_size", len(response)))

	return response, nil
}

// calculateIPChecksum 计算IP头校验和
func (h *TCPHandler) calculateIPChecksum(header []byte) {
	// 清零校验和字段
	header[10] = 0
	header[11] = 0

	// 计算校验和
	sum := uint32(0)
	for i := 0; i < len(header); i += 2 {
		sum += uint32(header[i])<<8 + uint32(header[i+1])
	}

	// 处理进位
	for sum>>16 != 0 {
		sum = (sum & 0xFFFF) + (sum >> 16)
	}

	// 取反
	checksum := uint16(^sum)
	header[10] = byte(checksum >> 8)
	header[11] = byte(checksum)
}

// calculateTCPChecksum 计算TCP校验和
func (h *TCPHandler) calculateTCPChecksum(packet []byte) {
	// 清零TCP校验和字段
	packet[36] = 0
	packet[37] = 0

	// TCP伪头部 + TCP头部
	srcIP := packet[12:16]
	dstIP := packet[16:20]
	tcpHeader := packet[20:]

	// 构造伪头部
	pseudoHeader := make([]byte, 12)
	copy(pseudoHeader[0:4], srcIP)
	copy(pseudoHeader[4:8], dstIP)
	pseudoHeader[8] = 0
	pseudoHeader[9] = 6 // TCP协议号
	pseudoHeader[10] = 0
	pseudoHeader[11] = byte(len(tcpHeader))

	// 计算校验和
	sum := uint32(0)

	// 伪头部
	for i := 0; i < len(pseudoHeader); i += 2 {
		sum += uint32(pseudoHeader[i])<<8 + uint32(pseudoHeader[i+1])
	}

	// TCP头部
	for i := 0; i < len(tcpHeader); i += 2 {
		if i+1 < len(tcpHeader) {
			sum += uint32(tcpHeader[i])<<8 + uint32(tcpHeader[i+1])
		} else {
			sum += uint32(tcpHeader[i]) << 8
		}
	}

	// 处理进位
	for sum>>16 != 0 {
		sum = (sum & 0xFFFF) + (sum >> 16)
	}

	// 取反
	checksum := uint16(^sum)
	packet[36] = byte(checksum >> 8)
	packet[37] = byte(checksum)
}

// forwardTCPData 转发TCP数据
// forwardTCPData 转发TCP数据包 - 基于互联网最佳实践改进
func (h *TCPHandler) forwardTCPData(conn *TCPConnection, tcpInfo *TCPPacketInfo, clientID string) ([]byte, error) {
	target := net.JoinHostPort(tcpInfo.DstIP.String(), fmt.Sprintf("%d", tcpInfo.DstPort))

	h.logger.Debug("Forwarding TCP data",
		zap.String("client_id", clientID),
		zap.String("target", target),
		zap.Int("payload_size", len(tcpInfo.Payload)))

	// 🚀 HTTP/HTTPS感知优化：检测是否为HTTP/HTTPS流量
	if h.isHTTPTraffic(tcpInfo.Payload, tcpInfo.DstPort) {
		h.logger.Debug("HTTP/HTTPS traffic detected, using optimized handling",
			zap.String("client_id", clientID),
			zap.String("target", target),
			zap.Uint16("dst_port", tcpInfo.DstPort))
		return h.forwardHTTPTraffic(conn, tcpInfo, clientID, target)
	}

	// 🚀 互联网最佳实践：重试机制和错误恢复
	maxRetries := 2
	for attempt := 0; attempt <= maxRetries; attempt++ {
		if attempt > 0 {
			h.logger.Debug("Retrying TCP data forwarding",
				zap.String("client_id", clientID),
				zap.String("target", target),
				zap.Int("attempt", attempt))

			// 重试前获取新连接
			newConn, err := h.connectionPool.GetConnection(target, h.dialTimeout)
			if err != nil {
				h.logger.Debug("Failed to get connection for retry",
					zap.String("client_id", clientID),
					zap.String("target", target),
					zap.Int("attempt", attempt),
					zap.Error(err))
				continue
			}
			conn = newConn
		}

		// 执行数据转发
		response, err := h.attemptTCPDataForward(conn, tcpInfo, clientID, target)
		if err == nil {
			return response, nil
		}

		// 检查是否是连接断开错误
		if isConnectionBroken(err) {
			h.logger.Debug("Connection broken, will retry with new connection",
				zap.String("client_id", clientID),
				zap.String("target", target),
				zap.String("conn_id", conn.ID),
				zap.Int("attempt", attempt),
				zap.Error(err))

			// 立即移除断开的连接
			h.connectionPool.RemoveConnection(target)

			// 如果还有重试机会，继续
			if attempt < maxRetries {
				continue
			}
		}

		// 其他类型的错误，直接返回
		h.logger.Debug("Non-recoverable error in TCP data forwarding",
			zap.String("client_id", clientID),
			zap.String("target", target),
			zap.Error(err))
		return h.buildRSTResponse(tcpInfo, clientID)
	}

	// 所有重试都失败了
	h.logger.Debug("All TCP forwarding attempts failed",
		zap.String("client_id", clientID),
		zap.String("target", target),
		zap.Int("max_retries", maxRetries))
	return h.buildRSTResponse(tcpInfo, clientID)
}

// isHTTPTraffic 检测是否为HTTP/HTTPS流量
func (h *TCPHandler) isHTTPTraffic(payload []byte, dstPort uint16) bool {
	// 1. 端口检查：常见的HTTP/HTTPS端口
	httpPorts := []uint16{80, 443, 8080, 8443, 3000, 8000, 9000}
	isHTTPPort := false
	for _, port := range httpPorts {
		if dstPort == port {
			isHTTPPort = true
			break
		}
	}

	// 2. 内容检查：HTTP方法
	if len(payload) >= 4 {
		payloadStr := string(payload[:min(len(payload), 16)])
		httpMethods := []string{"GET ", "POST", "PUT ", "DELE", "HEAD", "OPTI", "PATC", "TRAC", "CONN"}
		for _, method := range httpMethods {
			if strings.HasPrefix(payloadStr, method) {
				return true
			}
		}
	}

	// 如果是HTTP端口且有载荷，很可能是HTTP流量
	return isHTTPPort && len(payload) > 0
}

// forwardHTTPTraffic 专门处理HTTP/HTTPS流量的转发
func (h *TCPHandler) forwardHTTPTraffic(conn *TCPConnection, tcpInfo *TCPPacketInfo, clientID, target string) ([]byte, error) {
	h.logger.Debug("Handling HTTP/HTTPS traffic with optimized strategy",
		zap.String("client_id", clientID),
		zap.String("target", target),
		zap.Int("payload_size", len(tcpInfo.Payload)))

	// 🚀 HTTP优化策略：预期连接会被服务器关闭，不进行连接重用验证
	// 直接尝试转发，如果失败则立即创建新连接

	response, err := h.attemptTCPDataForward(conn, tcpInfo, clientID, target)
	if err == nil {
		// 🚀 HTTP优化：成功后立即标记连接为可能断开状态
		// 这样下次请求会优先创建新连接而不是尝试重用
		h.markConnectionAsHTTPUsed(conn, target)
		return response, nil
	}

	// 如果是连接断开错误（HTTP服务器的正常行为），立即尝试新连接
	if isConnectionBroken(err) {
		h.logger.Debug("HTTP connection closed by server (normal behavior), creating new connection",
			zap.String("client_id", clientID),
			zap.String("target", target),
			zap.String("conn_id", conn.ID))

		// 移除旧连接
		h.connectionPool.RemoveConnection(target)

		// 创建新连接并重试
		newConn, err := h.connectionPool.GetConnection(target, h.dialTimeout)
		if err != nil {
			h.logger.Debug("Failed to create new HTTP connection",
				zap.String("client_id", clientID),
				zap.String("target", target),
				zap.Error(err))
			return h.buildRSTResponse(tcpInfo, clientID)
		}

		response, err := h.attemptTCPDataForward(newConn, tcpInfo, clientID, target)
		if err == nil {
			h.markConnectionAsHTTPUsed(newConn, target)
			return response, nil
		}

		h.logger.Debug("HTTP retry also failed",
			zap.String("client_id", clientID),
			zap.String("target", target),
			zap.Error(err))
	}

	return h.buildRSTResponse(tcpInfo, clientID)
}

// markConnectionAsHTTPUsed 标记连接已用于HTTP流量
func (h *TCPHandler) markConnectionAsHTTPUsed(conn *TCPConnection, target string) {
	// 设置一个较短的空闲超时，因为HTTP服务器很可能会关闭连接
	conn.LastUsed = time.Now()
	// 可以在这里添加HTTP特定的元数据
	h.logger.Debug("Marked connection as HTTP-used",
		zap.String("target", target),
		zap.String("conn_id", conn.ID))
}

// min 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// attemptTCPDataForward 尝试执行TCP数据转发
func (h *TCPHandler) attemptTCPDataForward(conn *TCPConnection, tcpInfo *TCPPacketInfo, clientID, target string) ([]byte, error) {
	// 设置写超时
	if tcpConn, ok := conn.Conn.(*net.TCPConn); ok {
		tcpConn.SetWriteDeadline(time.Now().Add(h.writeTimeout))
	}

	// 发送数据
	n, err := conn.Conn.Write(tcpInfo.Payload)
	if err != nil {
		return nil, fmt.Errorf("write failed: %w", err)
	}

	conn.BytesSent += int64(n)
	conn.LastUsed = time.Now()

	// 设置读超时
	if tcpConn, ok := conn.Conn.(*net.TCPConn); ok {
		tcpConn.SetReadDeadline(time.Now().Add(h.readTimeout))
	}

	h.logger.Debug("Waiting for TCP response",
		zap.String("client_id", clientID),
		zap.String("target", target),
		zap.Duration("read_timeout", h.readTimeout))

	// 读取响应
	responseBuffer := make([]byte, 8192)
	n, err = conn.Conn.Read(responseBuffer)
	if err != nil {
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			h.logger.Debug("TCP read timeout, sending ACK",
				zap.String("client_id", clientID),
				zap.String("target", target),
				zap.Duration("timeout", h.readTimeout))
			return h.buildACKResponse(tcpInfo, clientID)
		}
		return nil, fmt.Errorf("read failed: %w", err)
	}

	if n > 0 {
		conn.BytesRecv += int64(n)
		h.logger.Debug("Received TCP response data",
			zap.String("client_id", clientID),
			zap.String("target", target),
			zap.Int("response_size", n))

		// 构造数据响应包
		return h.buildDataResponse(tcpInfo, responseBuffer[:n], clientID)
	}

	// 没有数据，发送ACK
	return h.buildACKResponse(tcpInfo, clientID)
}

// handleSYNPacket 处理SYN包（连接建立）
func (h *TCPHandler) handleSYNPacket(tcpInfo *TCPPacketInfo, clientID string) ([]byte, error) {
	target := net.JoinHostPort(tcpInfo.DstIP.String(), fmt.Sprintf("%d", tcpInfo.DstPort))

	h.logger.Debug("Handling TCP SYN packet",
		zap.String("client_id", clientID),
		zap.String("target", target))

	// 🔧 关键修复：对于SYN包，我们采用透明代理模式
	// 不在这里建立连接，而是等待实际的数据传输时再建立连接
	// 这样可以避免TCP状态管理的复杂性

	h.logger.Debug("TCP SYN packet noted, will establish connection on data transfer",
		zap.String("client_id", clientID),
		zap.String("target", target))

	// 不返回任何响应，让客户端的TCP栈处理超时和重传
	// 当客户端发送实际数据时，我们再建立连接并转发
	return nil, nil
}

// handleDataPacket 处理数据包
func (h *TCPHandler) handleDataPacket(tcpInfo *TCPPacketInfo, clientID string) ([]byte, error) {
	target := net.JoinHostPort(tcpInfo.DstIP.String(), fmt.Sprintf("%d", tcpInfo.DstPort))

	h.logger.Debug("Handling TCP data packet",
		zap.String("client_id", clientID),
		zap.String("target", target),
		zap.Int("payload_size", len(tcpInfo.Payload)),
		zap.Bool("has_payload", len(tcpInfo.Payload) > 0))

	// 如果没有数据载荷，只是一个ACK包，不需要特殊处理
	if len(tcpInfo.Payload) == 0 {
		h.logger.Debug("TCP packet has no payload, treating as ACK",
			zap.String("client_id", clientID),
			zap.String("target", target))
		return h.handleACKPacket(tcpInfo, clientID)
	}

	// 获取连接
	conn, err := h.connectionPool.GetConnection(target, h.dialTimeout)
	if err != nil {
		h.logger.Debug("No TCP connection available for data packet",
			zap.String("client_id", clientID),
			zap.String("target", target),
			zap.Error(err))
		return h.buildRSTResponse(tcpInfo, clientID)
	}

	h.logger.Debug("Found TCP connection for data transfer",
		zap.String("client_id", clientID),
		zap.String("target", target),
		zap.String("conn_id", conn.ID))

	// 设置写超时
	if tcpConn, ok := conn.Conn.(*net.TCPConn); ok {
		tcpConn.SetWriteDeadline(time.Now().Add(h.writeTimeout))
	}

	// 发送数据
	n, err := conn.Conn.Write(tcpInfo.Payload)
	if err != nil {
		h.logger.Debug("Failed to write TCP data",
			zap.String("client_id", clientID),
			zap.String("target", target),
			zap.Error(err))

		// 🔧 修复：当连接断开时，立即从连接池中移除该连接
		if isConnectionBroken(err) {
			h.logger.Debug("Connection broken, removing from pool",
				zap.String("client_id", clientID),
				zap.String("target", target),
				zap.String("conn_id", conn.ID))
			h.connectionPool.RemoveConnection(target)
		}

		return h.buildRSTResponse(tcpInfo, clientID)
	}

	conn.BytesSent += int64(n)
	conn.LastUsed = time.Now()

	// 设置读超时
	if tcpConn, ok := conn.Conn.(*net.TCPConn); ok {
		tcpConn.SetReadDeadline(time.Now().Add(h.readTimeout))
	}

	h.logger.Debug("Waiting for TCP response",
		zap.String("client_id", clientID),
		zap.String("target", target),
		zap.Duration("read_timeout", h.readTimeout))

	// 读取响应
	responseBuffer := make([]byte, 8192) // 增加缓冲区大小
	n, err = conn.Conn.Read(responseBuffer)
	if err != nil {
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			h.logger.Debug("TCP read timeout, sending ACK",
				zap.String("client_id", clientID),
				zap.String("target", target),
				zap.Duration("timeout", h.readTimeout))
			return h.buildACKResponse(tcpInfo, clientID)
		}
		h.logger.Debug("Failed to read TCP response",
			zap.String("client_id", clientID),
			zap.String("target", target),
			zap.Error(err))

		// 🔧 修复：当连接断开时，立即从连接池中移除该连接
		if isConnectionBroken(err) {
			h.logger.Debug("Connection broken during read, removing from pool",
				zap.String("client_id", clientID),
				zap.String("target", target),
				zap.String("conn_id", conn.ID))
			h.connectionPool.RemoveConnection(target)
		}

		return h.buildRSTResponse(tcpInfo, clientID)
	}

	if n > 0 {
		conn.BytesRecv += int64(n)
		h.logger.Debug("Received TCP response data",
			zap.String("client_id", clientID),
			zap.String("target", target),
			zap.Int("response_size", n))

		// 构造数据响应包
		return h.buildDataResponse(tcpInfo, responseBuffer[:n], clientID)
	}

	return h.buildACKResponse(tcpInfo, clientID)
}

// handleFINPacket 处理FIN包（连接关闭）
func (h *TCPHandler) handleFINPacket(tcpInfo *TCPPacketInfo, clientID string) ([]byte, error) {
	target := net.JoinHostPort(tcpInfo.DstIP.String(), fmt.Sprintf("%d", tcpInfo.DstPort))

	h.logger.Debug("Handling TCP FIN packet",
		zap.String("client_id", clientID),
		zap.String("target", target))

	// 关闭连接
	h.connectionPool.mu.Lock()
	if conn, exists := h.connectionPool.connections[target]; exists {
		h.connectionPool.closeConnection(conn)
		delete(h.connectionPool.connections, target)
	}
	h.connectionPool.mu.Unlock()

	// 返回FIN-ACK响应
	return h.buildFINACKResponse(tcpInfo, clientID)
}

// handleRSTPacket 处理RST包（连接重置）
func (h *TCPHandler) handleRSTPacket(tcpInfo *TCPPacketInfo, clientID string) ([]byte, error) {
	target := net.JoinHostPort(tcpInfo.DstIP.String(), fmt.Sprintf("%d", tcpInfo.DstPort))

	h.logger.Debug("Handling TCP RST packet",
		zap.String("client_id", clientID),
		zap.String("target", target))

	// 强制关闭连接
	h.connectionPool.mu.Lock()
	if conn, exists := h.connectionPool.connections[target]; exists {
		h.connectionPool.closeConnection(conn)
		delete(h.connectionPool.connections, target)
	}
	h.connectionPool.mu.Unlock()

	// RST包通常不需要响应
	return nil, nil
}

// handleACKPacket 处理ACK包
func (h *TCPHandler) handleACKPacket(tcpInfo *TCPPacketInfo, clientID string) ([]byte, error) {
	target := net.JoinHostPort(tcpInfo.DstIP.String(), fmt.Sprintf("%d", tcpInfo.DstPort))

	h.logger.Debug("Handling TCP ACK packet",
		zap.String("client_id", clientID),
		zap.String("target", target),
		zap.Uint32("seq_num", tcpInfo.SeqNum),
		zap.Uint32("ack_num", tcpInfo.AckNum))

	// 检查是否有对应的连接
	h.connectionPool.mu.RLock()
	conn, exists := h.connectionPool.connections[target]
	h.connectionPool.mu.RUnlock()

	if exists {
		// 更新连接的最后使用时间
		conn.mu.Lock()
		conn.LastUsed = time.Now()
		conn.mu.Unlock()

		h.logger.Debug("TCP ACK packet processed, connection updated",
			zap.String("client_id", clientID),
			zap.String("target", target),
			zap.String("conn_id", conn.ID))
	} else {
		h.logger.Debug("TCP ACK packet received but no connection found",
			zap.String("client_id", clientID),
			zap.String("target", target))
	}

	// ACK包通常不需要响应，但连接已经准备好接收数据
	return nil, nil
}

// buildSYNACKResponse 构造SYN-ACK响应包
func (h *TCPHandler) buildSYNACKResponse(tcpInfo *TCPPacketInfo, clientID string) ([]byte, error) {
	h.logger.Debug("SYN packet processed, connection established",
		zap.String("client_id", clientID),
		zap.Uint32("client_seq", tcpInfo.SeqNum))

	// 🔧 关键修复：对于SYN包，我们已经建立了到目标服务器的连接
	// 但不需要返回SYN-ACK响应包，因为这会导致TCP状态混乱
	// 让客户端的后续数据包直接通过已建立的连接转发
	return nil, nil
}

// buildACKResponse 构造ACK响应包
func (h *TCPHandler) buildACKResponse(tcpInfo *TCPPacketInfo, clientID string) ([]byte, error) {
	clientRealIP := h.getClientRealIP(tcpInfo.SrcIP, clientID)

	tcpResponse := []byte{
		byte(tcpInfo.DstPort >> 8), byte(tcpInfo.DstPort), // 源端口
		byte(tcpInfo.SrcPort >> 8), byte(tcpInfo.SrcPort), // 目标端口
		0x00, 0x00, 0x00, 0x01, // 序列号
		byte(tcpInfo.SeqNum >> 24), byte(tcpInfo.SeqNum >> 16), // 确认号
		byte(tcpInfo.SeqNum >> 8), byte(tcpInfo.SeqNum),
		0x50, 0x10, // 头长度(20) + ACK标志
		0x20, 0x00, // 窗口大小
		0x00, 0x00, // 校验和
		0x00, 0x00, // 紧急指针
	}

	return h.server.buildTCPResponsePacket(clientRealIP, tcpInfo.SrcPort, tcpInfo.DstIP, tcpInfo.DstPort, tcpResponse)
}

// buildRSTResponse 构造RST响应包
func (h *TCPHandler) buildRSTResponse(tcpInfo *TCPPacketInfo, clientID string) ([]byte, error) {
	clientRealIP := h.getClientRealIP(tcpInfo.SrcIP, clientID)

	tcpResponse := []byte{
		byte(tcpInfo.DstPort >> 8), byte(tcpInfo.DstPort), // 源端口
		byte(tcpInfo.SrcPort >> 8), byte(tcpInfo.SrcPort), // 目标端口
		0x00, 0x00, 0x00, 0x00, // 序列号
		byte(tcpInfo.SeqNum >> 24), byte(tcpInfo.SeqNum >> 16), // 确认号
		byte(tcpInfo.SeqNum >> 8), byte(tcpInfo.SeqNum),
		0x50, 0x04, // 头长度(20) + RST标志
		0x00, 0x00, // 窗口大小
		0x00, 0x00, // 校验和
		0x00, 0x00, // 紧急指针
	}

	return h.server.buildTCPResponsePacket(clientRealIP, tcpInfo.SrcPort, tcpInfo.DstIP, tcpInfo.DstPort, tcpResponse)
}

// buildFINACKResponse 构造FIN-ACK响应包
func (h *TCPHandler) buildFINACKResponse(tcpInfo *TCPPacketInfo, clientID string) ([]byte, error) {
	clientRealIP := h.getClientRealIP(tcpInfo.SrcIP, clientID)

	tcpResponse := []byte{
		byte(tcpInfo.DstPort >> 8), byte(tcpInfo.DstPort), // 源端口
		byte(tcpInfo.SrcPort >> 8), byte(tcpInfo.SrcPort), // 目标端口
		0x00, 0x00, 0x00, 0x01, // 序列号
		byte(tcpInfo.SeqNum >> 24), byte(tcpInfo.SeqNum >> 16), // 确认号
		byte(tcpInfo.SeqNum >> 8), byte(tcpInfo.SeqNum + 1),
		0x50, 0x11, // 头长度(20) + FIN+ACK标志
		0x20, 0x00, // 窗口大小
		0x00, 0x00, // 校验和
		0x00, 0x00, // 紧急指针
	}

	return h.server.buildTCPResponsePacket(clientRealIP, tcpInfo.SrcPort, tcpInfo.DstIP, tcpInfo.DstPort, tcpResponse)
}

// buildDataResponse 构造数据响应包
func (h *TCPHandler) buildDataResponse(tcpInfo *TCPPacketInfo, data []byte, clientID string) ([]byte, error) {
	clientRealIP := h.getClientRealIP(tcpInfo.SrcIP, clientID)

	// 构造TCP头部 + 数据
	tcpHeader := []byte{
		byte(tcpInfo.DstPort >> 8), byte(tcpInfo.DstPort), // 源端口
		byte(tcpInfo.SrcPort >> 8), byte(tcpInfo.SrcPort), // 目标端口
		0x00, 0x00, 0x00, 0x01, // 序列号
		byte(tcpInfo.SeqNum >> 24), byte(tcpInfo.SeqNum >> 16), // 确认号
		byte(tcpInfo.SeqNum >> 8), byte(tcpInfo.SeqNum),
		0x50, 0x18, // 头长度(20) + PSH+ACK标志
		0x20, 0x00, // 窗口大小
		0x00, 0x00, // 校验和
		0x00, 0x00, // 紧急指针
	}

	// 合并TCP头部和数据
	tcpResponse := append(tcpHeader, data...)

	return h.server.buildTCPResponsePacket(clientRealIP, tcpInfo.SrcPort, tcpInfo.DstIP, tcpInfo.DstPort, tcpResponse)
}

// getClientRealIP 获取客户端真实IP
func (h *TCPHandler) getClientRealIP(srcIP net.IP, clientID string) net.IP {
	// 使用地址映射缓存获取客户端真实IP
	if mapping, exists := h.server.addressMappingCache.Get(clientID); exists {
		h.logger.Debug("Using cached address mapping for TCP response",
			zap.String("client_id", clientID),
			zap.String("cached_client_real_ip", mapping.ClientRealIP.String()),
			zap.String("packet_src_ip", srcIP.String()))
		return mapping.ClientRealIP
	}

	// 降级：使用数据包中的源IP
	return srcIP
}

// isConnectionBroken 检查错误是否表示连接已断开
func isConnectionBroken(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()
	// 检查常见的连接断开错误
	return err.Error() == "EOF" ||
		strings.Contains(errStr, "broken pipe") ||
		strings.Contains(errStr, "connection reset") ||
		strings.Contains(errStr, "connection refused") ||
		strings.Contains(errStr, "connection aborted")
}
