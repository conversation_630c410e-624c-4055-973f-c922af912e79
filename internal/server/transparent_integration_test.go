package server

import (
	"testing"
	"time"

	"cyber-bastion/internal/server/transparent"
	"cyber-bastion/pkg/config"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap/zaptest"
)

// TestServerTransparentProxyIntegration 测试服务器与透明代理模块的集成
func TestServerTransparentProxyIntegration(t *testing.T) {
	logger := zaptest.NewLogger(t)

	// 创建测试配置
	cfg := &config.ServerConfig{
		Host: "127.0.0.1",
		Port: 0, // 使用随机端口
		Security: &config.SecurityConfig{
			AllowedIPs: []string{"127.0.0.1", "::1"},
		},
	}

	// 创建服务器
	server := NewServer(cfg, logger)
	require.NotNil(t, server)

	// 验证透明代理模块已初始化
	assert.NotNil(t, server.transparentProxy, "Transparent proxy should be initialized")

	// 测试服务器启动和停止
	t.Run("服务器启动和停止", func(t *testing.T) {
		// 启动服务器
		err := server.Start()
		require.NoError(t, err)

		// 等待服务器启动
		time.Sleep(100 * time.Millisecond)

		// 停止服务器
		err = server.Stop()
		require.NoError(t, err)
	})
}

// TestTransparentProxyPacketForwarding 测试透明代理数据包转发
func TestTransparentProxyPacketForwarding(t *testing.T) {
	logger := zaptest.NewLogger(t)

	// 创建透明代理配置
	config := transparent.DefaultConfig()
	config.EnableDebugLog = true
	config.LogPacketInfo = true

	// 创建透明代理
	proxy, err := transparent.NewTransparentProxy(config, logger)
	require.NoError(t, err)
	require.NotNil(t, proxy)

	defer proxy.Close()

	t.Run("IPv4 ICMP数据包转发", func(t *testing.T) {
		// 构造IPv4 ICMP数据包
		packet := buildTestIPv4ICMPPacket()

		// 转发数据包
		response, err := proxy.ForwardPacket(packet, "test-client")

		// 验证结果（可能没有响应，这是正常的）
		if err != nil {
			t.Logf("ICMP forwarding error (expected): %v", err)
		}
		if response != nil {
			t.Logf("ICMP response received: %d bytes", len(response))
		}
	})

	t.Run("IPv4 TCP数据包转发", func(t *testing.T) {
		// 构造IPv4 TCP数据包
		packet := buildTestIPv4TCPPacket()

		// 转发数据包
		response, err := proxy.ForwardPacket(packet, "test-client")

		// 验证结果（可能没有响应，这是正常的）
		if err != nil {
			t.Logf("TCP forwarding error (expected): %v", err)
		}
		if response != nil {
			t.Logf("TCP response received: %d bytes", len(response))
		}
	})

	t.Run("IPv4 UDP数据包转发", func(t *testing.T) {
		// 构造IPv4 UDP数据包
		packet := buildTestIPv4UDPPacket()

		// 转发数据包
		response, err := proxy.ForwardPacket(packet, "test-client")

		// 验证结果（可能没有响应，这是正常的）
		if err != nil {
			t.Logf("UDP forwarding error (expected): %v", err)
		}
		if response != nil {
			t.Logf("UDP response received: %d bytes", len(response))
		}
	})
}

// TestTransparentProxyMetrics 测试透明代理指标收集
func TestTransparentProxyMetrics(t *testing.T) {
	logger := zaptest.NewLogger(t)

	// 创建透明代理
	config := transparent.DefaultConfig()
	proxy, err := transparent.NewTransparentProxy(config, logger)
	require.NoError(t, err)
	require.NotNil(t, proxy)

	defer proxy.Close()

	// 获取初始指标
	metrics := proxy.GetMetrics()
	require.NotNil(t, metrics)

	// 验证初始指标
	assert.Equal(t, int64(0), metrics.PacketsForwarded)
	assert.Equal(t, int64(0), metrics.PacketsDropped)
	assert.Equal(t, int64(0), metrics.BytesForwarded)

	// 转发一些数据包
	packet := buildTestIPv4ICMPPacket()
	proxy.ForwardPacket(packet, "test-client")

	// 获取更新后的指标
	updatedMetrics := proxy.GetMetrics()
	// 验证指标有更新（可能是转发成功或失败）
	totalProcessed := updatedMetrics.PacketsForwarded + updatedMetrics.PacketsDropped
	assert.Greater(t, totalProcessed, int64(0))
}

// buildTestIPv4ICMPPacket 构造测试用的IPv4 ICMP数据包
func buildTestIPv4ICMPPacket() []byte {
	// IPv4头部 (20字节) + ICMP头部 (8字节)
	packet := make([]byte, 28)

	// IPv4头部
	packet[0] = 0x45  // 版本(4) + 头部长度(5*4=20字节)
	packet[1] = 0x00  // 服务类型
	packet[2] = 0x00  // 总长度高字节
	packet[3] = 0x1C  // 总长度低字节 (28字节)
	packet[4] = 0x00  // 标识高字节
	packet[5] = 0x01  // 标识低字节
	packet[6] = 0x00  // 标志 + 片偏移高3位
	packet[7] = 0x00  // 片偏移低8位
	packet[8] = 0x40  // TTL (64)
	packet[9] = 0x01  // 协议 (1 = ICMP)
	packet[10] = 0x00 // 头部校验和高字节
	packet[11] = 0x00 // 头部校验和低字节
	// 源IP: ********
	packet[12] = 10
	packet[13] = 1
	packet[14] = 0
	packet[15] = 2
	// 目标IP: *******
	packet[16] = 8
	packet[17] = 8
	packet[18] = 8
	packet[19] = 8

	// ICMP头部 (Echo Request)
	packet[20] = 0x08 // 类型 (8 = Echo Request)
	packet[21] = 0x00 // 代码
	packet[22] = 0x00 // 校验和高字节
	packet[23] = 0x00 // 校验和低字节
	packet[24] = 0x00 // 标识符高字节
	packet[25] = 0x01 // 标识符低字节
	packet[26] = 0x00 // 序列号高字节
	packet[27] = 0x01 // 序列号低字节

	return packet
}

// buildTestIPv4TCPPacket 构造测试用的IPv4 TCP数据包
func buildTestIPv4TCPPacket() []byte {
	// IPv4头部 (20字节) + TCP头部 (20字节)
	packet := make([]byte, 40)

	// IPv4头部
	packet[0] = 0x45  // 版本(4) + 头部长度(5*4=20字节)
	packet[1] = 0x00  // 服务类型
	packet[2] = 0x00  // 总长度高字节
	packet[3] = 0x28  // 总长度低字节 (40字节)
	packet[4] = 0x00  // 标识高字节
	packet[5] = 0x01  // 标识低字节
	packet[6] = 0x40  // 标志 (DF=1) + 片偏移高3位
	packet[7] = 0x00  // 片偏移低8位
	packet[8] = 0x40  // TTL (64)
	packet[9] = 0x06  // 协议 (6 = TCP)
	packet[10] = 0x00 // 头部校验和高字节
	packet[11] = 0x00 // 头部校验和低字节
	// 源IP: ********
	packet[12] = 10
	packet[13] = 1
	packet[14] = 0
	packet[15] = 2
	// 目标IP: ************* (example.com)
	packet[16] = 93
	packet[17] = 184
	packet[18] = 216
	packet[19] = 34

	// TCP头部
	packet[20] = 0x04 // 源端口高字节 (1024)
	packet[21] = 0x00 // 源端口低字节
	packet[22] = 0x00 // 目标端口高字节 (80)
	packet[23] = 0x50 // 目标端口低字节
	packet[24] = 0x00 // 序列号 (4字节)
	packet[25] = 0x00
	packet[26] = 0x00
	packet[27] = 0x01
	packet[28] = 0x00 // 确认号 (4字节)
	packet[29] = 0x00
	packet[30] = 0x00
	packet[31] = 0x00
	packet[32] = 0x50 // 头部长度(5*4=20字节) + 保留位
	packet[33] = 0x02 // 标志 (SYN=1)
	packet[34] = 0x20 // 窗口大小高字节 (8192)
	packet[35] = 0x00 // 窗口大小低字节
	packet[36] = 0x00 // 校验和高字节
	packet[37] = 0x00 // 校验和低字节
	packet[38] = 0x00 // 紧急指针高字节
	packet[39] = 0x00 // 紧急指针低字节

	return packet
}

// buildTestIPv4UDPPacket 构造测试用的IPv4 UDP数据包
func buildTestIPv4UDPPacket() []byte {
	// IPv4头部 (20字节) + UDP头部 (8字节) + 数据 (4字节)
	packet := make([]byte, 32)

	// IPv4头部
	packet[0] = 0x45  // 版本(4) + 头部长度(5*4=20字节)
	packet[1] = 0x00  // 服务类型
	packet[2] = 0x00  // 总长度高字节
	packet[3] = 0x20  // 总长度低字节 (32字节)
	packet[4] = 0x00  // 标识高字节
	packet[5] = 0x01  // 标识低字节
	packet[6] = 0x00  // 标志 + 片偏移高3位
	packet[7] = 0x00  // 片偏移低8位
	packet[8] = 0x40  // TTL (64)
	packet[9] = 0x11  // 协议 (17 = UDP)
	packet[10] = 0x00 // 头部校验和高字节
	packet[11] = 0x00 // 头部校验和低字节
	// 源IP: ********
	packet[12] = 10
	packet[13] = 1
	packet[14] = 0
	packet[15] = 2
	// 目标IP: *******
	packet[16] = 8
	packet[17] = 8
	packet[18] = 8
	packet[19] = 8

	// UDP头部
	packet[20] = 0x04 // 源端口高字节 (1024)
	packet[21] = 0x00 // 源端口低字节
	packet[22] = 0x00 // 目标端口高字节 (53)
	packet[23] = 0x35 // 目标端口低字节
	packet[24] = 0x00 // 长度高字节 (12字节)
	packet[25] = 0x0C // 长度低字节
	packet[26] = 0x00 // 校验和高字节
	packet[27] = 0x00 // 校验和低字节

	// UDP数据
	packet[28] = 0x74 // 't'
	packet[29] = 0x65 // 'e'
	packet[30] = 0x73 // 's'
	packet[31] = 0x74 // 't'

	return packet
}
