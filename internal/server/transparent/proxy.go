package transparent

import (
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// DefaultTransparentProxy 默认透明代理实现
type DefaultTransparentProxy struct {
	config *TransparentProxyConfig
	logger *zap.Logger

	// 核心组件
	parser         PacketParser
	addressMapping AddressMapping
	rawForwarder   RawSocketForwarder

	// 指标统计
	metrics *ForwardingMetrics
	mu      sync.RWMutex

	// 生命周期管理
	closed bool
}

// NewTransparentProxy 创建新的透明代理
func NewTransparentProxy(config *TransparentProxyConfig, logger *zap.Logger) (TransparentProxy, error) {
	if config == nil {
		config = DefaultConfig()
	}

	proxy := &DefaultTransparentProxy{
		config:         config,
		logger:         logger,
		parser:         NewPacketParser(config),
		addressMapping: NewAddressMapping(),
		rawForwarder:   NewRawSocketForwarder(config, logger),
		metrics:        &ForwardingMetrics{},
	}

	return proxy, nil
}

// ForwardPacket 转发数据包 - 统一的透明转发接口
func (p *DefaultTransparentProxy) ForwardPacket(packet []byte, clientID string) ([]byte, error) {
	startTime := time.Now()

	// 解析数据包
	packetInfo, err := p.parser.ParsePacket(packet)
	if err != nil {
		p.updateMetrics(false, 0, time.Since(startTime), 0)
		return nil, fmt.Errorf("failed to parse packet: %w", err)
	}

	// 特殊地址处理：IPv6多播和链路本地地址
	if p.shouldSkipPacket(packetInfo) {
		p.logger.Debug("Packet skipped due to special address handling",
			zap.String("client_id", clientID),
			zap.String("dst_ip", packetInfo.DstIP.String()),
			zap.String("reason", p.getSkipReason(packetInfo)))
		p.updateMetrics(true, packetInfo.PacketSize, time.Since(startTime), packetInfo.Protocol)
		return nil, nil // 不是错误，只是跳过处理
	}

	// 记录地址映射
	p.addressMapping.SetMapping(packetInfo.SrcIP.String(), clientID, p.config.AddressMappingTTL)

	// 调试日志
	if p.config.LogPacketInfo {
		p.logger.Debug("🚀 Transparent Proxy - Unified Forwarding",
			zap.String("client_id", clientID),
			zap.String("src_ip", packetInfo.SrcIP.String()),
			zap.String("dst_ip", packetInfo.DstIP.String()),
			zap.Uint8("protocol", packetInfo.Protocol),
			zap.Uint16("src_port", packetInfo.SrcPort),
			zap.Uint16("dst_port", packetInfo.DstPort),
			zap.Int("packet_size", packetInfo.PacketSize),
			zap.String("forwarding_mode", "raw_socket_transparent"))
	}

	// 使用原始套接字转发
	responseData, err := p.rawForwarder.ForwardRaw(packet, packetInfo.DstIP, packetInfo.Protocol)
	if err != nil {
		p.updateMetrics(false, packetInfo.PacketSize, time.Since(startTime), packetInfo.Protocol)
		return nil, fmt.Errorf("failed to forward packet: %w", err)
	}

	// 如果没有响应数据，直接返回
	if len(responseData) == 0 {
		p.updateMetrics(true, packetInfo.PacketSize, time.Since(startTime), packetInfo.Protocol)
		return nil, nil
	}

	// 构造响应数据包
	responsePacket, err := p.buildResponsePacket(packetInfo, responseData, clientID)
	if err != nil {
		p.updateMetrics(false, packetInfo.PacketSize, time.Since(startTime), packetInfo.Protocol)
		return nil, fmt.Errorf("failed to build response packet: %w", err)
	}

	p.updateMetrics(true, packetInfo.PacketSize, time.Since(startTime), packetInfo.Protocol)

	if p.config.LogPacketInfo {
		p.logger.Debug("✅ Transparent Proxy - Response Generated",
			zap.String("client_id", clientID),
			zap.Int("response_size", len(responsePacket)),
			zap.Duration("latency", time.Since(startTime)))
	}

	return responsePacket, nil
}

// buildResponsePacket 构造响应数据包
func (p *DefaultTransparentProxy) buildResponsePacket(originalPacket *PacketInfo, responseData []byte, clientID string) ([]byte, error) {
	// 获取原始客户端IP的映射
	originalClientIP := originalPacket.SrcIP.String()
	mappedClientID, exists := p.addressMapping.GetMapping(originalClientIP)
	if !exists || mappedClientID != clientID {
		return nil, fmt.Errorf("address mapping not found for client %s (original IP: %s)", clientID, originalClientIP)
	}

	// 构造响应包：源IP是目标服务器，目标IP是原始客户端
	responsePacket, err := p.parser.BuildResponsePacket(
		originalPacket,
		responseData,
		originalPacket.DstIP, // 响应的源IP是原始请求的目标IP
		originalPacket.SrcIP, // 响应的目标IP是原始请求的源IP
	)
	if err != nil {
		return nil, fmt.Errorf("failed to build response packet: %w", err)
	}

	return responsePacket, nil
}

// GetMetrics 获取转发指标
func (p *DefaultTransparentProxy) GetMetrics() *ForwardingMetrics {
	p.mu.RLock()
	defer p.mu.RUnlock()

	// 复制指标以避免并发访问问题
	metrics := *p.metrics
	return &metrics
}

// updateMetrics 更新指标
func (p *DefaultTransparentProxy) updateMetrics(success bool, bytes int, latency time.Duration, protocol uint8) {
	p.mu.Lock()
	defer p.mu.Unlock()

	if success {
		p.metrics.PacketsForwarded++
		p.metrics.BytesForwarded += int64(bytes)

		// 更新协议统计
		switch protocol {
		case 1, 58: // ICMP/ICMPv6
			p.metrics.ICMPPackets++
		case 6: // TCP
			p.metrics.TCPPackets++
		case 17: // UDP
			p.metrics.UDPPackets++
		}

		// 更新平均延迟
		if p.metrics.PacketsForwarded == 1 {
			p.metrics.AverageLatency = latency
		} else {
			p.metrics.AverageLatency = (p.metrics.AverageLatency + latency) / 2
		}

		p.metrics.LastForwardTime = time.Now()
	} else {
		p.metrics.PacketsDropped++
		p.metrics.ForwardingErrors++
	}
}

// Close 关闭透明代理
func (p *DefaultTransparentProxy) Close() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.closed {
		return nil
	}

	p.logger.Info("Closing transparent proxy")

	// 关闭各个组件
	if err := p.rawForwarder.Close(); err != nil {
		p.logger.Error("Failed to close raw forwarder", zap.Error(err))
	}

	if am, ok := p.addressMapping.(*DefaultAddressMapping); ok {
		if err := am.Close(); err != nil {
			p.logger.Error("Failed to close address mapping", zap.Error(err))
		}
	}

	p.closed = true
	p.logger.Info("Transparent proxy closed successfully")

	return nil
}

// GetAddressMappingStats 获取地址映射统计
func (p *DefaultTransparentProxy) GetAddressMappingStats() map[string]interface{} {
	return p.addressMapping.GetStats()
}

// IsHealthy 检查透明代理健康状态
func (p *DefaultTransparentProxy) IsHealthy() bool {
	p.mu.RLock()
	defer p.mu.RUnlock()

	return !p.closed
}

// GetProtocolStats 获取协议统计
func (p *DefaultTransparentProxy) GetProtocolStats() map[string]int64 {
	p.mu.RLock()
	defer p.mu.RUnlock()

	return map[string]int64{
		"icmp": p.metrics.ICMPPackets,
		"tcp":  p.metrics.TCPPackets,
		"udp":  p.metrics.UDPPackets,
	}
}

// shouldSkipPacket 检查是否应该跳过数据包处理
func (p *DefaultTransparentProxy) shouldSkipPacket(packetInfo *PacketInfo) bool {
	// IPv6多播地址处理
	if packetInfo.DstIP.To16() != nil && packetInfo.DstIP.IsMulticast() {
		return true
	}

	// IPv6链路本地地址处理
	if packetInfo.DstIP.To16() != nil && packetInfo.DstIP.IsLinkLocalUnicast() {
		return true
	}

	return false
}

// getSkipReason 获取跳过数据包的原因
func (p *DefaultTransparentProxy) getSkipReason(packetInfo *PacketInfo) string {
	if packetInfo.DstIP.To16() != nil && packetInfo.DstIP.IsMulticast() {
		return "IPv6_multicast_address"
	}

	if packetInfo.DstIP.To16() != nil && packetInfo.DstIP.IsLinkLocalUnicast() {
		return "IPv6_link_local_address"
	}

	return "unknown"
}
