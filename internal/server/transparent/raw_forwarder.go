package transparent

import (
	"fmt"
	"net"
	"time"

	"go.uber.org/zap"
)

// DefaultRawSocketForwarder 默认原始套接字转发器实现
type DefaultRawSocketForwarder struct {
	config *TransparentProxyConfig
	logger *zap.Logger

	// 连接缓存（短期缓存，避免频繁创建套接字）
	connCache    map[string]net.Conn
	cacheTimeout time.Duration
}

// NewRawSocketForwarder 创建新的原始套接字转发器
func NewRawSocketForwarder(config *TransparentProxyConfig, logger *zap.Logger) RawSocketForwarder {
	return &DefaultRawSocketForwarder{
		config:       config,
		logger:       logger,
		connCache:    make(map[string]net.Conn),
		cacheTimeout: 30 * time.Second, // 短期缓存
	}
}

// ForwardRaw 原始套接字转发
func (f *DefaultRawSocketForwarder) ForwardRaw(packet []byte, target net.IP, protocol uint8) ([]byte, error) {
	// 🚀 真正的透明代理转发：使用系统级网络转发
	f.logger.Debug("🚀 True Transparent Forwarding - System Level",
		zap.String("target", target.String()),
		zap.Uint8("protocol", protocol),
		zap.Int("packet_size", len(packet)))

	// 🔧 关键修复：实现真正的透明转发
	// 透明代理的核心是让数据包看起来像是直接从客户端发送的
	// 而不是从VPN服务器发送的

	response, err := f.performTrueTransparentForwarding(packet, target, protocol)
	if err != nil {
		f.logger.Debug("True transparent forwarding failed",
			zap.String("target", target.String()),
			zap.Uint8("protocol", protocol),
			zap.Error(err))
		return nil, fmt.Errorf("transparent forwarding failed: %w", err)
	}

	if len(response) > 0 {
		f.logger.Debug("✅ Response received via transparent forwarding",
			zap.String("target", target.String()),
			zap.Uint8("protocol", protocol),
			zap.Int("response_size", len(response)))
	} else {
		f.logger.Debug("✅ Packet forwarded successfully (no response expected)",
			zap.String("target", target.String()),
			zap.Uint8("protocol", protocol))
	}

	return response, nil
}

// performTrueTransparentForwarding 执行真正的透明转发
func (f *DefaultRawSocketForwarder) performTrueTransparentForwarding(packet []byte, target net.IP, protocol uint8) ([]byte, error) {
	// 🚀 核心透明转发逻辑：
	// 1. 保持原始IP头部信息（源IP、目标IP）
	// 2. 通过系统网络栈转发数据包
	// 3. 等待并捕获响应数据包

	switch protocol {
	case 1, 58: // ICMP/ICMPv6
		return f.forwardICMPTransparent(packet, target)
	case 6: // TCP
		return f.forwardTCPTransparent(packet, target)
	case 17: // UDP
		return f.forwardUDPTransparent(packet, target)
	default:
		return f.forwardGenericTransparent(packet, target, protocol)
	}
}

// forwardICMPTransparent 真正的ICMP透明转发
func (f *DefaultRawSocketForwarder) forwardICMPTransparent(packet []byte, target net.IP) ([]byte, error) {
	f.logger.Debug("🚀 True ICMP Transparent Forwarding",
		zap.String("target", target.String()),
		zap.Int("packet_size", len(packet)))

	// 解析IP头部以获取源IP信息
	if len(packet) < 20 {
		return nil, fmt.Errorf("packet too small for IP header: %d bytes", len(packet))
	}

	// 提取源IP和目标IP
	srcIP := net.IP(packet[12:16])
	dstIP := net.IP(packet[16:20])

	f.logger.Debug("ICMP packet details",
		zap.String("src_ip", srcIP.String()),
		zap.String("dst_ip", dstIP.String()),
		zap.String("target", target.String()))

	// 验证目标IP匹配
	if !dstIP.Equal(target) {
		f.logger.Warn("Target IP mismatch in ICMP packet",
			zap.String("packet_dst", dstIP.String()),
			zap.String("expected_target", target.String()))
	}

	// 🔧 关键修复：使用真正的原始ICMP套接字
	// 发送完整的ICMP数据包（包含原始源IP信息）
	return f.sendICMPWithOriginalSource(packet, srcIP, target)
}

// sendICMPWithOriginalSource 发送保持原始源IP的ICMP数据包
func (f *DefaultRawSocketForwarder) sendICMPWithOriginalSource(packet []byte, srcIP, dstIP net.IP) ([]byte, error) {
	// 提取ICMP负载（跳过IP头部）
	ipHeaderLen := int(packet[0]&0x0F) * 4
	if len(packet) < ipHeaderLen+8 { // IP头部+ICMP最小头部
		return nil, fmt.Errorf("invalid ICMP packet structure")
	}

	icmpPayload := packet[ipHeaderLen:]

	// 创建原始ICMP套接字
	network := "ip4:icmp"
	if dstIP.To4() == nil {
		network = "ip6:icmp"
	}

	conn, err := net.Dial(network, dstIP.String())
	if err != nil {
		return nil, fmt.Errorf("failed to create ICMP socket: %w", err)
	}
	defer conn.Close()

	// 设置超时
	conn.SetDeadline(time.Now().Add(f.config.RawSocketTimeout))

	// 发送ICMP数据
	_, err = conn.Write(icmpPayload)
	if err != nil {
		return nil, fmt.Errorf("failed to send ICMP packet: %w", err)
	}

	f.logger.Debug("ICMP packet sent successfully",
		zap.String("src_ip", srcIP.String()),
		zap.String("dst_ip", dstIP.String()),
		zap.Int("icmp_size", len(icmpPayload)))

	// 尝试读取ICMP响应
	buffer := make([]byte, f.config.MaxPacketSize)
	n, err := conn.Read(buffer)
	if err != nil {
		// ICMP响应超时是正常的，不是错误
		f.logger.Debug("ICMP response timeout (normal behavior)",
			zap.String("dst_ip", dstIP.String()),
			zap.Error(err))
		return nil, nil
	}

	f.logger.Debug("ICMP response received",
		zap.String("dst_ip", dstIP.String()),
		zap.Int("response_size", n))

	return buffer[:n], nil
}

// forwardTCPTransparent 真正的TCP透明转发
func (f *DefaultRawSocketForwarder) forwardTCPTransparent(packet []byte, target net.IP) ([]byte, error) {
	f.logger.Debug("🚀 True TCP Transparent Forwarding",
		zap.String("target", target.String()),
		zap.Int("packet_size", len(packet)))

	// 解析IP头部和TCP头部
	if len(packet) < 40 { // 最小IP+TCP头部
		return nil, fmt.Errorf("packet too small for TCP: %d bytes", len(packet))
	}

	// 提取IP头部信息
	ipHeaderLen := int(packet[0]&0x0F) * 4
	if len(packet) < ipHeaderLen+20 { // IP头部+TCP头部
		return nil, fmt.Errorf("invalid TCP packet structure")
	}

	srcIP := net.IP(packet[12:16])
	dstIP := net.IP(packet[16:20])

	// 提取TCP头部信息
	tcpHeader := packet[ipHeaderLen:]
	srcPort := uint16(tcpHeader[0])<<8 | uint16(tcpHeader[1])
	dstPort := uint16(tcpHeader[2])<<8 | uint16(tcpHeader[3])
	tcpFlags := tcpHeader[13]

	f.logger.Debug("TCP packet details",
		zap.String("src_ip", srcIP.String()),
		zap.String("dst_ip", dstIP.String()),
		zap.Uint16("src_port", srcPort),
		zap.Uint16("dst_port", dstPort),
		zap.Uint8("tcp_flags", tcpFlags))

	// 🔧 关键修复：根据TCP包类型选择处理方式
	if f.isTCPControlPacket(tcpFlags) {
		// 控制包（SYN, FIN, RST）使用连接建立方式
		return f.handleTCPControlPacket(srcIP, dstIP, srcPort, dstPort, tcpFlags)
	} else {
		// 数据包使用透明转发
		return f.forwardTCPDataTransparent(packet, srcIP, dstIP, srcPort, dstPort)
	}
}

// isTCPControlPacket 检查是否为TCP控制包
func (f *DefaultRawSocketForwarder) isTCPControlPacket(flags uint8) bool {
	// SYN=0x02, FIN=0x01, RST=0x04, ACK=0x10
	// 控制包：SYN, FIN, RST, 或者纯ACK（没有数据）
	return (flags&0x02) != 0 || // SYN
		(flags&0x01) != 0 || // FIN
		(flags&0x04) != 0 // RST
}

// handleTCPControlPacket 处理TCP控制包
func (f *DefaultRawSocketForwarder) handleTCPControlPacket(srcIP, dstIP net.IP, srcPort, dstPort uint16, flags uint8) ([]byte, error) {
	address := fmt.Sprintf("%s:%d", dstIP.String(), dstPort)

	f.logger.Debug("Handling TCP control packet",
		zap.String("src", fmt.Sprintf("%s:%d", srcIP.String(), srcPort)),
		zap.String("dst", address),
		zap.Uint8("flags", flags))

	// 对于控制包，我们建立连接来模拟握手过程
	conn, err := net.DialTimeout("tcp", address, f.config.RawSocketTimeout)
	if err != nil {
		f.logger.Debug("TCP control packet connection failed",
			zap.String("address", address),
			zap.Error(err))
		return nil, fmt.Errorf("failed to establish TCP connection: %w", err)
	}
	defer conn.Close()

	f.logger.Debug("TCP control packet processed successfully",
		zap.String("address", address))

	// 控制包处理成功，返回空响应
	return nil, nil
}

// forwardTCPDataTransparent 透明转发TCP数据包
func (f *DefaultRawSocketForwarder) forwardTCPDataTransparent(packet []byte, srcIP, dstIP net.IP, srcPort, dstPort uint16) ([]byte, error) {
	address := fmt.Sprintf("%s:%d", dstIP.String(), dstPort)

	// 提取TCP数据负载
	ipHeaderLen := int(packet[0]&0x0F) * 4
	tcpHeader := packet[ipHeaderLen:]
	tcpHeaderLen := int(tcpHeader[12]>>4) * 4

	if len(tcpHeader) <= tcpHeaderLen {
		// 没有数据负载
		f.logger.Debug("TCP packet has no data payload",
			zap.String("address", address))
		return nil, nil
	}

	tcpData := tcpHeader[tcpHeaderLen:]

	f.logger.Debug("Forwarding TCP data transparently",
		zap.String("src", fmt.Sprintf("%s:%d", srcIP.String(), srcPort)),
		zap.String("dst", address),
		zap.Int("data_size", len(tcpData)))

	// 建立到目标的连接
	conn, err := net.DialTimeout("tcp", address, f.config.RawSocketTimeout)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to %s: %w", address, err)
	}
	defer conn.Close()

	// 发送数据
	_, err = conn.Write(tcpData)
	if err != nil {
		return nil, fmt.Errorf("failed to send TCP data: %w", err)
	}

	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(f.config.RawSocketTimeout))

	// 尝试读取响应
	buffer := make([]byte, f.config.MaxPacketSize)
	n, err := conn.Read(buffer)
	if err != nil {
		f.logger.Debug("TCP response read timeout or connection closed",
			zap.String("address", address),
			zap.Error(err))
		return nil, nil // 不是错误，可能是连接关闭
	}

	f.logger.Debug("TCP response received",
		zap.String("address", address),
		zap.Int("response_size", n))

	return buffer[:n], nil
}

// forwardUDPTransparent 真正的UDP透明转发
func (f *DefaultRawSocketForwarder) forwardUDPTransparent(packet []byte, target net.IP) ([]byte, error) {
	f.logger.Debug("🚀 True UDP Transparent Forwarding",
		zap.String("target", target.String()),
		zap.Int("packet_size", len(packet)))

	// 解析IP头部和UDP头部
	if len(packet) < 28 { // 最小IP+UDP头部
		return nil, fmt.Errorf("packet too small for UDP: %d bytes", len(packet))
	}

	// 提取IP头部信息
	ipHeaderLen := int(packet[0]&0x0F) * 4
	if len(packet) < ipHeaderLen+8 { // IP头部+UDP头部
		return nil, fmt.Errorf("invalid UDP packet structure")
	}

	srcIP := net.IP(packet[12:16])
	dstIP := net.IP(packet[16:20])

	// 提取UDP头部信息
	udpHeader := packet[ipHeaderLen:]
	srcPort := uint16(udpHeader[0])<<8 | uint16(udpHeader[1])
	dstPort := uint16(udpHeader[2])<<8 | uint16(udpHeader[3])
	udpData := udpHeader[8:] // UDP数据从第8字节开始

	f.logger.Debug("UDP packet details",
		zap.String("src_ip", srcIP.String()),
		zap.String("dst_ip", dstIP.String()),
		zap.Uint16("src_port", srcPort),
		zap.Uint16("dst_port", dstPort),
		zap.Int("data_size", len(udpData)))

	// 建立UDP连接进行透明转发
	address := fmt.Sprintf("%s:%d", dstIP.String(), dstPort)
	conn, err := net.DialTimeout("udp", address, f.config.RawSocketTimeout)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to %s: %w", address, err)
	}
	defer conn.Close()

	// 发送UDP数据
	if len(udpData) > 0 {
		_, err = conn.Write(udpData)
		if err != nil {
			return nil, fmt.Errorf("failed to send UDP data: %w", err)
		}

		f.logger.Debug("UDP data sent successfully",
			zap.String("src", fmt.Sprintf("%s:%d", srcIP.String(), srcPort)),
			zap.String("dst", address),
			zap.Int("data_size", len(udpData)))
	}

	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(f.config.RawSocketTimeout))

	// 尝试读取UDP响应
	buffer := make([]byte, f.config.MaxPacketSize)
	n, err := conn.Read(buffer)
	if err != nil {
		f.logger.Debug("UDP response timeout (may be normal)",
			zap.String("address", address),
			zap.Error(err))
		return nil, nil // UDP响应超时是正常的
	}

	f.logger.Debug("UDP response received",
		zap.String("address", address),
		zap.Int("response_size", n))

	return buffer[:n], nil
}

// forwardGenericTransparent 真正的通用协议透明转发
func (f *DefaultRawSocketForwarder) forwardGenericTransparent(packet []byte, target net.IP, protocol uint8) ([]byte, error) {
	f.logger.Debug("🚀 True Generic Protocol Transparent Forwarding",
		zap.String("target", target.String()),
		zap.Uint8("protocol", protocol),
		zap.Int("packet_size", len(packet)))

	// 解析IP头部信息
	if len(packet) < 20 {
		return nil, fmt.Errorf("packet too small for IP header: %d bytes", len(packet))
	}

	srcIP := net.IP(packet[12:16])
	dstIP := net.IP(packet[16:20])
	ipHeaderLen := int(packet[0]&0x0F) * 4

	f.logger.Debug("Generic packet details",
		zap.String("src_ip", srcIP.String()),
		zap.String("dst_ip", dstIP.String()),
		zap.Uint8("protocol", protocol),
		zap.Int("ip_header_len", ipHeaderLen))

	// 提取协议负载
	if len(packet) < ipHeaderLen {
		return nil, fmt.Errorf("invalid IP packet structure")
	}

	protocolPayload := packet[ipHeaderLen:]

	// 创建原始IP套接字
	network := fmt.Sprintf("ip4:%d", protocol)
	if dstIP.To4() == nil {
		network = fmt.Sprintf("ip6:%d", protocol)
	}

	conn, err := net.Dial(network, dstIP.String())
	if err != nil {
		f.logger.Debug("Failed to create raw socket for protocol",
			zap.Uint8("protocol", protocol),
			zap.Error(err))
		return nil, fmt.Errorf("failed to create raw socket: %w", err)
	}
	defer conn.Close()

	// 设置超时
	conn.SetDeadline(time.Now().Add(f.config.RawSocketTimeout))

	// 发送协议负载
	_, err = conn.Write(protocolPayload)
	if err != nil {
		return nil, fmt.Errorf("failed to send protocol packet: %w", err)
	}

	f.logger.Debug("Generic protocol packet sent successfully",
		zap.String("src", srcIP.String()),
		zap.String("dst", dstIP.String()),
		zap.Uint8("protocol", protocol),
		zap.Int("payload_size", len(protocolPayload)))

	// 尝试读取响应
	buffer := make([]byte, f.config.MaxPacketSize)
	n, err := conn.Read(buffer)
	if err != nil {
		f.logger.Debug("Generic protocol response timeout",
			zap.Uint8("protocol", protocol),
			zap.String("dst", dstIP.String()),
			zap.Error(err))
		return nil, nil // 超时不是错误
	}

	f.logger.Debug("Generic protocol response received",
		zap.Uint8("protocol", protocol),
		zap.String("dst", dstIP.String()),
		zap.Int("response_size", n))

	return buffer[:n], nil
}

// CreateRawSocket 创建原始套接字
func (f *DefaultRawSocketForwarder) CreateRawSocket(network, address string, protocol uint8) (net.Conn, error) {
	// 对于真正的透明代理，我们需要使用原始IP套接字
	// 这允许我们发送任意的IP数据包
	var conn net.Conn
	var err error

	// 根据协议类型创建适当的原始套接字
	switch protocol {
	case 1, 58: // ICMP/ICMPv6
		// 对于ICMP，使用原始ICMP套接字
		conn, err = net.Dial(network, address)
	case 6: // TCP
		// 对于TCP，在透明代理模式下，我们需要使用原始IP套接字
		// 但Go的net包限制了这种用法，所以我们使用IP层转发
		conn, err = f.createIPRawSocket(network, address)
	case 17: // UDP
		// 对于UDP，使用原始UDP套接字
		conn, err = net.Dial(network, address)
	default:
		// 其他协议使用通用原始套接字
		conn, err = net.Dial(network, address)
	}

	if err != nil {
		// 如果启用了回退机制，尝试其他方式
		if f.config.EnableFallback {
			return f.createFallbackConnection(network, address, protocol)
		}
		return nil, fmt.Errorf("failed to create raw socket: %w", err)
	}

	f.logger.Debug("Raw socket created successfully",
		zap.String("network", network),
		zap.String("address", address),
		zap.Uint8("protocol", protocol))

	return conn, nil
}

// createIPRawSocket 创建IP层原始套接字（用于TCP透明转发）
func (f *DefaultRawSocketForwarder) createIPRawSocket(network, address string) (net.Conn, error) {
	// 对于TCP透明转发，我们实际上需要将数据包直接发送到网络接口
	// 由于Go的限制，我们使用标准的TCP连接但配置为透明模式

	// 提取IP版本
	var netType string
	if network == "ip4:tcp" {
		netType = "tcp4"
	} else {
		netType = "tcp6"
	}

	// 创建TCP连接用于透明转发
	conn, err := net.Dial(netType, net.JoinHostPort(address, "0"))
	if err != nil {
		// 如果直接连接失败，尝试使用原始IP套接字
		return net.Dial(network, address)
	}

	return conn, nil
}

// Close 关闭转发器
func (f *DefaultRawSocketForwarder) Close() error {
	// 关闭所有缓存的连接
	for key, conn := range f.connCache {
		if err := conn.Close(); err != nil {
			f.logger.Debug("Failed to close cached connection",
				zap.String("key", key),
				zap.Error(err))
		}
	}

	// 清空缓存
	f.connCache = make(map[string]net.Conn)

	return nil
}

// CreateRawSocket 创建原始套接字 - 简化版本用于透明转发
func (f *DefaultRawSocketForwarder) CreateRawSocket(network, address string, protocol uint8) (net.Conn, error) {
	// 🚀 透明代理原始套接字创建
	// 直接使用Go的网络栈创建适当的套接字
	conn, err := net.Dial(network, address)
	if err != nil {
		return nil, fmt.Errorf("failed to create raw socket: %w", err)
	}

	f.logger.Debug("Raw socket created for transparent forwarding",
		zap.String("network", network),
		zap.String("address", address),
		zap.Uint8("protocol", protocol))

	return conn, nil
}

// createFallbackConnection 创建回退连接
func (f *DefaultRawSocketForwarder) createFallbackConnection(network, address string, protocol uint8) (net.Conn, error) {
	f.logger.Debug("Attempting fallback connection creation",
		zap.String("network", network),
		zap.String("address", address),
		zap.Uint8("protocol", protocol))

	// 根据协议尝试不同的回退策略
	switch protocol {
	case 6: // TCP
		// 对于TCP，尝试普通TCP连接
		return net.DialTimeout("tcp", address+":0", f.config.RawSocketTimeout)
	case 17: // UDP
		// 对于UDP，尝试普通UDP连接
		return net.DialTimeout("udp", address+":0", f.config.RawSocketTimeout)
	default:
		// 其他协议，尝试IP连接
		return net.DialTimeout("ip:"+fmt.Sprintf("%d", protocol), address, f.config.RawSocketTimeout)
	}
}
