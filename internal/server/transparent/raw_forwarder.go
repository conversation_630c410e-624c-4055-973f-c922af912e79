package transparent

import (
	"fmt"
	"net"
	"time"

	"go.uber.org/zap"
)

// DefaultRawSocketForwarder 默认原始套接字转发器实现
type DefaultRawSocketForwarder struct {
	config *TransparentProxyConfig
	logger *zap.Logger

	// 连接缓存（短期缓存，避免频繁创建套接字）
	connCache    map[string]net.Conn
	cacheTimeout time.Duration
}

// NewRawSocketForwarder 创建新的原始套接字转发器
func NewRawSocketForwarder(config *TransparentProxyConfig, logger *zap.Logger) RawSocketForwarder {
	return &DefaultRawSocketForwarder{
		config:       config,
		logger:       logger,
		connCache:    make(map[string]net.Conn),
		cacheTimeout: 30 * time.Second, // 短期缓存
	}
}

// ForwardRaw 原始套接字转发
func (f *DefaultRawSocketForwarder) ForwardRaw(packet []byte, target net.IP, protocol uint8) ([]byte, error) {
	// 🚀 真正的透明代理转发：直接转发IP数据包到目标
	f.logger.Debug("🚀 Transparent Raw Socket Forwarding",
		zap.String("target", target.String()),
		zap.Uint8("protocol", protocol),
		zap.Int("packet_size", len(packet)))

	// 对于透明代理，我们需要将数据包转发到实际的网络接口
	// 而不是创建新的连接
	err := f.forwardPacketToInterface(packet, target, protocol)
	if err != nil {
		f.logger.Debug("Failed to forward packet to interface",
			zap.String("target", target.String()),
			zap.Uint8("protocol", protocol),
			zap.Error(err))
		return nil, fmt.Errorf("failed to forward packet: %w", err)
	}

	f.logger.Debug("✅ Packet forwarded successfully via transparent proxy",
		zap.String("target", target.String()),
		zap.Uint8("protocol", protocol),
		zap.Int("bytes_sent", len(packet)))

	// 对于透明代理，我们不等待响应，因为响应会通过正常的路由返回
	// 这是透明代理的核心特性：数据包看起来像是直接从客户端发送的
	return nil, nil
}

// forwardPacketToInterface 将数据包转发到网络接口
func (f *DefaultRawSocketForwarder) forwardPacketToInterface(packet []byte, target net.IP, protocol uint8) error {
	// 🚀 核心透明转发逻辑：
	// 1. 解析IP数据包头部
	// 2. 确保目标地址正确
	// 3. 通过系统路由表转发数据包

	// 简化的实现：使用Go的网络栈进行转发
	// 在生产环境中，这里应该使用更底层的网络接口操作

	// 根据协议类型选择转发方式
	switch protocol {
	case 1, 58: // ICMP/ICMPv6
		return f.forwardICMPPacket(packet, target)
	case 6: // TCP
		return f.forwardTCPPacket(packet, target)
	case 17: // UDP
		return f.forwardUDPPacket(packet, target)
	default:
		return f.forwardGenericPacket(packet, target, protocol)
	}
}

// forwardICMPPacket 转发ICMP数据包
func (f *DefaultRawSocketForwarder) forwardICMPPacket(packet []byte, target net.IP) error {
	// 对于ICMP，我们可以使用原始套接字
	network := "ip4:icmp"
	if target.To4() == nil {
		network = "ip6:icmp"
	}

	conn, err := net.Dial(network, target.String())
	if err != nil {
		return fmt.Errorf("failed to create ICMP socket: %w", err)
	}
	defer conn.Close()

	// 提取ICMP负载（跳过IP头部）
	icmpPayload := packet[20:] // 假设标准IPv4头部20字节

	_, err = conn.Write(icmpPayload)
	return err
}

// forwardTCPPacket 转发TCP数据包
func (f *DefaultRawSocketForwarder) forwardTCPPacket(packet []byte, target net.IP) error {
	// 🚀 TCP透明转发的关键实现
	f.logger.Debug("🚀 TCP Transparent Forwarding",
		zap.String("target", target.String()),
		zap.Int("packet_size", len(packet)))

	// 解析TCP数据包以获取端口信息
	if len(packet) < 40 { // 最小IP+TCP头部
		return fmt.Errorf("packet too small for TCP: %d bytes", len(packet))
	}

	// 提取目标端口（TCP头部偏移16-17字节，但需要考虑IP头部长度）
	ipHeaderLen := int(packet[0]&0x0F) * 4
	if len(packet) < ipHeaderLen+20 { // IP头部+TCP头部
		return fmt.Errorf("invalid TCP packet structure")
	}

	tcpHeader := packet[ipHeaderLen:]
	dstPort := uint16(tcpHeader[2])<<8 | uint16(tcpHeader[3])

	f.logger.Debug("TCP packet details",
		zap.String("target", target.String()),
		zap.Uint16("dst_port", dstPort),
		zap.Int("ip_header_len", ipHeaderLen))

	// 使用标准TCP连接进行转发（模拟透明代理行为）
	return f.forwardTCPViaConnection(packet[ipHeaderLen:], target, dstPort)
}

// forwardTCPViaConnection 通过TCP连接转发数据
func (f *DefaultRawSocketForwarder) forwardTCPViaConnection(tcpPayload []byte, target net.IP, port uint16) error {
	address := fmt.Sprintf("%s:%d", target.String(), port)

	// 创建到目标的TCP连接
	conn, err := net.DialTimeout("tcp", address, f.config.RawSocketTimeout)
	if err != nil {
		return fmt.Errorf("failed to connect to %s: %w", address, err)
	}
	defer conn.Close()

	// 提取TCP数据负载（跳过TCP头部）
	if len(tcpPayload) < 20 {
		return fmt.Errorf("TCP payload too small")
	}

	tcpHeaderLen := int(tcpPayload[12]>>4) * 4
	if len(tcpPayload) <= tcpHeaderLen {
		// 没有数据负载，这可能是控制包（SYN, ACK等）
		f.logger.Debug("TCP control packet detected, connection established",
			zap.String("target", address))
		return nil
	}

	data := tcpPayload[tcpHeaderLen:]
	if len(data) > 0 {
		// 发送数据
		_, err = conn.Write(data)
		if err != nil {
			return fmt.Errorf("failed to write TCP data: %w", err)
		}

		f.logger.Debug("TCP data forwarded successfully",
			zap.String("target", address),
			zap.Int("data_size", len(data)))
	}

	return nil
}

// forwardUDPPacket 转发UDP数据包
func (f *DefaultRawSocketForwarder) forwardUDPPacket(packet []byte, target net.IP) error {
	f.logger.Debug("🚀 UDP Transparent Forwarding",
		zap.String("target", target.String()),
		zap.Int("packet_size", len(packet)))

	// 解析UDP数据包
	if len(packet) < 28 { // 最小IP+UDP头部
		return fmt.Errorf("packet too small for UDP: %d bytes", len(packet))
	}

	ipHeaderLen := int(packet[0]&0x0F) * 4
	if len(packet) < ipHeaderLen+8 { // IP头部+UDP头部
		return fmt.Errorf("invalid UDP packet structure")
	}

	udpHeader := packet[ipHeaderLen:]
	dstPort := uint16(udpHeader[2])<<8 | uint16(udpHeader[3])
	udpData := udpHeader[8:] // UDP数据从第8字节开始

	// 使用UDP连接转发
	address := fmt.Sprintf("%s:%d", target.String(), dstPort)
	conn, err := net.DialTimeout("udp", address, f.config.RawSocketTimeout)
	if err != nil {
		return fmt.Errorf("failed to connect to %s: %w", address, err)
	}
	defer conn.Close()

	if len(udpData) > 0 {
		_, err = conn.Write(udpData)
		if err != nil {
			return fmt.Errorf("failed to write UDP data: %w", err)
		}

		f.logger.Debug("UDP data forwarded successfully",
			zap.String("target", address),
			zap.Int("data_size", len(udpData)))
	}

	return nil
}

// forwardGenericPacket 转发通用协议数据包
func (f *DefaultRawSocketForwarder) forwardGenericPacket(packet []byte, target net.IP, protocol uint8) error {
	f.logger.Debug("🚀 Generic Protocol Forwarding",
		zap.String("target", target.String()),
		zap.Uint8("protocol", protocol),
		zap.Int("packet_size", len(packet)))

	// 对于未知协议，我们尝试使用原始IP套接字
	network := fmt.Sprintf("ip4:%d", protocol)
	if target.To4() == nil {
		network = fmt.Sprintf("ip6:%d", protocol)
	}

	conn, err := net.Dial(network, target.String())
	if err != nil {
		f.logger.Debug("Failed to create raw socket for protocol",
			zap.Uint8("protocol", protocol),
			zap.Error(err))
		return fmt.Errorf("failed to create raw socket: %w", err)
	}
	defer conn.Close()

	// 发送整个数据包
	_, err = conn.Write(packet)
	if err != nil {
		return fmt.Errorf("failed to write generic packet: %w", err)
	}

	f.logger.Debug("Generic packet forwarded successfully",
		zap.String("target", target.String()),
		zap.Uint8("protocol", protocol))

	return nil
}

// CreateRawSocket 创建原始套接字
func (f *DefaultRawSocketForwarder) CreateRawSocket(network, address string, protocol uint8) (net.Conn, error) {
	// 对于真正的透明代理，我们需要使用原始IP套接字
	// 这允许我们发送任意的IP数据包
	var conn net.Conn
	var err error

	// 根据协议类型创建适当的原始套接字
	switch protocol {
	case 1, 58: // ICMP/ICMPv6
		// 对于ICMP，使用原始ICMP套接字
		conn, err = net.Dial(network, address)
	case 6: // TCP
		// 对于TCP，在透明代理模式下，我们需要使用原始IP套接字
		// 但Go的net包限制了这种用法，所以我们使用IP层转发
		conn, err = f.createIPRawSocket(network, address)
	case 17: // UDP
		// 对于UDP，使用原始UDP套接字
		conn, err = net.Dial(network, address)
	default:
		// 其他协议使用通用原始套接字
		conn, err = net.Dial(network, address)
	}

	if err != nil {
		// 如果启用了回退机制，尝试其他方式
		if f.config.EnableFallback {
			return f.createFallbackConnection(network, address, protocol)
		}
		return nil, fmt.Errorf("failed to create raw socket: %w", err)
	}

	f.logger.Debug("Raw socket created successfully",
		zap.String("network", network),
		zap.String("address", address),
		zap.Uint8("protocol", protocol))

	return conn, nil
}

// createIPRawSocket 创建IP层原始套接字（用于TCP透明转发）
func (f *DefaultRawSocketForwarder) createIPRawSocket(network, address string) (net.Conn, error) {
	// 对于TCP透明转发，我们实际上需要将数据包直接发送到网络接口
	// 由于Go的限制，我们使用标准的TCP连接但配置为透明模式

	// 提取IP版本
	var netType string
	if network == "ip4:tcp" {
		netType = "tcp4"
	} else {
		netType = "tcp6"
	}

	// 创建TCP连接用于透明转发
	conn, err := net.Dial(netType, net.JoinHostPort(address, "0"))
	if err != nil {
		// 如果直接连接失败，尝试使用原始IP套接字
		return net.Dial(network, address)
	}

	return conn, nil
}

// Close 关闭转发器
func (f *DefaultRawSocketForwarder) Close() error {
	// 关闭所有缓存的连接
	for key, conn := range f.connCache {
		if err := conn.Close(); err != nil {
			f.logger.Debug("Failed to close cached connection",
				zap.String("key", key),
				zap.Error(err))
		}
	}

	// 清空缓存
	f.connCache = make(map[string]net.Conn)

	return nil
}

// getNetworkType 根据目标IP和协议获取网络类型
func (f *DefaultRawSocketForwarder) getNetworkType(target net.IP, protocol uint8) string {
	var ipVersion string
	if target.To4() != nil {
		ipVersion = "ip4"
	} else {
		ipVersion = "ip6"
	}

	switch protocol {
	case 1: // ICMP
		return ipVersion + ":icmp"
	case 6: // TCP
		return ipVersion + ":tcp"
	case 17: // UDP
		return ipVersion + ":udp"
	case 58: // ICMPv6
		return ipVersion + ":icmp"
	default:
		return fmt.Sprintf("%s:%d", ipVersion, protocol)
	}
}

// getOrCreateConnection 获取或创建连接
func (f *DefaultRawSocketForwarder) getOrCreateConnection(network, address string, protocol uint8) (net.Conn, error) {
	key := fmt.Sprintf("%s:%s:%d", network, address, protocol)

	// 检查缓存
	if conn, exists := f.connCache[key]; exists {
		// 简单检查连接是否仍然有效
		if f.isConnectionValid(conn) {
			return conn, nil
		}
		// 连接无效，从缓存中移除
		delete(f.connCache, key)
		conn.Close()
	}

	// 创建新连接
	conn, err := f.CreateRawSocket(network, address, protocol)
	if err != nil {
		return nil, err
	}

	// 添加到缓存
	f.connCache[key] = conn

	return conn, nil
}

// removeConnection 移除连接
func (f *DefaultRawSocketForwarder) removeConnection(network, address string, protocol uint8) {
	key := fmt.Sprintf("%s:%s:%d", network, address, protocol)

	if conn, exists := f.connCache[key]; exists {
		conn.Close()
		delete(f.connCache, key)
	}
}

// isConnectionValid 检查连接是否有效
func (f *DefaultRawSocketForwarder) isConnectionValid(conn net.Conn) bool {
	// 简单的连接有效性检查
	// 对于原始套接字，这个检查比较简单
	return conn != nil
}

// createFallbackConnection 创建回退连接
func (f *DefaultRawSocketForwarder) createFallbackConnection(network, address string, protocol uint8) (net.Conn, error) {
	f.logger.Debug("Attempting fallback connection creation",
		zap.String("network", network),
		zap.String("address", address),
		zap.Uint8("protocol", protocol))

	// 根据协议尝试不同的回退策略
	switch protocol {
	case 6: // TCP
		// 对于TCP，尝试普通TCP连接
		return net.DialTimeout("tcp", address+":0", f.config.RawSocketTimeout)
	case 17: // UDP
		// 对于UDP，尝试普通UDP连接
		return net.DialTimeout("udp", address+":0", f.config.RawSocketTimeout)
	default:
		// 其他协议，尝试IP连接
		return net.DialTimeout("ip:"+fmt.Sprintf("%d", protocol), address, f.config.RawSocketTimeout)
	}
}
