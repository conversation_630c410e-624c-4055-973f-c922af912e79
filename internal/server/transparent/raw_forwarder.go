package transparent

import (
	"fmt"
	"net"
	"time"

	"go.uber.org/zap"
)

// DefaultRawSocketForwarder 默认原始套接字转发器实现
type DefaultRawSocketForwarder struct {
	config *TransparentProxyConfig
	logger *zap.Logger

	// 连接缓存（短期缓存，避免频繁创建套接字）
	connCache    map[string]net.Conn
	cacheTimeout time.Duration
}

// NewRawSocketForwarder 创建新的原始套接字转发器
func NewRawSocketForwarder(config *TransparentProxyConfig, logger *zap.Logger) RawSocketForwarder {
	return &DefaultRawSocketForwarder{
		config:       config,
		logger:       logger,
		connCache:    make(map[string]net.Conn),
		cacheTimeout: 30 * time.Second, // 短期缓存
	}
}

// ForwardRaw 原始套接字转发
func (f *DefaultRawSocketForwarder) ForwardRaw(packet []byte, target net.IP, protocol uint8) ([]byte, error) {
	// 根据协议类型选择网络类型
	network := f.getNetworkType(target, protocol)
	address := target.String()

	// 创建或获取原始套接字连接
	conn, err := f.getOrCreateConnection(network, address, protocol)
	if err != nil {
		return nil, fmt.Errorf("failed to create raw socket: %w", err)
	}

	// 设置写超时
	if err := conn.SetWriteDeadline(time.Now().Add(f.config.RawSocketTimeout)); err != nil {
		f.logger.Debug("Failed to set write deadline", zap.Error(err))
	}

	// 发送数据包
	n, err := conn.Write(packet)
	if err != nil {
		f.logger.Debug("Failed to write to raw socket",
			zap.String("target", address),
			zap.Uint8("protocol", protocol),
			zap.Error(err))

		// 移除失效的连接
		f.removeConnection(network, address, protocol)
		return nil, fmt.Errorf("failed to write to raw socket: %w", err)
	}

	f.logger.Debug("Packet sent via raw socket",
		zap.String("target", address),
		zap.Uint8("protocol", protocol),
		zap.Int("bytes_sent", n))

	// 设置读超时
	if err := conn.SetReadDeadline(time.Now().Add(f.config.RawSocketTimeout)); err != nil {
		f.logger.Debug("Failed to set read deadline", zap.Error(err))
	}

	// 读取响应
	buffer := make([]byte, f.config.MaxPacketSize)
	n, err = conn.Read(buffer)
	if err != nil {
		f.logger.Debug("Failed to read from raw socket",
			zap.String("target", address),
			zap.Uint8("protocol", protocol),
			zap.Error(err))

		// 对于某些协议（如ICMP），可能不会有响应，这是正常的
		if protocol == 1 || protocol == 58 { // ICMP/ICMPv6
			return nil, nil // 返回空响应，不是错误
		}

		// 对于TCP协议，原始套接字读取超时是常见的，不应该作为错误处理
		if protocol == 6 { // TCP
			f.logger.Debug("TCP raw socket read timeout - this is expected behavior",
				zap.String("target", address),
				zap.String("reason", "TCP responses are handled by kernel, not raw socket"))
			return nil, nil // 返回空响应，表示数据包已发送
		}

		return nil, fmt.Errorf("failed to read from raw socket: %w", err)
	}

	f.logger.Debug("Response received via raw socket",
		zap.String("target", address),
		zap.Uint8("protocol", protocol),
		zap.Int("bytes_received", n))

	return buffer[:n], nil
}

// CreateRawSocket 创建原始套接字
func (f *DefaultRawSocketForwarder) CreateRawSocket(network, address string, protocol uint8) (net.Conn, error) {
	// 尝试创建原始套接字
	conn, err := net.Dial(network, address)
	if err != nil {
		// 如果启用了回退机制，尝试其他方式
		if f.config.EnableFallback {
			return f.createFallbackConnection(network, address, protocol)
		}
		return nil, fmt.Errorf("failed to create raw socket: %w", err)
	}

	f.logger.Debug("Raw socket created successfully",
		zap.String("network", network),
		zap.String("address", address),
		zap.Uint8("protocol", protocol))

	return conn, nil
}

// Close 关闭转发器
func (f *DefaultRawSocketForwarder) Close() error {
	// 关闭所有缓存的连接
	for key, conn := range f.connCache {
		if err := conn.Close(); err != nil {
			f.logger.Debug("Failed to close cached connection",
				zap.String("key", key),
				zap.Error(err))
		}
	}

	// 清空缓存
	f.connCache = make(map[string]net.Conn)

	return nil
}

// getNetworkType 根据目标IP和协议获取网络类型
func (f *DefaultRawSocketForwarder) getNetworkType(target net.IP, protocol uint8) string {
	var ipVersion string
	if target.To4() != nil {
		ipVersion = "ip4"
	} else {
		ipVersion = "ip6"
	}

	switch protocol {
	case 1: // ICMP
		return ipVersion + ":icmp"
	case 6: // TCP
		return ipVersion + ":tcp"
	case 17: // UDP
		return ipVersion + ":udp"
	case 58: // ICMPv6
		return ipVersion + ":icmp"
	default:
		return fmt.Sprintf("%s:%d", ipVersion, protocol)
	}
}

// getOrCreateConnection 获取或创建连接
func (f *DefaultRawSocketForwarder) getOrCreateConnection(network, address string, protocol uint8) (net.Conn, error) {
	key := fmt.Sprintf("%s:%s:%d", network, address, protocol)

	// 检查缓存
	if conn, exists := f.connCache[key]; exists {
		// 简单检查连接是否仍然有效
		if f.isConnectionValid(conn) {
			return conn, nil
		}
		// 连接无效，从缓存中移除
		delete(f.connCache, key)
		conn.Close()
	}

	// 创建新连接
	conn, err := f.CreateRawSocket(network, address, protocol)
	if err != nil {
		return nil, err
	}

	// 添加到缓存
	f.connCache[key] = conn

	return conn, nil
}

// removeConnection 移除连接
func (f *DefaultRawSocketForwarder) removeConnection(network, address string, protocol uint8) {
	key := fmt.Sprintf("%s:%s:%d", network, address, protocol)

	if conn, exists := f.connCache[key]; exists {
		conn.Close()
		delete(f.connCache, key)
	}
}

// isConnectionValid 检查连接是否有效
func (f *DefaultRawSocketForwarder) isConnectionValid(conn net.Conn) bool {
	// 简单的连接有效性检查
	// 对于原始套接字，这个检查比较简单
	return conn != nil
}

// createFallbackConnection 创建回退连接
func (f *DefaultRawSocketForwarder) createFallbackConnection(network, address string, protocol uint8) (net.Conn, error) {
	f.logger.Debug("Attempting fallback connection creation",
		zap.String("network", network),
		zap.String("address", address),
		zap.Uint8("protocol", protocol))

	// 根据协议尝试不同的回退策略
	switch protocol {
	case 6: // TCP
		// 对于TCP，尝试普通TCP连接
		return net.DialTimeout("tcp", address+":0", f.config.RawSocketTimeout)
	case 17: // UDP
		// 对于UDP，尝试普通UDP连接
		return net.DialTimeout("udp", address+":0", f.config.RawSocketTimeout)
	default:
		// 其他协议，尝试IP连接
		return net.DialTimeout("ip:"+fmt.Sprintf("%d", protocol), address, f.config.RawSocketTimeout)
	}
}
