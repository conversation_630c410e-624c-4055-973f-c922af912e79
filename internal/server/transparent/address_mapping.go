package transparent

import (
	"sync"
	"time"
)

// MappingEntry 地址映射条目
type MappingEntry struct {
	ClientID  string        // 客户端连接标识
	CreatedAt time.Time     // 创建时间
	TTL       time.Duration // 生存时间
}

// IsExpired 检查映射是否过期
func (e *MappingEntry) IsExpired() bool {
	return time.Since(e.CreatedAt) > e.TTL
}

// DefaultAddressMapping 默认地址映射实现
type DefaultAddressMapping struct {
	mappings map[string]*MappingEntry // IP -> MappingEntry
	mu       sync.RWMutex             // 读写锁

	// 统计信息
	stats struct {
		TotalMappings   int64 // 总映射数
		ActiveMappings  int64 // 活跃映射数
		ExpiredMappings int64 // 过期映射数
		LookupCount     int64 // 查找次数
		HitCount        int64 // 命中次数
		MissCount       int64 // 未命中次数
	}

	// 清理相关
	cleanupTicker *time.Ticker
	stopCleanup   chan struct{}
}

// NewAddressMapping 创建新的地址映射
func NewAddressMapping() AddressMapping {
	am := &DefaultAddressMapping{
		mappings:    make(map[string]*MappingEntry),
		stopCleanup: make(chan struct{}),
	}

	// 启动定期清理过期映射
	am.cleanupTicker = time.NewTicker(1 * time.Minute)
	go am.cleanupLoop()

	return am
}

// SetMapping 设置地址映射
func (am *DefaultAddressMapping) SetMapping(originalIP string, clientID string, ttl time.Duration) {
	am.mu.Lock()
	defer am.mu.Unlock()

	// 检查是否是新映射
	isNew := am.mappings[originalIP] == nil

	am.mappings[originalIP] = &MappingEntry{
		ClientID:  clientID,
		CreatedAt: time.Now(),
		TTL:       ttl,
	}

	// 更新统计
	if isNew {
		am.stats.TotalMappings++
		am.stats.ActiveMappings++
	}
}

// GetMapping 获取地址映射
func (am *DefaultAddressMapping) GetMapping(originalIP string) (string, bool) {
	am.mu.RLock()
	defer am.mu.RUnlock()

	// 更新查找统计
	am.stats.LookupCount++

	entry, exists := am.mappings[originalIP]
	if !exists {
		am.stats.MissCount++
		return "", false
	}

	// 检查是否过期
	if entry.IsExpired() {
		// 延迟删除，在清理循环中处理
		am.stats.MissCount++
		return "", false
	}

	am.stats.HitCount++
	return entry.ClientID, true
}

// RemoveMapping 移除地址映射
func (am *DefaultAddressMapping) RemoveMapping(originalIP string) {
	am.mu.Lock()
	defer am.mu.Unlock()

	if _, exists := am.mappings[originalIP]; exists {
		delete(am.mappings, originalIP)
		am.stats.ActiveMappings--
	}
}

// GetStats 获取映射统计
func (am *DefaultAddressMapping) GetStats() map[string]interface{} {
	am.mu.RLock()
	defer am.mu.RUnlock()

	return map[string]interface{}{
		"total_mappings":   am.stats.TotalMappings,
		"active_mappings":  am.stats.ActiveMappings,
		"expired_mappings": am.stats.ExpiredMappings,
		"lookup_count":     am.stats.LookupCount,
		"hit_count":        am.stats.HitCount,
		"miss_count":       am.stats.MissCount,
		"hit_rate":         am.calculateHitRate(),
	}
}

// calculateHitRate 计算命中率
func (am *DefaultAddressMapping) calculateHitRate() float64 {
	if am.stats.LookupCount == 0 {
		return 0.0
	}
	return float64(am.stats.HitCount) / float64(am.stats.LookupCount)
}

// cleanupLoop 清理循环
func (am *DefaultAddressMapping) cleanupLoop() {
	for {
		select {
		case <-am.cleanupTicker.C:
			am.cleanupExpiredMappings()
		case <-am.stopCleanup:
			am.cleanupTicker.Stop()
			return
		}
	}
}

// cleanupExpiredMappings 清理过期映射
func (am *DefaultAddressMapping) cleanupExpiredMappings() {
	am.mu.Lock()
	defer am.mu.Unlock()

	expiredCount := int64(0)
	for ip, entry := range am.mappings {
		if entry.IsExpired() {
			delete(am.mappings, ip)
			expiredCount++
		}
	}

	// 更新统计
	am.stats.ExpiredMappings += expiredCount
	am.stats.ActiveMappings -= expiredCount
}

// Close 关闭地址映射
func (am *DefaultAddressMapping) Close() error {
	close(am.stopCleanup)

	am.mu.Lock()
	defer am.mu.Unlock()

	// 清空所有映射
	am.mappings = make(map[string]*MappingEntry)
	am.stats.ActiveMappings = 0

	return nil
}
