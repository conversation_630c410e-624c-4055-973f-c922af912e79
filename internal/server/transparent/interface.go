package transparent

import (
	"net"
	"time"
)

// TransparentProxy 透明代理核心接口
// 遵循VPN透明代理最佳实践，实现真正的端到端透明转发
type TransparentProxy interface {
	// ForwardPacket 转发数据包 - 统一的透明转发接口
	// packet: 原始IP数据包
	// clientID: 客户端标识，用于地址映射
	// 返回: 响应数据包和错误
	ForwardPacket(packet []byte, clientID string) ([]byte, error)
	
	// GetMetrics 获取转发指标
	GetMetrics() *ForwardingMetrics
	
	// Close 关闭透明代理
	Close() error
}

// PacketInfo 数据包信息
type PacketInfo struct {
	// 基本IP信息
	Version    uint8  // IP版本 (4 or 6)
	Protocol   uint8  // 协议类型 (1=ICMP, 6=TCP, 17=UDP)
	SrcIP      net.IP // 源IP地址
	DstIP      net.IP // 目标IP地址
	PacketSize int    // 数据包大小
	
	// 协议特定信息
	SrcPort uint16 // 源端口 (TCP/UDP)
	DstPort uint16 // 目标端口 (TCP/UDP)
	
	// 原始数据
	RawPacket []byte // 完整的原始数据包
	Payload   []byte // 协议载荷数据
}

// ForwardingMetrics 转发指标
type ForwardingMetrics struct {
	// 数据包统计
	PacketsForwarded int64 // 成功转发的数据包数
	PacketsDropped   int64 // 丢弃的数据包数
	BytesForwarded   int64 // 转发的字节数
	
	// 协议统计
	ICMPPackets int64 // ICMP数据包数
	TCPPackets  int64 // TCP数据包数
	UDPPackets  int64 // UDP数据包数
	
	// 性能指标
	AverageLatency time.Duration // 平均延迟
	LastForwardTime time.Time    // 最后转发时间
	
	// 错误统计
	ForwardingErrors int64 // 转发错误数
	AddressMappingErrors int64 // 地址映射错误数
}

// AddressMapping 地址映射接口
type AddressMapping interface {
	// SetMapping 设置地址映射
	// originalIP: 原始客户端IP
	// clientID: 客户端连接标识
	// ttl: 映射生存时间
	SetMapping(originalIP string, clientID string, ttl time.Duration)
	
	// GetMapping 获取地址映射
	// originalIP: 原始客户端IP
	// 返回: 客户端连接标识和是否存在
	GetMapping(originalIP string) (string, bool)
	
	// RemoveMapping 移除地址映射
	RemoveMapping(originalIP string)
	
	// GetStats 获取映射统计
	GetStats() map[string]interface{}
}

// RawSocketForwarder 原始套接字转发器接口
type RawSocketForwarder interface {
	// ForwardRaw 原始套接字转发
	// packet: 原始数据包
	// target: 目标地址
	// protocol: 协议类型
	// 返回: 响应数据和错误
	ForwardRaw(packet []byte, target net.IP, protocol uint8) ([]byte, error)
	
	// CreateRawSocket 创建原始套接字
	// network: 网络类型 (如 "ip4:icmp", "ip4:tcp")
	// address: 目标地址
	// protocol: 协议号
	// 返回: 连接和错误
	CreateRawSocket(network, address string, protocol uint8) (net.Conn, error)
	
	// Close 关闭转发器
	Close() error
}

// PacketParser 数据包解析器接口
type PacketParser interface {
	// ParsePacket 解析数据包
	// packet: 原始数据包
	// 返回: 解析后的包信息和错误
	ParsePacket(packet []byte) (*PacketInfo, error)
	
	// BuildResponsePacket 构造响应数据包
	// originalPacket: 原始请求包信息
	// responseData: 响应数据
	// srcIP: 响应源IP
	// dstIP: 响应目标IP
	// 返回: 构造的响应包和错误
	BuildResponsePacket(originalPacket *PacketInfo, responseData []byte, srcIP, dstIP net.IP) ([]byte, error)
}

// TransparentProxyConfig 透明代理配置
type TransparentProxyConfig struct {
	// 基本配置
	EnableIPv4 bool // 启用IPv4支持
	EnableIPv6 bool // 启用IPv6支持
	
	// 转发配置
	ForwardingTimeout time.Duration // 转发超时时间
	MaxPacketSize     int           // 最大数据包大小
	
	// 地址映射配置
	AddressMappingTTL time.Duration // 地址映射生存时间
	
	// 原始套接字配置
	RawSocketTimeout time.Duration // 原始套接字超时
	EnableFallback   bool          // 启用回退机制
	
	// 日志配置
	EnableDebugLog bool // 启用调试日志
	LogPacketInfo  bool // 记录数据包信息
}

// DefaultConfig 默认配置
func DefaultConfig() *TransparentProxyConfig {
	return &TransparentProxyConfig{
		EnableIPv4:        true,
		EnableIPv6:        true,
		ForwardingTimeout: 30 * time.Second,
		MaxPacketSize:     8192,
		AddressMappingTTL: 5 * time.Minute,
		RawSocketTimeout:  10 * time.Second,
		EnableFallback:    true,
		EnableDebugLog:    false,
		LogPacketInfo:     false,
	}
}
