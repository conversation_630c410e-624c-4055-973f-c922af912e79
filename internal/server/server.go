package server

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"encoding/json"
	"fmt"
	"net"
	"os"
	"strings"
	"sync"
	"time"

	"cyber-bastion/internal/forwarding"
	"cyber-bastion/pkg/checksum"
	"cyber-bastion/pkg/config"
	"cyber-bastion/pkg/protocol"
	"cyber-bastion/pkg/tun"

	"go.uber.org/zap"
)

// peekableConn 是一个可以"偷看"数据的连接包装器
type peekableConn struct {
	net.Conn
	buffer []byte
	offset int
}

// newPeekableConn 创建一个新的可偷看连接
func newPeekableConn(conn net.Conn) *peekableConn {
	return &peekableConn{
		Conn:   conn,
		buffer: make([]byte, 0),
		offset: 0,
	}
}

// peek 偷看数据而不消费
func (pc *peekableConn) peek(n int) ([]byte, error) {
	// 如果缓冲区中的数据不够，从连接中读取更多数据
	for len(pc.buffer) < n {
		temp := make([]byte, 1024)
		nr, err := pc.Conn.Read(temp)
		if err != nil {
			return nil, err
		}
		pc.buffer = append(pc.buffer, temp[:nr]...)
	}

	if len(pc.buffer) < n {
		return pc.buffer, nil
	}
	return pc.buffer[:n], nil
}

// Read 从连接读取数据，优先从缓冲区读取
func (pc *peekableConn) Read(b []byte) (int, error) {
	// 如果缓冲区中有数据，先从缓冲区读取
	if pc.offset < len(pc.buffer) {
		n := copy(b, pc.buffer[pc.offset:])
		pc.offset += n

		// 如果缓冲区数据已经全部读取，清空缓冲区
		if pc.offset >= len(pc.buffer) {
			pc.buffer = pc.buffer[:0]
			pc.offset = 0
		}

		return n, nil
	}

	// 缓冲区为空，直接从连接读取
	return pc.Conn.Read(b)
}

// tlsPeekableConn 是一个可以"偷看"TLS连接数据的包装器
type tlsPeekableConn struct {
	*tls.Conn
	buffer []byte
	offset int
}

// newTLSPeekableConn 创建一个新的TLS可偷看连接
func newTLSPeekableConn(conn *tls.Conn) *tlsPeekableConn {
	return &tlsPeekableConn{
		Conn:   conn,
		buffer: make([]byte, 0),
		offset: 0,
	}
}

// peek 偷看TLS连接数据而不消费
func (tpc *tlsPeekableConn) peek(n int) ([]byte, error) {
	// 如果缓冲区中的数据不够，从连接中读取更多数据
	for len(tpc.buffer) < n {
		temp := make([]byte, 1024)
		nr, err := tpc.Conn.Read(temp)
		if err != nil {
			return nil, err
		}
		tpc.buffer = append(tpc.buffer, temp[:nr]...)
	}

	if len(tpc.buffer) < n {
		return tpc.buffer, nil
	}
	return tpc.buffer[:n], nil
}

// Read 从TLS连接读取数据，优先从缓冲区读取
func (tpc *tlsPeekableConn) Read(b []byte) (int, error) {
	// 如果缓冲区中有数据，先从缓冲区读取
	if tpc.offset < len(tpc.buffer) {
		n := copy(b, tpc.buffer[tpc.offset:])
		tpc.offset += n

		// 如果缓冲区数据已经全部读取，清空缓冲区
		if tpc.offset >= len(tpc.buffer) {
			tpc.buffer = tpc.buffer[:0]
			tpc.offset = 0
		}

		return n, nil
	}

	// 缓冲区为空，直接从连接读取
	return tpc.Conn.Read(b)
}

// Write 写入数据到TLS连接
func (tpc *tlsPeekableConn) Write(b []byte) (int, error) {
	return tpc.Conn.Write(b)
}

// Client 表示连接的客户端
type Client struct {
	ID         string
	Conn       net.Conn
	LastSeen   time.Time
	Authorized bool
	mu         sync.RWMutex
}

// NewClient 创建新客户端
func NewClient(id string, conn net.Conn) *Client {
	return &Client{
		ID:         id,
		Conn:       conn,
		LastSeen:   time.Now(),
		Authorized: false,
	}
}

// UpdateLastSeen 更新最后活跃时间
func (c *Client) UpdateLastSeen() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.LastSeen = time.Now()
}

// IsAuthorized 检查是否已认证
func (c *Client) IsAuthorized() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.Authorized
}

// SetAuthorized 设置认证状态
func (c *Client) SetAuthorized(authorized bool) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.Authorized = authorized
}

// IPStats IP连接统计
type IPStats struct {
	ConnectionCount int
	LastConnection  time.Time
	BlockedUntil    time.Time
	AttackAttempts  int
	LastAttack      time.Time
}

// SecurityEvent 安全事件
type SecurityEvent struct {
	Type        string
	IP          string
	Description string
	Timestamp   time.Time
	Severity    string
}

// AddressMapping 地址映射关系
type AddressMapping struct {
	ClientRealIP  net.IP    // 客户端真实IP
	ServerLocalIP net.IP    // 服务器本地IP
	TargetIP      net.IP    // 目标IP
	LastUsed      time.Time // 最后使用时间
	PacketCount   int64     // 数据包计数
}

// AddressMappingCache 地址映射缓存
type AddressMappingCache struct {
	mappings map[string]*AddressMapping // key: clientID
	mu       sync.RWMutex
	ttl      time.Duration // 缓存TTL
}

// GetForwarding 获取地址映射（forwarding包兼容）
func (amc *AddressMappingCache) GetForwarding(clientID string) (*forwarding.AddressMapping, bool) {
	amc.mu.RLock()
	defer amc.mu.RUnlock()

	mapping, exists := amc.mappings[clientID]
	if !exists {
		return nil, false
	}

	// 检查是否过期
	if time.Since(mapping.LastUsed) > amc.ttl {
		return nil, false
	}

	// 转换为forwarding包的AddressMapping
	forwardingMapping := &forwarding.AddressMapping{
		ClientRealIP:  mapping.ClientRealIP,
		ServerLocalIP: mapping.ServerLocalIP,
		TargetIP:      mapping.TargetIP,
		LastUsed:      mapping.LastUsed,
		PacketCount:   mapping.PacketCount,
	}

	return forwardingMapping, true
}

// AddressMappingCacheAdapter 地址映射缓存适配器
type AddressMappingCacheAdapter struct {
	cache *AddressMappingCache
}

// NewAddressMappingCacheAdapter 创建地址映射缓存适配器
func NewAddressMappingCacheAdapter(cache *AddressMappingCache) *AddressMappingCacheAdapter {
	return &AddressMappingCacheAdapter{cache: cache}
}

// Set 设置地址映射
func (a *AddressMappingCacheAdapter) Set(clientID string, clientRealIP, serverLocalIP, targetIP net.IP) {
	a.cache.Set(clientID, clientRealIP, serverLocalIP, targetIP)
}

// Get 获取地址映射
func (a *AddressMappingCacheAdapter) Get(clientID string) (*forwarding.AddressMapping, bool) {
	return a.cache.GetForwarding(clientID)
}

// Update 更新地址映射的使用时间和计数
func (a *AddressMappingCacheAdapter) Update(clientID string) {
	a.cache.Update(clientID)
}

// Cleanup 清理过期的映射
func (a *AddressMappingCacheAdapter) Cleanup() {
	a.cache.Cleanup()
}

// 确保AddressMappingCacheAdapter实现了forwarding.AddressMappingCacheInterface接口
var _ forwarding.AddressMappingCacheInterface = (*AddressMappingCacheAdapter)(nil)

// NewAddressMappingCache 创建新的地址映射缓存
func NewAddressMappingCache(ttl time.Duration) *AddressMappingCache {
	return &AddressMappingCache{
		mappings: make(map[string]*AddressMapping),
		ttl:      ttl,
	}
}

// Set 设置地址映射
func (amc *AddressMappingCache) Set(clientID string, clientRealIP, serverLocalIP, targetIP net.IP) {
	amc.mu.Lock()
	defer amc.mu.Unlock()

	amc.mappings[clientID] = &AddressMapping{
		ClientRealIP:  clientRealIP,
		ServerLocalIP: serverLocalIP,
		TargetIP:      targetIP,
		LastUsed:      time.Now(),
		PacketCount:   1,
	}
}

// Get 获取地址映射
func (amc *AddressMappingCache) Get(clientID string) (*AddressMapping, bool) {
	amc.mu.RLock()
	defer amc.mu.RUnlock()

	mapping, exists := amc.mappings[clientID]
	if !exists {
		return nil, false
	}

	// 检查是否过期
	if time.Since(mapping.LastUsed) > amc.ttl {
		return nil, false
	}

	return mapping, true
}

// Update 更新地址映射的使用时间和计数
func (amc *AddressMappingCache) Update(clientID string) {
	amc.mu.Lock()
	defer amc.mu.Unlock()

	if mapping, exists := amc.mappings[clientID]; exists {
		mapping.LastUsed = time.Now()
		mapping.PacketCount++
	}
}

// Cleanup 清理过期的映射
func (amc *AddressMappingCache) Cleanup() {
	amc.mu.Lock()
	defer amc.mu.Unlock()

	now := time.Now()
	for clientID, mapping := range amc.mappings {
		if now.Sub(mapping.LastUsed) > amc.ttl {
			delete(amc.mappings, clientID)
		}
	}
}

// Server TCP服务器
type Server struct {
	config              *config.ServerConfig
	logger              *zap.Logger
	listener            net.Listener
	clients             map[string]*Client
	mu                  sync.RWMutex
	ipStats             map[string]*IPStats
	ipMu                sync.RWMutex
	ctx                 context.Context
	cancel              context.CancelFunc
	wg                  sync.WaitGroup
	forwardingHandler   *forwarding.OptimizedForwardingHandler
	legacyForwarding    bool
	concurrentForwarder *ConcurrentForwarder
	tcpHandler          *TCPHandler
	// 地址映射缓存
	addressMappingCache        *AddressMappingCache
	addressMappingCacheAdapter *AddressMappingCacheAdapter
}

// NewServer 创建新服务器
func NewServer(cfg *config.ServerConfig, logger *zap.Logger) *Server {
	ctx, cancel := context.WithCancel(context.Background())

	// 创建优化转发处理器配置（临时注释，使用传统转发）
	/*
		handlerConfig := &forwarding.HandlerConfig{
			WorkerCount:     getConfigInt(cfg, "forwarding_workers", 10),
			QueueSize:       getConfigInt(cfg, "forwarding_queue_size", 1000),
			MaxIdleConns:    getConfigInt(cfg, "max_idle_connections", 50),
			MaxActiveConns:  getConfigInt(cfg, "max_active_connections", 200),
			IdleTimeout:     time.Minute * 5,
			DialTimeout:     time.Second * 10,
			EnableMetrics:   true,
			MetricsInterval: time.Second * 30,
			EnableCache:     true,
			CacheSize:       1000,
			CacheTTL:        time.Minute * 5,
		}
	*/

	// 创建优化转发处理器配置
	handlerConfig := &forwarding.HandlerConfig{
		WorkerCount:     4,
		QueueSize:       1000,
		MaxIdleConns:    10,
		MaxActiveConns:  100,
		IdleTimeout:     time.Minute * 5,
		DialTimeout:     time.Second * 10,
		EnableMetrics:   true,
		MetricsInterval: time.Second * 30,
		EnableCache:     true,
		CacheSize:       1000,
		CacheTTL:        time.Minute * 5,
	}

	// 创建优化转发处理器
	forwardingHandler, err := forwarding.NewOptimizedForwardingHandler(handlerConfig, logger)
	if err != nil {
		logger.Error("Failed to create optimized forwarding handler, falling back to legacy mode",
			zap.Error(err))
		forwardingHandler = nil
	} else {
		logger.Info("Created optimized forwarding handler successfully")
	}

	// 创建地址映射缓存
	addressMappingCache := NewAddressMappingCache(time.Minute * 10) // 10分钟TTL
	addressMappingCacheAdapter := NewAddressMappingCacheAdapter(addressMappingCache)

	// 如果优化转发处理器创建成功，设置地址映射缓存
	if forwardingHandler != nil {
		forwardingHandler.SetAddressMappingCache(addressMappingCacheAdapter)
		logger.Info("Address mapping cache integrated with optimized forwarding handler")
	}

	// 创建并发转发器
	concurrentForwarder := NewConcurrentForwarder(nil, logger) // 稍后设置server引用

	// 创建TCP处理器
	tcpHandler := NewTCPHandler(nil, logger) // 稍后设置server引用

	server := &Server{
		config:                     cfg,
		logger:                     logger,
		clients:                    make(map[string]*Client),
		ipStats:                    make(map[string]*IPStats),
		ctx:                        ctx,
		cancel:                     cancel,
		forwardingHandler:          forwardingHandler,
		legacyForwarding:           forwardingHandler == nil,
		concurrentForwarder:        concurrentForwarder,
		tcpHandler:                 tcpHandler,
		addressMappingCache:        addressMappingCache,
		addressMappingCacheAdapter: addressMappingCacheAdapter,
	}

	// 设置并发转发器的server引用
	concurrentForwarder.server = server

	// 设置TCP处理器的server引用
	tcpHandler.server = server

	return server
}

// generateSimpleTCPResponse 生成简单的TCP响应（降级策略）
func (s *Server) generateSimpleTCPResponse(packet []byte, clientID string) ([]byte, error) {
	if len(packet) < 40 {
		return nil, fmt.Errorf("packet too short for TCP response")
	}

	// 解析原始包
	srcIP := net.IP(packet[12:16])
	dstIP := net.IP(packet[16:20])
	tcpHeader := packet[20:]
	srcPort := uint16(tcpHeader[0])<<8 | uint16(tcpHeader[1])
	dstPort := uint16(tcpHeader[2])<<8 | uint16(tcpHeader[3])
	seqNum := uint32(tcpHeader[4])<<24 | uint32(tcpHeader[5])<<16 | uint32(tcpHeader[6])<<8 | uint32(tcpHeader[7])

	s.logger.Debug("Generating simple TCP response as fallback",
		zap.String("client_id", clientID),
		zap.String("src_ip", srcIP.String()),
		zap.String("dst_ip", dstIP.String()),
		zap.Uint16("src_port", srcPort),
		zap.Uint16("dst_port", dstPort))

	// 构造TCP RST响应包（告诉客户端连接被拒绝）
	// 这是一个安全的降级策略，不会导致连接挂起
	responseData := []byte{
		0x00, 0x00, 0x00, 0x00, // 序列号（RST包中为0）
		byte(seqNum >> 24), byte(seqNum >> 16), byte(seqNum >> 8), byte(seqNum), // 确认号（原序列号+1）
		0x50, 0x04, // TCP头长度(20字节) + RST标志
		0x00, 0x00, // 窗口大小
		0x00, 0x00, // 校验和（稍后计算）
		0x00, 0x00, // 紧急指针
	}

	// 使用地址映射缓存获取正确的客户端IP
	var clientRealIP net.IP = srcIP
	if mapping, exists := s.addressMappingCache.Get(clientID); exists {
		clientRealIP = mapping.ClientRealIP
		s.logger.Debug("Using cached address mapping for simple TCP response",
			zap.String("client_id", clientID),
			zap.String("cached_client_real_ip", clientRealIP.String()),
			zap.String("packet_src_ip", srcIP.String()))
	}

	// 构造完整的TCP响应包
	responsePacket, err := s.buildTCPResponsePacket(clientRealIP, srcPort, dstIP, dstPort, responseData)
	if err != nil {
		return nil, fmt.Errorf("failed to build simple TCP response: %w", err)
	}

	s.logger.Debug("Simple TCP response generated",
		zap.String("client_id", clientID),
		zap.String("response_type", "RST"),
		zap.Int("response_size", len(responsePacket)))

	return responsePacket, nil
}

// getConfigInt 获取配置整数值，如果不存在则返回默认值
func getConfigInt(cfg *config.ServerConfig, key string, defaultValue int) int {
	// 这里简化处理，直接返回默认值
	// 在实际实现中，可以从配置文件中读取相应的值
	return defaultValue
}

// Start 启动服务器
func (s *Server) Start() error {
	addr := fmt.Sprintf("%s:%d", s.config.Host, s.config.Port)

	var listener net.Listener
	var err error

	// 总是使用普通TCP监听器，然后在连接时决定是否使用TLS
	listener, err = net.Listen("tcp", addr)
	if err != nil {
		return fmt.Errorf("failed to listen on %s: %w", addr, err)
	}

	if s.config.Security != nil && s.config.Security.EnableTLS {
		s.logger.Info("TLS Server started (with HTTP masquerading)", zap.String("address", addr))
	} else {
		s.logger.Info("Server started", zap.String("address", addr))
	}

	s.listener = listener

	// 启动优化转发处理器
	if s.forwardingHandler != nil {
		if err := s.forwardingHandler.Start(); err != nil {
			s.logger.Error("Failed to start optimized forwarding handler, falling back to legacy mode",
				zap.Error(err))
			s.legacyForwarding = true
		} else {
			s.logger.Info("Optimized forwarding handler started successfully")
		}
	}

	// 启动并发转发器
	if err := s.concurrentForwarder.Start(); err != nil {
		s.logger.Error("Failed to start concurrent forwarder", zap.Error(err))
		return fmt.Errorf("failed to start concurrent forwarder: %w", err)
	}

	// 启动TCP处理器
	if err := s.tcpHandler.Start(); err != nil {
		s.logger.Error("Failed to start TCP handler", zap.Error(err))
		return fmt.Errorf("failed to start TCP handler: %w", err)
	}

	// 启动客户端清理协程
	s.wg.Add(1)
	go s.cleanupClients()

	// 启动地址映射缓存清理协程
	s.wg.Add(1)
	go s.cleanupAddressMappings()

	// 接受连接
	for {
		select {
		case <-s.ctx.Done():
			return nil
		default:
			conn, err := listener.Accept()
			if err != nil {
				if s.ctx.Err() != nil {
					return nil
				}
				s.logger.Error("Failed to accept connection", zap.Error(err))
				continue
			}

			// 获取客户端IP
			clientIP := s.getClientIP(conn.RemoteAddr().String())

			// IP访问控制检查
			if !s.isIPAllowed(clientIP) {
				s.recordSecurityEvent("IP_BLOCKED", clientIP,
					"Connection rejected by IP filter", "MEDIUM")
				conn.Close()
				continue
			}

			// 检查客户端数量限制
			if s.getClientCount() >= s.config.MaxClients {
				s.logger.Warn("Max clients reached, rejecting connection",
					zap.String("remote_addr", conn.RemoteAddr().String()))
				conn.Close()
				continue
			}

			// 处理新连接
			s.wg.Add(1)
			go s.handleConnection(conn)
		}
	}
}

// Stop 停止服务器
func (s *Server) Stop() error {
	s.logger.Info("Stopping server...")
	s.cancel()

	if s.listener != nil {
		s.listener.Close()
	}

	// 关闭所有客户端连接
	s.mu.Lock()
	for _, client := range s.clients {
		client.Conn.Close()
	}
	s.mu.Unlock()

	// 停止优化转发处理器
	if s.forwardingHandler != nil {
		if err := s.forwardingHandler.Stop(); err != nil {
			s.logger.Error("Failed to stop optimized forwarding handler", zap.Error(err))
		} else {
			s.logger.Info("Optimized forwarding handler stopped")
		}
	}

	// 停止并发转发器
	if err := s.concurrentForwarder.Stop(); err != nil {
		s.logger.Error("Failed to stop concurrent forwarder", zap.Error(err))
	}

	// 停止TCP处理器
	if err := s.tcpHandler.Stop(); err != nil {
		s.logger.Error("Failed to stop TCP handler", zap.Error(err))
	}

	s.wg.Wait()
	s.logger.Info("Server stopped")
	return nil
}

// handleConnection 处理客户端连接
func (s *Server) handleConnection(conn net.Conn) {
	defer s.wg.Done()
	defer conn.Close()

	clientID := conn.RemoteAddr().String()
	clientIP := s.getClientIP(clientID)

	s.logger.Info("New client connected", zap.String("client_id", clientID))

	// 创建可偷看的连接包装器
	peekConn := newPeekableConn(conn)

	// 设置连接超时
	if s.config.ReadTimeout > 0 {
		conn.SetReadDeadline(time.Now().Add(time.Duration(s.config.ReadTimeout) * time.Second))
	}

	// 检测连接类型
	s.logger.Debug("Detecting connection type", zap.String("client_id", clientID))
	connType := s.detectConnectionType(peekConn, clientID)

	switch connType {
	case "http":
		s.handleHTTPRequest(peekConn, clientID, clientIP)
		return
	case "tls":
		// 处理TLS连接
		if s.config.Security != nil && s.config.Security.EnableTLS {
			tlsConfig, err := s.loadTLSConfig()
			if err != nil {
				s.logger.Error("Failed to load TLS config", zap.Error(err))
				return
			}

			tlsConn := tls.Server(peekConn, tlsConfig)
			err = tlsConn.Handshake()
			if err != nil {
				s.logger.Debug("TLS handshake failed",
					zap.String("client_id", clientID),
					zap.Error(err))
				return
			}
			s.logger.Debug("TLS handshake completed", zap.String("client_id", clientID))

			// 为TLS连接创建可偷看的包装器
			tlsPeekConn := newTLSPeekableConn(tlsConn)

			// 检查TLS连接内是否是HTTPS请求
			if s.isHTTPSRequestWithPeek(tlsPeekConn, clientID) {
				s.handleHTTPSRequest(tlsPeekConn, clientID, clientIP)
				return
			}

			conn = tlsPeekConn
		} else {
			s.logger.Debug("TLS connection detected but TLS not enabled", zap.String("client_id", clientID))
			return
		}
	case "custom":
		// 自定义协议连接
		s.logger.Debug("Custom protocol connection detected", zap.String("client_id", clientID))
		conn = peekConn
	default:
		s.logger.Debug("Unknown connection type, treating as custom protocol", zap.String("client_id", clientID))
		conn = peekConn
	}

	// 按照原有协议处理
	client := NewClient(clientID, conn)

	// 添加到客户端列表
	s.addClient(client)
	defer s.removeClient(clientID)

	for {
		select {
		case <-s.ctx.Done():
			return
		default:
			// 接收消息
			msg, err := protocol.ReceiveMessage(conn)
			if err != nil {
				// 检查是否是正常关闭连接
				if s.isExpectedCloseError(err) {
					s.logger.Debug("Client connection closed normally",
						zap.String("client_id", clientID),
						zap.Error(err))
				} else {
					// 检查是否是潜在攻击
					if protocol.IsSuspiciousError(err) {
						s.recordSecurityEvent("PROTOCOL_ATTACK", clientIP,
							"Protocol violation detected", "HIGH")
					} else {
						s.logger.Error("Failed to receive message",
							zap.String("client_id", clientID),
							zap.Error(protocol.SanitizeError(err)))
					}
				}
				return
			}

			client.UpdateLastSeen()

			// 处理消息
			if err := s.handleMessage(client, msg); err != nil {
				s.logger.Error("Failed to handle message",
					zap.String("client_id", clientID),
					zap.Error(err))
				return
			}

			// 更新读取超时
			if s.config.ReadTimeout > 0 {
				conn.SetReadDeadline(time.Now().Add(time.Duration(s.config.ReadTimeout) * time.Second))
			}
		}
	}
}

// handleMessage 处理消息
func (s *Server) handleMessage(client *Client, msg *protocol.Message) error {
	s.logger.Debug("Received message",
		zap.String("client_id", client.ID),
		zap.String("message", msg.String()))

	switch msg.Type {
	case protocol.MessageTypeAuth:
		return s.handleAuth(client, msg)
	case protocol.MessageTypeHeartbeat:
		return s.handleHeartbeat(client, msg)
	case protocol.MessageTypeData:
		if !client.IsAuthorized() {
			return s.sendError(client, msg.ID, fmt.Errorf("not authorized"))
		}
		return s.handleData(client, msg)
	case protocol.MessageTypeTunData:
		if !client.IsAuthorized() {
			return s.sendError(client, msg.ID, fmt.Errorf("not authorized"))
		}
		return s.handleTunData(client, msg)
	case protocol.MessageTypeTunResponse:
		// TUN响应消息不应该从client发送到server
		return s.sendError(client, msg.ID, fmt.Errorf("unexpected TUN response message from client"))
	default:
		return s.sendError(client, msg.ID, fmt.Errorf("unknown message type: %d", msg.Type))
	}
}

// handleAuth 处理认证消息
func (s *Server) handleAuth(client *Client, msg *protocol.Message) error {
	token := string(msg.Data)
	if token == s.config.AuthToken {
		client.SetAuthorized(true)
		s.logger.Info("Client authenticated", zap.String("client_id", client.ID))
		return s.sendResponse(client, msg.ID, []byte("authenticated"))
	}

	s.logger.Warn("Authentication failed", zap.String("client_id", client.ID))
	return s.sendError(client, msg.ID, fmt.Errorf("authentication failed"))
}

// handleHeartbeat 处理心跳消息
func (s *Server) handleHeartbeat(client *Client, msg *protocol.Message) error {
	return s.sendResponse(client, msg.ID, []byte("pong"))
}

// handleData 处理数据消息
func (s *Server) handleData(client *Client, msg *protocol.Message) error {
	s.logger.Info("Received data message",
		zap.String("client_id", client.ID),
		zap.String("message_id", msg.ID),
		zap.Int("data_length", len(msg.Data)))

	// 这里可以添加具体的业务逻辑
	response := fmt.Sprintf("Received %d bytes", len(msg.Data))
	return s.sendResponse(client, msg.ID, []byte(response))
}

// sendResponse 发送响应消息
func (s *Server) sendResponse(client *Client, msgID string, data []byte) error {
	response := protocol.NewResponse(msgID, data)
	return protocol.SendMessage(client.Conn, response)
}

// sendError 发送错误消息
func (s *Server) sendError(client *Client, msgID string, err error) error {
	errorMsg := protocol.NewError(msgID, err)
	return protocol.SendMessage(client.Conn, errorMsg)
}

// sendTunResponse 发送TUN响应数据包消息
func (s *Server) sendTunResponse(client *Client, msgID string, packet []byte) error {
	response := protocol.NewTunResponseMessage(msgID, packet)
	return protocol.SendMessage(client.Conn, response)
}

// addClient 添加客户端
func (s *Server) addClient(client *Client) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.clients[client.ID] = client
}

// removeClient 移除客户端
func (s *Server) removeClient(clientID string) {
	s.mu.Lock()
	defer s.mu.Unlock()
	delete(s.clients, clientID)
	s.logger.Info("Client disconnected", zap.String("client_id", clientID))
}

// getClientCount 获取客户端数量
func (s *Server) getClientCount() int {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return len(s.clients)
}

// cleanupClients 清理不活跃的客户端
func (s *Server) cleanupClients() {
	defer s.wg.Done()
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			s.mu.Lock()
			now := time.Now()
			for id, client := range s.clients {
				client.mu.RLock()
				lastSeen := client.LastSeen
				client.mu.RUnlock()

				if now.Sub(lastSeen) > 60*time.Second {
					s.logger.Info("Cleaning up inactive client", zap.String("client_id", id))
					client.Conn.Close()
					delete(s.clients, id)
				}
			}
			s.mu.Unlock()
		}
	}
}

// cleanupAddressMappings 清理过期的地址映射
func (s *Server) cleanupAddressMappings() {
	defer s.wg.Done()
	ticker := time.NewTicker(5 * time.Minute) // 每5分钟清理一次
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			s.logger.Debug("Cleaning up expired address mappings")
			s.addressMappingCache.Cleanup()
		}
	}
}

// isExpectedCloseError 检查是否是预期的连接关闭错误
func (s *Server) isExpectedCloseError(err error) bool {
	if err == nil {
		return false
	}

	// 检查context是否已取消（正常关闭）
	if s.ctx.Err() != nil {
		return true
	}

	errStr := err.Error()
	// 检查常见的正常关闭错误
	return errStr == "EOF" ||
		errStr == "io: read/write on closed pipe" ||
		errStr == "broken pipe" ||
		// 检查包含这些关键词的错误
		containsAny(errStr, []string{
			"use of closed network connection",
			"connection reset by peer",
			"i/o timeout",
			"network is unreachable",
			"connection refused",
			"EOF",
		})
}

// containsAny 检查字符串是否包含任何一个子字符串
func containsAny(s string, substrings []string) bool {
	for _, substr := range substrings {
		if len(s) >= len(substr) {
			for i := 0; i <= len(s)-len(substr); i++ {
				if s[i:i+len(substr)] == substr {
					return true
				}
			}
		}
	}
	return false
}

// getClientIP 从远程地址中提取IP
func (s *Server) getClientIP(remoteAddr string) string {
	host, _, err := net.SplitHostPort(remoteAddr)
	if err != nil {
		return remoteAddr
	}
	return host
}

// isIPAllowed 检查IP是否被允许连接
func (s *Server) isIPAllowed(ip string) bool {
	if s.config.Security == nil || !s.config.Security.EnableIPFilter {
		return s.checkRateLimit(ip)
	}

	// 检查黑名单
	for _, blockedIP := range s.config.Security.BlockedIPs {
		if ip == blockedIP {
			s.logger.Warn("IP blocked by blacklist", zap.String("ip", ip))
			return false
		}
	}

	// 如果有白名单，检查白名单
	if len(s.config.Security.AllowedIPs) > 0 {
		for _, allowedIP := range s.config.Security.AllowedIPs {
			if ip == allowedIP {
				return s.checkRateLimit(ip)
			}
		}
		s.logger.Warn("IP not in whitelist", zap.String("ip", ip))
		return false
	}

	return s.checkRateLimit(ip)
}

// checkRateLimit 检查IP连接速率限制
func (s *Server) checkRateLimit(ip string) bool {
	if s.config.Security == nil || !s.config.Security.RateLimitEnabled {
		return s.checkConnectionLimit(ip)
	}

	s.ipMu.Lock()
	defer s.ipMu.Unlock()

	now := time.Now()
	stats, exists := s.ipStats[ip]
	if !exists {
		stats = &IPStats{
			ConnectionCount: 1,
			LastConnection:  now,
		}
		s.ipStats[ip] = stats
		return s.checkConnectionLimit(ip)
	}

	// 检查是否在阻止期内
	if now.Before(stats.BlockedUntil) {
		s.logger.Warn("IP temporarily blocked",
			zap.String("ip", ip),
			zap.Time("blocked_until", stats.BlockedUntil))
		return false
	}

	// 重置计数器（每分钟）
	if now.Sub(stats.LastConnection) > time.Minute {
		stats.ConnectionCount = 1
		stats.LastConnection = now
		return s.checkConnectionLimit(ip)
	}

	stats.ConnectionCount++
	stats.LastConnection = now

	// 检查速率限制
	if stats.ConnectionCount > s.config.Security.RateLimitPerMin {
		// 临时阻止该IP 5分钟
		stats.BlockedUntil = now.Add(5 * time.Minute)
		s.logger.Warn("IP rate limit exceeded, temporarily blocking",
			zap.String("ip", ip),
			zap.Int("connections", stats.ConnectionCount),
			zap.Int("limit", s.config.Security.RateLimitPerMin))
		return false
	}

	return s.checkConnectionLimit(ip)
}

// checkConnectionLimit 检查单个IP的并发连接数限制
func (s *Server) checkConnectionLimit(ip string) bool {
	if s.config.Security == nil || s.config.Security.MaxConnPerIP <= 0 {
		return true
	}

	s.mu.RLock()
	defer s.mu.RUnlock()

	count := 0
	for _, client := range s.clients {
		clientIP := s.getClientIP(client.ID)
		if clientIP == ip {
			count++
		}
	}

	if count >= s.config.Security.MaxConnPerIP {
		s.logger.Warn("IP connection limit exceeded",
			zap.String("ip", ip),
			zap.Int("current_connections", count),
			zap.Int("limit", s.config.Security.MaxConnPerIP))
		return false
	}

	return true
}

// loadTLSConfig 加载TLS配置
func (s *Server) loadTLSConfig() (*tls.Config, error) {
	if s.config.Security == nil {
		return nil, fmt.Errorf("security config is nil")
	}

	cert, err := tls.LoadX509KeyPair(s.config.Security.TLSCertFile, s.config.Security.TLSKeyFile)
	if err != nil {
		return nil, fmt.Errorf("failed to load certificate: %w", err)
	}

	tlsConfig := &tls.Config{
		Certificates: []tls.Certificate{cert},
		ClientAuth:   tls.NoClientCert, // 不要求客户端证书，用于HTTP伪装
	}

	// 如果指定了CA文件，加载客户端证书验证
	if s.config.Security.TLSCAFile != "" {
		caCert, err := os.ReadFile(s.config.Security.TLSCAFile)
		if err != nil {
			return nil, fmt.Errorf("failed to read CA certificate: %w", err)
		}

		caCertPool := x509.NewCertPool()
		if !caCertPool.AppendCertsFromPEM(caCert) {
			return nil, fmt.Errorf("failed to parse CA certificate")
		}

		tlsConfig.ClientCAs = caCertPool
		s.logger.Info("TLS CA file loaded", zap.String("ca_file", s.config.Security.TLSCAFile))
	}

	return tlsConfig, nil
}

// recordSecurityEvent 记录安全事件
func (s *Server) recordSecurityEvent(eventType, ip, description, severity string) {
	event := SecurityEvent{
		Type:        eventType,
		IP:          ip,
		Description: description,
		Timestamp:   time.Now(),
		Severity:    severity,
	}

	// 记录到日志
	switch severity {
	case "HIGH":
		s.logger.Error("Security Event",
			zap.String("type", event.Type),
			zap.String("ip", event.IP),
			zap.String("description", event.Description),
			zap.String("severity", event.Severity))
	case "MEDIUM":
		s.logger.Warn("Security Event",
			zap.String("type", event.Type),
			zap.String("ip", event.IP),
			zap.String("description", event.Description),
			zap.String("severity", event.Severity))
	default:
		s.logger.Info("Security Event",
			zap.String("type", event.Type),
			zap.String("ip", event.IP),
			zap.String("description", event.Description),
			zap.String("severity", event.Severity))
	}

	// 更新IP攻击统计
	s.updateAttackStats(ip)
}

// updateAttackStats 更新IP攻击统计
func (s *Server) updateAttackStats(ip string) {
	s.ipMu.Lock()
	defer s.ipMu.Unlock()

	stats, exists := s.ipStats[ip]
	if !exists {
		stats = &IPStats{}
		s.ipStats[ip] = stats
	}

	stats.AttackAttempts++
	stats.LastAttack = time.Now()

	// 如果攻击次数过多，延长阻止时间
	if stats.AttackAttempts >= 5 {
		stats.BlockedUntil = time.Now().Add(30 * time.Minute) // 阻止30分钟
		s.logger.Error("IP blocked due to repeated attacks",
			zap.String("ip", ip),
			zap.Int("attack_attempts", stats.AttackAttempts))
	}
}

// isHTTPRequestWithPeek 使用peek方式检查是否是HTTP请求
func (s *Server) isHTTPRequestWithPeek(conn *peekableConn, clientID string) bool {
	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))
	defer conn.SetReadDeadline(time.Time{}) // 清除超时

	// 偷看前几个字节来检测HTTP请求
	buffer, err := conn.peek(16)
	if err != nil {
		s.logger.Debug("Failed to peek data for HTTP detection",
			zap.String("client_id", clientID),
			zap.Error(err))
		return false
	}

	data := string(buffer)
	s.logger.Debug("Read data for HTTP detection",
		zap.String("client_id", clientID),
		zap.String("data", data),
		zap.Int("bytes", len(buffer)))

	// 检查是否是HTTP方法开头
	httpMethods := []string{"GET ", "POST ", "PUT ", "DELETE ", "HEAD ", "OPTIONS ", "PATCH ", "TRACE ", "CONNECT "}
	for _, method := range httpMethods {
		if strings.HasPrefix(data, method) {
			s.logger.Info("HTTP request detected",
				zap.String("client_id", clientID),
				zap.String("method", strings.TrimSpace(method)))
			return true
		}
	}

	s.logger.Debug("Not an HTTP request", zap.String("client_id", clientID))
	return false
}

// isHTTPRequest 检查是否是HTTP请求（保留原方法以兼容）
func (s *Server) isHTTPRequest(conn net.Conn, clientID string) bool {
	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))
	defer conn.SetReadDeadline(time.Time{}) // 清除超时

	// 读取前几个字节来检测HTTP请求
	buffer := make([]byte, 16)
	n, err := conn.Read(buffer)
	if err != nil {
		s.logger.Debug("Failed to read data for HTTP detection",
			zap.String("client_id", clientID),
			zap.Error(err))
		return false
	}

	data := string(buffer[:n])
	s.logger.Debug("Read data for HTTP detection",
		zap.String("client_id", clientID),
		zap.String("data", data),
		zap.Int("bytes", n))

	// 检查是否是HTTP方法开头
	httpMethods := []string{"GET ", "POST ", "PUT ", "DELETE ", "HEAD ", "OPTIONS ", "PATCH ", "TRACE ", "CONNECT "}
	for _, method := range httpMethods {
		if strings.HasPrefix(data, method) {
			s.logger.Info("HTTP request detected",
				zap.String("client_id", clientID),
				zap.String("method", strings.TrimSpace(method)))
			return true
		}
	}

	s.logger.Debug("Not an HTTP request", zap.String("client_id", clientID))
	return false
}

// handleHTTPRequest 处理HTTP请求，返回伪装的HTTP响应
func (s *Server) handleHTTPRequest(conn net.Conn, clientID, clientIP string) {
	s.logger.Info("Handling HTTP request (masquerading as web server)",
		zap.String("client_id", clientID),
		zap.String("client_ip", clientIP))

	// 读取HTTP请求
	buffer := make([]byte, 4096)
	n, err := conn.Read(buffer)
	if err != nil {
		s.logger.Error("Failed to read HTTP request", zap.Error(err))
		return
	}

	request := string(buffer[:n])

	// 检查是否是监控端点请求
	if strings.Contains(request, "GET /metrics/forwarding") {
		s.handleForwardingMetrics(conn, clientID, clientIP)
		return
	}

	if strings.Contains(request, "GET /status/forwarding") {
		s.handleForwardingStatus(conn, clientID, clientIP)
		return
	}

	// 记录安全事件
	s.recordSecurityEvent("HTTP_REQUEST", clientIP,
		"HTTP request received on secure service", "LOW")

	// 构造伪装的HTTP响应
	response := s.buildHTTPResponse()

	// 发送响应
	_, err = conn.Write([]byte(response))
	if err != nil {
		s.logger.Error("Failed to send HTTP response",
			zap.String("client_id", clientID),
			zap.Error(err))
	} else {
		s.logger.Debug("HTTP response sent successfully",
			zap.String("client_id", clientID))
	}
}

// handleForwardingMetrics 处理转发指标请求
func (s *Server) handleForwardingMetrics(conn net.Conn, clientID, clientIP string) {
	s.logger.Debug("Handling forwarding metrics request",
		zap.String("client_id", clientID),
		zap.String("client_ip", clientIP))

	var jsonData string
	if s.forwardingHandler != nil {
		metrics := s.forwardingHandler.GetMetrics()
		jsonData = fmt.Sprintf(`{
  "handler": {
    "total_requests": %d,
    "successful_requests": %d,
    "failed_requests": %d,
    "average_latency_ms": %d,
    "throughput_per_sec": %.2f,
    "error_rate_percent": %.2f,
    "queue_utilization_percent": %.2f,
    "connection_pool_utilization_percent": %.2f
  },
  "timestamp": "%s",
  "status": "optimized"
}`, metrics.TotalRequests, metrics.SuccessfulRequests, metrics.FailedRequests,
			metrics.AverageLatency.Milliseconds(), metrics.ThroughputPerSec, metrics.ErrorRate,
			metrics.QueueUtilization, metrics.ConnectionPoolUtilization,
			time.Now().Format(time.RFC3339))
	} else {
		jsonData = fmt.Sprintf(`{
  "handler": {
    "status": "legacy_mode",
    "message": "Using legacy forwarding implementation"
  },
  "timestamp": "%s"
}`, time.Now().Format(time.RFC3339))
	}

	response := fmt.Sprintf("HTTP/1.1 200 OK\r\n"+
		"Content-Type: application/json\r\n"+
		"Content-Length: %d\r\n"+
		"Server: nginx/1.18.0\r\n"+
		"Date: %s\r\n"+
		"Connection: close\r\n"+
		"\r\n"+
		"%s",
		len(jsonData),
		time.Now().Format(time.RFC1123),
		jsonData)

	conn.Write([]byte(response))
}

// handleForwardingStatus 处理转发状态请求
func (s *Server) handleForwardingStatus(conn net.Conn, clientID, clientIP string) {
	s.logger.Debug("Handling forwarding status request",
		zap.String("client_id", clientID),
		zap.String("client_ip", clientIP))

	var jsonData string
	if s.forwardingHandler != nil {
		status := s.forwardingHandler.GetDetailedStatus()
		statusBytes, _ := json.Marshal(status)
		jsonData = string(statusBytes)
	} else {
		jsonData = fmt.Sprintf(`{
  "status": "legacy_mode",
  "message": "Using legacy forwarding implementation",
  "timestamp": "%s"
}`, time.Now().Format(time.RFC3339))
	}

	response := fmt.Sprintf("HTTP/1.1 200 OK\r\n"+
		"Content-Type: application/json\r\n"+
		"Content-Length: %d\r\n"+
		"Server: nginx/1.18.0\r\n"+
		"Date: %s\r\n"+
		"Connection: close\r\n"+
		"\r\n"+
		"%s",
		len(jsonData),
		time.Now().Format(time.RFC1123),
		jsonData)

	conn.Write([]byte(response))
}

// buildHTTPResponse 构建伪装的HTTP响应
func (s *Server) buildHTTPResponse() string {
	// 构造JSON响应数据
	jsonData := `{
  "status": "ok",
  "message": "Service is running",
  "timestamp": "` + time.Now().Format(time.RFC3339) + `",
  "version": "1.0.0"
}`

	// 构造完整的HTTP响应
	response := fmt.Sprintf("HTTP/1.1 200 OK\r\n"+
		"Content-Type: application/json\r\n"+
		"Content-Length: %d\r\n"+
		"Server: nginx/1.18.0\r\n"+
		"Date: %s\r\n"+
		"Connection: close\r\n"+
		"\r\n"+
		"%s",
		len(jsonData),
		time.Now().Format(time.RFC1123),
		jsonData)

	return response
}

// detectConnectionType 检测连接类型
func (s *Server) detectConnectionType(conn *peekableConn, clientID string) string {
	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))
	defer conn.SetReadDeadline(time.Time{}) // 清除超时

	// 偷看前几个字节来检测连接类型
	buffer, err := conn.peek(16)
	if err != nil {
		s.logger.Debug("Failed to peek data for connection type detection",
			zap.String("client_id", clientID),
			zap.Error(err))
		return "unknown"
	}

	if len(buffer) < 4 {
		s.logger.Debug("Insufficient data for connection type detection",
			zap.String("client_id", clientID),
			zap.Int("bytes", len(buffer)))
		return "unknown"
	}

	data := string(buffer)
	s.logger.Debug("Read data for connection type detection",
		zap.String("client_id", clientID),
		zap.String("data", data),
		zap.Int("bytes", len(buffer)))

	// 1. 检查是否是HTTP请求
	httpMethods := []string{"GET ", "POST ", "PUT ", "DELETE ", "HEAD ", "OPTIONS ", "PATCH ", "TRACE ", "CONNECT "}
	for _, method := range httpMethods {
		if strings.HasPrefix(data, method) {
			s.logger.Info("HTTP request detected",
				zap.String("client_id", clientID),
				zap.String("method", strings.TrimSpace(method)))
			return "http"
		}
	}

	// 2. 检查是否是TLS握手
	// TLS握手以 0x16 (22) 开始，后跟版本号
	if len(buffer) >= 3 && buffer[0] == 0x16 && buffer[1] == 0x03 {
		s.logger.Debug("TLS handshake detected", zap.String("client_id", clientID))
		return "tls"
	}

	// 3. 检查是否是自定义协议
	// 自定义协议格式：[4字节长度][JSON消息]
	// 检查前4个字节是否是合理的长度值
	if len(buffer) >= 4 {
		// 读取长度字段（大端序）
		length := uint32(buffer[0])<<24 | uint32(buffer[1])<<16 | uint32(buffer[2])<<8 | uint32(buffer[3])

		// 检查长度是否合理（1字节到1MB之间）
		if length > 0 && length <= 1024*1024 {
			// 如果有足够的数据，检查JSON格式
			if len(buffer) > 4 {
				jsonStart := buffer[4:]
				// 检查是否以JSON开始（{ 或 [）
				if len(jsonStart) > 0 && (jsonStart[0] == '{' || jsonStart[0] == '[') {
					s.logger.Debug("Custom protocol detected",
						zap.String("client_id", clientID),
						zap.Uint32("message_length", length))
					return "custom"
				}
			} else {
				// 数据不够，但长度合理，可能是自定义协议
				s.logger.Debug("Possible custom protocol detected (insufficient data)",
					zap.String("client_id", clientID),
					zap.Uint32("message_length", length))
				return "custom"
			}
		}
	}

	s.logger.Debug("Unknown connection type", zap.String("client_id", clientID))
	return "unknown"
}

// isHTTPSRequest 检查TLS连接内是否是HTTPS请求
func (s *Server) isHTTPSRequest(conn *tls.Conn, clientID string) bool {
	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))
	defer conn.SetReadDeadline(time.Time{}) // 清除超时

	// 读取前几个字节来检测HTTP请求
	buffer := make([]byte, 16)
	n, err := conn.Read(buffer)
	if err != nil {
		s.logger.Debug("Failed to read data for HTTPS detection",
			zap.String("client_id", clientID),
			zap.Error(err))
		return false
	}

	data := string(buffer[:n])
	s.logger.Debug("Checking TLS content for HTTPS",
		zap.String("client_id", clientID),
		zap.String("request_line", data))

	// 首先检查是否是自定义协议消息
	// 自定义协议格式：[4字节长度][JSON消息]
	if n >= 4 {
		// 检查前4个字节是否是长度字段
		length := uint32(buffer[0])<<24 | uint32(buffer[1])<<16 | uint32(buffer[2])<<8 | uint32(buffer[3])

		// 如果长度合理且后面跟着JSON，这是自定义协议
		if length > 0 && length <= 1024*1024 && n > 4 {
			jsonStart := buffer[4:]
			if len(jsonStart) > 0 && (jsonStart[0] == '{' || jsonStart[0] == '[') {
				s.logger.Debug("Custom protocol message detected in TLS connection",
					zap.String("client_id", clientID),
					zap.Uint32("message_length", length))
				return false // 不是HTTPS请求，是自定义协议
			}
		}
	}

	// 检查是否是HTTP方法开头
	httpMethods := []string{"GET ", "POST ", "PUT ", "DELETE ", "HEAD ", "OPTIONS ", "PATCH ", "TRACE ", "CONNECT "}
	for _, method := range httpMethods {
		if strings.HasPrefix(data, method) {
			s.logger.Info("HTTPS request detected in TLS connection",
				zap.String("client_id", clientID),
				zap.String("method", strings.TrimSpace(method)))
			return true
		}
	}

	s.logger.Debug("Not an HTTPS request, treating as custom protocol",
		zap.String("client_id", clientID))
	return false
}

// isHTTPSRequestWithPeek 使用peek方式检查TLS连接内是否是HTTPS请求
func (s *Server) isHTTPSRequestWithPeek(conn *tlsPeekableConn, clientID string) bool {
	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))
	defer conn.SetReadDeadline(time.Time{}) // 清除超时

	// 偷看前几个字节来检测HTTP请求
	buffer, err := conn.peek(16)
	if err != nil {
		s.logger.Debug("Failed to peek data for HTTPS detection",
			zap.String("client_id", clientID),
			zap.Error(err))
		return false
	}

	data := string(buffer)
	s.logger.Debug("Checking TLS content for HTTPS with peek",
		zap.String("client_id", clientID),
		zap.String("request_line", data))

	// 首先检查是否是自定义协议消息
	// 自定义协议格式：[4字节长度][JSON消息]
	if len(buffer) >= 4 {
		// 检查前4个字节是否是长度字段
		length := uint32(buffer[0])<<24 | uint32(buffer[1])<<16 | uint32(buffer[2])<<8 | uint32(buffer[3])

		// 如果长度合理且后面跟着JSON，这是自定义协议
		if length > 0 && length <= 1024*1024 && len(buffer) > 4 {
			jsonStart := buffer[4:]
			if len(jsonStart) > 0 && (jsonStart[0] == '{' || jsonStart[0] == '[') {
				s.logger.Debug("Custom protocol message detected in TLS connection",
					zap.String("client_id", clientID),
					zap.Uint32("message_length", length))
				return false // 不是HTTPS请求，是自定义协议
			}
		}
	}

	// 检查是否是HTTP方法开头
	httpMethods := []string{"GET ", "POST ", "PUT ", "DELETE ", "HEAD ", "OPTIONS ", "PATCH ", "TRACE ", "CONNECT "}
	for _, method := range httpMethods {
		if strings.HasPrefix(data, method) {
			s.logger.Info("HTTPS request detected in TLS connection",
				zap.String("client_id", clientID),
				zap.String("method", strings.TrimSpace(method)))
			return true
		}
	}

	s.logger.Debug("Not an HTTPS request, treating as custom protocol",
		zap.String("client_id", clientID))
	return false
}

// handleHTTPSRequest 处理HTTPS请求，返回伪装的HTTP响应
func (s *Server) handleHTTPSRequest(conn *tlsPeekableConn, clientID, clientIP string) {
	s.logger.Info("Handling HTTPS request (masquerading as web server)",
		zap.String("client_id", clientID),
		zap.String("client_ip", clientIP))

	// 记录安全事件
	s.recordSecurityEvent("HTTPS_REQUEST", clientIP,
		"HTTPS request received on non-HTTP service", "LOW")

	// 构造伪装的HTTP响应
	response := s.buildHTTPResponse()

	// 发送响应
	_, err := conn.Write([]byte(response))
	if err != nil {
		s.logger.Error("Failed to send HTTPS response",
			zap.String("client_id", clientID),
			zap.Error(err))
	} else {
		s.logger.Debug("HTTPS response sent successfully",
			zap.String("client_id", clientID))
	}
}

// handleTunData 处理TUN数据包消息
func (s *Server) handleTunData(client *Client, msg *protocol.Message) error {
	if s.config.Forwarding == nil || !s.config.Forwarding.Enabled {
		return s.sendError(client, msg.ID, fmt.Errorf("transparent forwarding is disabled"))
	}

	// 解析数据包获取基本信息用于日志记录和地址映射
	var srcIP, dstIP, protocolStr string
	var srcIPAddr, dstIPAddr net.IP
	if len(msg.Data) >= 20 {
		// 使用tun包中的工具函数解析数据包
		srcIPAddr = tun.GetSourceIP(msg.Data)
		dstIPAddr = tun.GetDestinationIP(msg.Data)
		protocolStr = tun.GetProtocolString(msg.Data)

		if srcIPAddr != nil {
			srcIP = srcIPAddr.String()
		}
		if dstIPAddr != nil {
			dstIP = dstIPAddr.String()
		}
	}

	s.logger.Debug("Received TUN data packet",
		zap.String("client_id", client.ID),
		zap.String("message_id", msg.ID),
		zap.Int("packet_length", len(msg.Data)),
		zap.String("src_ip", srcIP),
		zap.String("dst_ip", dstIP),
		zap.String("protocol", protocolStr))

	// 记录地址映射关系（用于响应包的正确路由）
	if srcIPAddr != nil && dstIPAddr != nil {
		// 🔧 关键修复：使用数据包中的原始源IP作为响应包的目标IP
		// srcIPAddr 是从数据包中解析出的原始源IP（例如：*************）
		// 这是应用程序的真实IP，响应包应该返回到这个IP
		clientOriginalIP := srcIPAddr // 使用数据包中的原始源IP

		// 获取服务器本地IP
		serverLocalIP := s.getServerLocalIP() // 使用实际服务器IP *************

		// 检查是否已有映射
		if mapping, exists := s.addressMappingCache.Get(client.ID); exists {
			// 更新现有映射，使用数据包中的原始源IP
			s.addressMappingCache.Set(client.ID, clientOriginalIP, serverLocalIP, dstIPAddr)
			s.logger.Debug("Updated existing address mapping with packet source IP",
				zap.String("client_id", client.ID),
				zap.String("client_original_ip", clientOriginalIP.String()),
				zap.String("packet_src_ip", srcIP),
				zap.String("target_ip", dstIP),
				zap.Int64("packet_count", mapping.PacketCount+1))
		} else {
			// 创建新映射，使用数据包中的原始源IP
			s.addressMappingCache.Set(client.ID, clientOriginalIP, serverLocalIP, dstIPAddr)
			s.logger.Debug("Created new address mapping with packet source IP",
				zap.String("client_id", client.ID),
				zap.String("client_original_ip", clientOriginalIP.String()),
				zap.String("packet_src_ip", srcIP),
				zap.String("server_local_ip", serverLocalIP.String()),
				zap.String("target_ip", dstIP))
		}
	}

	// 转发数据包并获取响应
	var responsePacket []byte
	var err error

	// 🔧 强制使用传统转发逻辑以支持TCP处理器
	// 优化转发处理器不支持我们的TCP处理器修复
	responsePacket, err = s.forwardPacketWithResponse(msg.Data, client.ID)

	if err != nil {
		s.logger.Error("Failed to forward packet",
			zap.String("client_id", client.ID),
			zap.String("message_id", msg.ID),
			zap.Bool("optimized", !s.legacyForwarding),
			zap.Error(err))
		return s.sendError(client, msg.ID, err)
	}

	// 如果有响应数据包，发送TUN响应消息
	if responsePacket != nil && len(responsePacket) > 0 {
		if err := s.sendTunResponse(client, msg.ID, responsePacket); err != nil {
			s.logger.Error("Failed to send TUN response",
				zap.String("client_id", client.ID),
				zap.String("message_id", msg.ID),
				zap.Error(err))
			return err
		}
		s.logger.Debug("TUN response sent to client",
			zap.String("client_id", client.ID),
			zap.String("message_id", msg.ID),
			zap.Int("response_length", len(responsePacket)))
	}

	// 发送确认响应
	response := fmt.Sprintf("Packet forwarded (%d bytes)", len(msg.Data))
	return s.sendResponse(client, msg.ID, []byte(response))
}

// forwardPacket 转发数据包到目标地址
func (s *Server) forwardPacket(packet []byte, clientID string) error {
	if len(packet) < 20 {
		return fmt.Errorf("packet too short (minimum IPv4 header is 20 bytes)")
	}

	// 解析IP头部
	version := packet[0] >> 4
	switch version {
	case 4:
		return s.forwardIPv4Packet(packet, clientID)
	case 6:
		return s.forwardIPv6Packet(packet, clientID)
	default:
		return fmt.Errorf("unsupported IP version: %d", version)
	}
}

// handleOptimizedForwarding 使用优化转发处理器处理数据包
func (s *Server) handleOptimizedForwarding(packet []byte, clientID string) ([]byte, error) {
	// 使用同步方式处理，超时时间为30秒
	response, err := s.forwardingHandler.HandlePacketSync(packet, clientID, time.Second*30)
	if err != nil {
		s.logger.Debug("Optimized forwarding failed, falling back to legacy mode",
			zap.String("client_id", clientID),
			zap.Error(err))

		// 降级到传统转发
		return s.forwardPacketWithResponse(packet, clientID)
	}

	return response, nil
}

// forwardPacketWithResponse 转发数据包到目标地址并获取响应
func (s *Server) forwardPacketWithResponse(packet []byte, clientID string) ([]byte, error) {
	if len(packet) < 20 {
		return nil, fmt.Errorf("packet too short (minimum IPv4 header is 20 bytes)")
	}

	// 解析IP头部
	version := packet[0] >> 4
	switch version {
	case 4:
		return s.forwardIPv4PacketWithResponse(packet, clientID)
	case 6:
		return s.forwardIPv6PacketWithResponse(packet, clientID)
	default:
		return nil, fmt.Errorf("unsupported IP version: %d", version)
	}
}

// forwardIPv4Packet 转发IPv4数据包
func (s *Server) forwardIPv4Packet(packet []byte, clientID string) error {
	if len(packet) < 20 {
		return fmt.Errorf("IPv4 packet too short (minimum header is 20 bytes)")
	}

	// 获取源IP和目标IP地址
	srcIP := net.IP(packet[12:16])
	dstIP := net.IP(packet[16:20])

	// 获取协议类型
	protocol := packet[9]
	protocolStr := tun.ProtocolToString(protocol, false)

	// 记录转发信息
	s.logger.Debug("Forwarding IPv4 packet",
		zap.String("client_id", clientID),
		zap.String("src_ip", srcIP.String()),
		zap.String("dst_ip", dstIP.String()),
		zap.Int("packet_size", len(packet)),
		zap.String("protocol", protocolStr))

	// 检查是否允许转发到此目标
	if !s.isForwardingAllowed(dstIP) {
		return fmt.Errorf("forwarding to %s is not allowed", dstIP.String())
	}

	// 根据协议类型转发
	switch protocol {
	case 1: // ICMP
		return s.forwardICMP(packet, dstIP, clientID)
	case 6: // TCP
		return s.forwardTCP(packet, dstIP, clientID)
	case 17: // UDP
		return s.forwardUDP(packet, dstIP, clientID)
	default:
		return s.forwardRaw(packet, dstIP, clientID)
	}
}

// forwardIPv6Packet 转发IPv6数据包
func (s *Server) forwardIPv6Packet(packet []byte, clientID string) error {
	if len(packet) < 40 {
		return fmt.Errorf("IPv6 packet too short (minimum header is 40 bytes)")
	}

	// 获取源IP和目标IP地址 (IPv6源地址在字节8-23，目标地址在字节24-39)
	srcIP := net.IP(packet[8:24])
	dstIP := net.IP(packet[24:40])

	// 获取下一个头部类型 (IPv6中的协议字段)
	nextHeader := packet[6]
	protocolStr := tun.ProtocolToString(nextHeader, true)

	// 记录转发信息
	s.logger.Debug("Forwarding IPv6 packet",
		zap.String("client_id", clientID),
		zap.String("src_ip", srcIP.String()),
		zap.String("dst_ip", dstIP.String()),
		zap.Int("packet_size", len(packet)),
		zap.String("protocol", protocolStr))

	// 检查是否允许转发到此目标
	if !s.isForwardingAllowed(dstIP) {
		return fmt.Errorf("forwarding to %s is not allowed", dstIP.String())
	}

	// 根据协议类型转发
	switch nextHeader {
	case 58: // ICMPv6
		return s.forwardICMPv6(packet, dstIP, clientID)
	case 6: // TCP
		return s.forwardTCPv6(packet, dstIP, clientID)
	case 17: // UDP
		return s.forwardUDPv6(packet, dstIP, clientID)
	default:
		return s.forwardRawv6(packet, dstIP, clientID)
	}
}

// getServerLocalIP 获取服务器本地IP地址
func (s *Server) getServerLocalIP() net.IP {
	// 直接使用实际服务器IP
	return net.ParseIP("*************")
}

// getClientRealIP 获取客户端真实IP地址
func (s *Server) getClientRealIP(client *Client) net.IP {
	if client.Conn != nil {
		if remoteAddr := client.Conn.RemoteAddr(); remoteAddr != nil {
			if tcpAddr, ok := remoteAddr.(*net.TCPAddr); ok {
				return tcpAddr.IP
			}
		}
	}
	return nil
}

// isForwardingAllowed 检查是否允许转发到指定IP
func (s *Server) isForwardingAllowed(dstIP net.IP) bool {
	// 检查阻止列表
	for _, blockedNet := range s.config.Forwarding.BlockedNetworks {
		_, network, err := net.ParseCIDR(blockedNet)
		if err != nil {
			s.logger.Warn("Invalid blocked network CIDR", zap.String("cidr", blockedNet))
			continue
		}
		if network.Contains(dstIP) {
			s.logger.Debug("Packet blocked by blocked networks", zap.String("dst_ip", dstIP.String()), zap.String("blocked_net", blockedNet))
			return false
		}
	}

	// 如果有允许列表，检查是否在允许列表中
	if len(s.config.Forwarding.AllowedNetworks) > 0 {
		for _, allowedNet := range s.config.Forwarding.AllowedNetworks {
			_, network, err := net.ParseCIDR(allowedNet)
			if err != nil {
				s.logger.Warn("Invalid allowed network CIDR", zap.String("cidr", allowedNet))
				continue
			}
			if network.Contains(dstIP) {
				return true
			}
		}
		// 不在允许列表中
		s.logger.Debug("Packet not in allowed networks", zap.String("dst_ip", dstIP.String()))
		return false
	}

	// 没有允许列表，默认允许（除了阻止列表）
	return true
}

// forwardICMP 转发ICMP数据包
func (s *Server) forwardICMP(packet []byte, dstIP net.IP, clientID string) error {
	s.logger.Debug("Forwarding ICMP packet",
		zap.String("client_id", clientID),
		zap.String("dst_ip", dstIP.String()),
		zap.Int("packet_size", len(packet)))

	// 创建原始套接字发送ICMP包
	conn, err := net.Dial("ip4:icmp", dstIP.String())
	if err != nil {
		return fmt.Errorf("failed to create ICMP connection: %w", err)
	}
	defer conn.Close()

	// 发送ICMP数据（跳过IP头部）
	icmpData := packet[20:] // 跳过20字节的IP头部
	_, err = conn.Write(icmpData)
	if err != nil {
		return fmt.Errorf("failed to send ICMP packet: %w", err)
	}

	s.logger.Debug("ICMP packet forwarded successfully",
		zap.String("client_id", clientID),
		zap.String("dst_ip", dstIP.String()))

	return nil
}

// forwardTCP 转发TCP数据包
func (s *Server) forwardTCP(packet []byte, dstIP net.IP, clientID string) error {
	if len(packet) < 40 { // 最小TCP包长度（20字节IP头 + 20字节TCP头）
		return fmt.Errorf("TCP packet too short")
	}

	// 获取源IP地址
	srcIP := net.IP(packet[12:16])

	// 解析TCP头部获取源端口和目标端口
	tcpHeader := packet[20:]
	srcPort := uint16(tcpHeader[0])<<8 | uint16(tcpHeader[1])
	dstPort := uint16(tcpHeader[2])<<8 | uint16(tcpHeader[3])

	s.logger.Debug("Forwarding TCP packet",
		zap.String("client_id", clientID),
		zap.String("src_ip", srcIP.String()),
		zap.Uint16("src_port", srcPort),
		zap.String("dst_ip", dstIP.String()),
		zap.Uint16("dst_port", dstPort),
		zap.Int("packet_size", len(packet)))

	// 对于TCP，我们需要建立连接并转发数据
	// 这里简化处理，实际应用中可能需要更复杂的TCP状态管理
	addr := net.JoinHostPort(dstIP.String(), fmt.Sprintf("%d", dstPort))

	// 如果配置了接口绑定，使用指定接口
	var conn net.Conn
	var err error

	if s.config.Forwarding.BindInterface && s.config.Forwarding.InterfaceName != "" {
		// 绑定到指定接口（这里简化处理）
		conn, err = net.Dial("tcp", addr)
	} else {
		conn, err = net.Dial("tcp", addr)
	}

	if err != nil {
		return fmt.Errorf("failed to connect to %s: %w", addr, err)
	}
	defer conn.Close()

	// 提取TCP载荷数据
	ipHeaderLen := int(packet[0]&0x0F) * 4
	tcpHeaderLen := int(tcpHeader[12]>>4) * 4
	payloadStart := ipHeaderLen + tcpHeaderLen

	if payloadStart < len(packet) {
		payload := packet[payloadStart:]
		if len(payload) > 0 {
			_, err = conn.Write(payload)
			if err != nil {
				return fmt.Errorf("failed to send TCP data: %w", err)
			}
		}
	}

	s.logger.Debug("TCP packet forwarded successfully",
		zap.String("client_id", clientID),
		zap.String("dst_addr", addr))

	return nil
}

// forwardUDP 转发UDP数据包
func (s *Server) forwardUDP(packet []byte, dstIP net.IP, clientID string) error {
	if len(packet) < 28 { // 最小UDP包长度（20字节IP头 + 8字节UDP头）
		return fmt.Errorf("UDP packet too short")
	}

	// 获取源IP地址
	srcIP := net.IP(packet[12:16])

	// 解析UDP头部获取源端口和目标端口
	udpHeader := packet[20:]
	srcPort := uint16(udpHeader[0])<<8 | uint16(udpHeader[1])
	dstPort := uint16(udpHeader[2])<<8 | uint16(udpHeader[3])

	s.logger.Debug("Forwarding UDP packet",
		zap.String("client_id", clientID),
		zap.String("src_ip", srcIP.String()),
		zap.Uint16("src_port", srcPort),
		zap.String("dst_ip", dstIP.String()),
		zap.Uint16("dst_port", dstPort),
		zap.Int("packet_size", len(packet)))

	addr := net.JoinHostPort(dstIP.String(), fmt.Sprintf("%d", dstPort))

	conn, err := net.Dial("udp", addr)
	if err != nil {
		return fmt.Errorf("failed to create UDP connection: %w", err)
	}
	defer conn.Close()

	// 提取UDP载荷数据
	udpPayload := packet[28:] // 跳过IP头(20) + UDP头(8)
	_, err = conn.Write(udpPayload)
	if err != nil {
		return fmt.Errorf("failed to send UDP packet: %w", err)
	}

	s.logger.Debug("UDP packet forwarded successfully",
		zap.String("client_id", clientID),
		zap.String("dst_addr", addr))

	return nil
}

// forwardRaw 转发其他协议的数据包
func (s *Server) forwardRaw(packet []byte, dstIP net.IP, clientID string) error {
	// 获取源IP地址
	srcIP := net.IP(packet[12:16])
	protocol := packet[9]

	s.logger.Debug("Forwarding raw packet",
		zap.String("client_id", clientID),
		zap.String("src_ip", srcIP.String()),
		zap.String("dst_ip", dstIP.String()),
		zap.Uint8("protocol", protocol),
		zap.Int("packet_size", len(packet)))

	// 对于其他协议，尝试使用原始套接字
	conn, err := net.Dial(fmt.Sprintf("ip4:%d", protocol), dstIP.String())
	if err != nil {
		return fmt.Errorf("failed to create raw connection for protocol %d: %w", protocol, err)
	}
	defer conn.Close()

	// 发送数据（跳过IP头部）
	payload := packet[20:]
	_, err = conn.Write(payload)
	if err != nil {
		return fmt.Errorf("failed to send raw packet: %w", err)
	}

	s.logger.Debug("Raw packet forwarded successfully",
		zap.String("client_id", clientID),
		zap.String("dst_ip", dstIP.String()),
		zap.Uint8("protocol", protocol))

	return nil
}

// forwardICMPv6 转发ICMPv6数据包
func (s *Server) forwardICMPv6(packet []byte, dstIP net.IP, clientID string) error {
	s.logger.Debug("Forwarding ICMPv6 packet",
		zap.String("client_id", clientID),
		zap.String("dst_ip", dstIP.String()),
		zap.Int("packet_size", len(packet)))

	// 检查是否是多播地址
	if dstIP.IsMulticast() {
		s.logger.Debug("Dropping ICMPv6 packet to multicast address",
			zap.String("client_id", clientID),
			zap.String("dst_ip", dstIP.String()))
		// 对于多播地址，我们不转发，直接返回成功
		// 这避免了连接错误，因为多播地址不能直接连接
		return nil
	}

	// 检查是否是链路本地地址
	if dstIP.IsLinkLocalUnicast() {
		s.logger.Debug("Dropping ICMPv6 packet to link-local address",
			zap.String("client_id", clientID),
			zap.String("dst_ip", dstIP.String()))
		// 链路本地地址通常不应该通过隧道转发
		return nil
	}

	// 创建原始套接字发送ICMPv6包
	conn, err := net.Dial("ip6:ipv6-icmp", dstIP.String())
	if err != nil {
		return fmt.Errorf("failed to create ICMPv6 connection: %w", err)
	}
	defer conn.Close()

	// 发送ICMPv6数据（跳过IPv6头部）
	icmpData := packet[40:] // 跳过40字节的IPv6头部
	_, err = conn.Write(icmpData)
	if err != nil {
		return fmt.Errorf("failed to send ICMPv6 packet: %w", err)
	}

	s.logger.Debug("ICMPv6 packet forwarded successfully",
		zap.String("client_id", clientID),
		zap.String("dst_ip", dstIP.String()))

	return nil
}

// forwardTCPv6 转发TCP over IPv6数据包
func (s *Server) forwardTCPv6(packet []byte, dstIP net.IP, clientID string) error {
	if len(packet) < 60 { // 最小TCP over IPv6包长度（40字节IPv6头 + 20字节TCP头）
		return fmt.Errorf("TCP over IPv6 packet too short")
	}

	// 获取源IP地址 (IPv6源地址在字节8-23)
	srcIP := net.IP(packet[8:24])

	// 解析TCP头部获取源端口和目标端口
	tcpHeader := packet[40:]
	srcPort := uint16(tcpHeader[0])<<8 | uint16(tcpHeader[1])
	dstPort := uint16(tcpHeader[2])<<8 | uint16(tcpHeader[3])

	s.logger.Debug("Forwarding TCP over IPv6 packet",
		zap.String("client_id", clientID),
		zap.String("src_ip", srcIP.String()),
		zap.Uint16("src_port", srcPort),
		zap.String("dst_ip", dstIP.String()),
		zap.Uint16("dst_port", dstPort),
		zap.Int("packet_size", len(packet)))

	// 对于TCP，我们需要建立连接并转发数据
	addr := fmt.Sprintf("[%s]:%d", dstIP.String(), dstPort)

	// 如果配置了接口绑定，使用指定接口
	var conn net.Conn
	var err error

	if s.config.Forwarding.BindInterface && s.config.Forwarding.InterfaceName != "" {
		// 绑定到指定接口（这里简化处理）
		conn, err = net.Dial("tcp6", addr)
	} else {
		conn, err = net.Dial("tcp6", addr)
	}

	if err != nil {
		return fmt.Errorf("failed to connect to %s: %w", addr, err)
	}
	defer conn.Close()

	// 提取TCP载荷数据
	tcpHeaderLen := int(tcpHeader[12]>>4) * 4
	payloadStart := 40 + tcpHeaderLen // IPv6头部40字节 + TCP头部

	if payloadStart < len(packet) {
		payload := packet[payloadStart:]
		if len(payload) > 0 {
			_, err = conn.Write(payload)
			if err != nil {
				return fmt.Errorf("failed to send TCP data: %w", err)
			}
		}
	}

	s.logger.Debug("TCP over IPv6 packet forwarded successfully",
		zap.String("client_id", clientID),
		zap.String("dst_addr", addr))

	return nil
}

// forwardUDPv6 转发UDP over IPv6数据包
func (s *Server) forwardUDPv6(packet []byte, dstIP net.IP, clientID string) error {
	if len(packet) < 48 { // 最小UDP over IPv6包长度（40字节IPv6头 + 8字节UDP头）
		return fmt.Errorf("UDP over IPv6 packet too short")
	}

	// 获取源IP地址 (IPv6源地址在字节8-23)
	srcIP := net.IP(packet[8:24])

	// 解析UDP头部获取源端口和目标端口
	udpHeader := packet[40:]
	srcPort := uint16(udpHeader[0])<<8 | uint16(udpHeader[1])
	dstPort := uint16(udpHeader[2])<<8 | uint16(udpHeader[3])

	s.logger.Debug("Forwarding UDP over IPv6 packet",
		zap.String("client_id", clientID),
		zap.String("src_ip", srcIP.String()),
		zap.Uint16("src_port", srcPort),
		zap.String("dst_ip", dstIP.String()),
		zap.Uint16("dst_port", dstPort),
		zap.Int("packet_size", len(packet)))

	addr := fmt.Sprintf("[%s]:%d", dstIP.String(), dstPort)

	conn, err := net.Dial("udp6", addr)
	if err != nil {
		return fmt.Errorf("failed to create UDP over IPv6 connection: %w", err)
	}
	defer conn.Close()

	// 提取UDP载荷数据
	udpPayload := packet[48:] // 跳过IPv6头(40) + UDP头(8)
	_, err = conn.Write(udpPayload)
	if err != nil {
		return fmt.Errorf("failed to send UDP over IPv6 packet: %w", err)
	}

	s.logger.Debug("UDP over IPv6 packet forwarded successfully",
		zap.String("client_id", clientID),
		zap.String("dst_addr", addr))

	return nil
}

// forwardRawv6 转发其他IPv6协议的数据包
func (s *Server) forwardRawv6(packet []byte, dstIP net.IP, clientID string) error {
	// 获取源IP地址 (IPv6源地址在字节8-23)
	srcIP := net.IP(packet[8:24])
	nextHeader := packet[6]

	s.logger.Debug("Forwarding raw IPv6 packet",
		zap.String("client_id", clientID),
		zap.String("src_ip", srcIP.String()),
		zap.String("dst_ip", dstIP.String()),
		zap.Uint8("next_header", nextHeader),
		zap.Int("packet_size", len(packet)))

	// 对于其他协议，尝试使用原始套接字
	conn, err := net.Dial(fmt.Sprintf("ip6:%d", nextHeader), dstIP.String())
	if err != nil {
		return fmt.Errorf("failed to create raw IPv6 connection for protocol %d: %w", nextHeader, err)
	}
	defer conn.Close()

	// 发送数据（跳过IPv6头部）
	payload := packet[40:]
	_, err = conn.Write(payload)
	if err != nil {
		return fmt.Errorf("failed to send raw IPv6 packet: %w", err)
	}

	s.logger.Debug("Raw IPv6 packet forwarded successfully",
		zap.String("client_id", clientID),
		zap.String("dst_ip", dstIP.String()),
		zap.Uint8("next_header", nextHeader))

	return nil
}

// forwardIPv4PacketWithResponse 转发IPv4数据包并获取响应
func (s *Server) forwardIPv4PacketWithResponse(packet []byte, clientID string) ([]byte, error) {
	if len(packet) < 20 {
		return nil, fmt.Errorf("IPv4 packet too short (minimum header is 20 bytes)")
	}

	// 获取源IP和目标IP地址
	srcIP := net.IP(packet[12:16])
	dstIP := net.IP(packet[16:20])

	// 获取协议类型
	protocol := packet[9]
	protocolStr := tun.ProtocolToString(protocol, false)

	// 记录转发信息
	s.logger.Debug("Forwarding IPv4 packet with response",
		zap.String("client_id", clientID),
		zap.String("src_ip", srcIP.String()),
		zap.String("dst_ip", dstIP.String()),
		zap.Int("packet_size", len(packet)),
		zap.String("protocol", protocolStr))

	// 检查是否允许转发到此目标
	if !s.isForwardingAllowed(dstIP) {
		return nil, fmt.Errorf("forwarding to %s is not allowed", dstIP.String())
	}

	// 根据协议类型选择转发策略
	switch protocol {
	case 1: // ICMP - 使用原始套接字
		return s.forwardICMPWithResponse(packet, dstIP, clientID)
	case 6: // TCP - 使用新的TCP处理器
		return s.tcpHandler.HandleTCPPacket(packet, clientID)
	case 17: // UDP - 使用原始套接字
		return s.forwardUDPWithResponse(packet, dstIP, clientID)
	default: // 其他协议 - 使用原始套接字
		return s.forwardRawWithResponse(packet, dstIP, clientID)
	}
}

// forwardIPv6PacketWithResponse 转发IPv6数据包并获取响应
func (s *Server) forwardIPv6PacketWithResponse(packet []byte, clientID string) ([]byte, error) {
	if len(packet) < 40 {
		return nil, fmt.Errorf("IPv6 packet too short (minimum header is 40 bytes)")
	}

	// 获取源IP和目标IP地址 (IPv6源地址在字节8-23，目标地址在字节24-39)
	srcIP := net.IP(packet[8:24])
	dstIP := net.IP(packet[24:40])

	// 获取下一个头部类型 (IPv6中的协议字段)
	nextHeader := packet[6]
	protocolStr := tun.ProtocolToString(nextHeader, true)

	// 记录转发信息
	s.logger.Debug("Forwarding IPv6 packet with response",
		zap.String("client_id", clientID),
		zap.String("src_ip", srcIP.String()),
		zap.String("dst_ip", dstIP.String()),
		zap.Int("packet_size", len(packet)),
		zap.String("protocol", protocolStr))

	// 检查是否允许转发到此目标
	if !s.isForwardingAllowed(dstIP) {
		return nil, fmt.Errorf("forwarding to %s is not allowed", dstIP.String())
	}

	// 根据协议类型选择转发策略
	switch nextHeader {
	case 58: // ICMPv6 - 使用原始套接字
		return s.forwardICMPv6WithResponse(packet, dstIP, clientID)
	case 6: // TCP over IPv6 - 使用混合策略
		return s.forwardTCPv6WithHybridStrategy(packet, dstIP, clientID)
	case 17: // UDP over IPv6 - 使用原始套接字
		return s.forwardUDPv6WithResponse(packet, dstIP, clientID)
	default: // 其他IPv6协议 - 使用原始套接字
		return s.forwardRawv6WithResponse(packet, dstIP, clientID)
	}
}

// forwardUDPWithResponse 转发UDP数据包并获取响应
func (s *Server) forwardUDPWithResponse(packet []byte, dstIP net.IP, clientID string) ([]byte, error) {
	if len(packet) < 28 { // 最小UDP包长度（20字节IP头 + 8字节UDP头）
		return nil, fmt.Errorf("UDP packet too short")
	}

	// 获取源IP地址
	srcIP := net.IP(packet[12:16])

	// 解析UDP头部获取源端口和目标端口
	udpHeader := packet[20:]
	srcPort := uint16(udpHeader[0])<<8 | uint16(udpHeader[1])
	dstPort := uint16(udpHeader[2])<<8 | uint16(udpHeader[3])

	// 注意：现在所有UDP包都通过统一的原始套接字转发处理
	// 这个函数保留是为了向后兼容，但实际上不会被调用
	s.logger.Debug("Legacy UDP forwarding function called (should use unified raw socket)",
		zap.String("client_id", clientID),
		zap.String("src_ip", srcIP.String()),
		zap.Uint16("src_port", srcPort),
		zap.String("dst_ip", dstIP.String()),
		zap.Uint16("dst_port", dstPort),
		zap.Int("packet_size", len(packet)))

	addr := net.JoinHostPort(dstIP.String(), fmt.Sprintf("%d", dstPort))

	conn, err := net.Dial("udp", addr)
	if err != nil {
		return nil, fmt.Errorf("failed to create UDP connection: %w", err)
	}
	defer conn.Close()

	// 提取UDP载荷数据
	udpPayload := packet[28:] // 跳过IP头(20) + UDP头(8)
	_, err = conn.Write(udpPayload)
	if err != nil {
		return nil, fmt.Errorf("failed to send UDP packet: %w", err)
	}

	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))

	// 读取响应
	responseBuffer := make([]byte, 1500) // MTU大小的缓冲区
	n, err := conn.Read(responseBuffer)
	if err != nil {
		// 对于UDP，没有响应是正常的，不应该返回错误
		s.logger.Debug("No UDP response received (this is normal for UDP)",
			zap.String("client_id", clientID),
			zap.String("dst_addr", addr),
			zap.Error(err))
		return nil, nil
	}

	if n > 0 {
		s.logger.Debug("UDP response received",
			zap.String("client_id", clientID),
			zap.String("dst_addr", addr),
			zap.Int("response_size", n))

		// 使用地址映射缓存确保正确的IP地址映射
		var clientRealIP net.IP = srcIP // 默认使用数据包中的源IP

		// 从地址映射缓存中获取客户端真实IP
		if mapping, exists := s.addressMappingCache.Get(clientID); exists {
			clientRealIP = mapping.ClientRealIP
			s.logger.Debug("Using cached address mapping for UDP response",
				zap.String("client_id", clientID),
				zap.String("cached_client_real_ip", clientRealIP.String()),
				zap.String("packet_client_ip", srcIP.String()),
				zap.String("target_ip", dstIP.String()))
		} else {
			s.logger.Debug("No cached address mapping found for UDP, using packet source IP",
				zap.String("client_id", clientID),
				zap.String("packet_client_ip", srcIP.String()),
				zap.String("target_ip", dstIP.String()))
		}

		// 构造完整的UDP响应数据包，使用数据包原始源IP
		responsePacket, err := s.buildUDPResponsePacket(srcIP, srcPort, dstIP, dstPort, responseBuffer[:n])
		if err != nil {
			return nil, fmt.Errorf("failed to build UDP response packet: %w", err)
		}

		s.logger.Debug("UDP response packet constructed with address mapping",
			zap.String("client_id", clientID),
			zap.String("response_src_ip", dstIP.String()),
			zap.String("response_dst_ip", srcIP.String()),
			zap.String("original_packet_src", srcIP.String()),
			zap.Int("response_packet_len", len(responsePacket)))

		return responsePacket, nil
	}

	return nil, nil
}

// buildUDPResponsePacket 构造UDP响应数据包
func (s *Server) buildUDPResponsePacket(originalSrcIP net.IP, originalSrcPort uint16, originalDstIP net.IP, originalDstPort uint16, responseData []byte) ([]byte, error) {
	// 构造IPv4 UDP响应数据包
	// 响应数据包的源IP应该是原始的目标IP，目标IP应该是原始的源IP

	ipHeaderLen := 20
	udpHeaderLen := 8
	totalLen := ipHeaderLen + udpHeaderLen + len(responseData)

	packet := make([]byte, totalLen)

	// 构造IPv4头部
	packet[0] = 0x45                  // 版本(4) + 头部长度(5*4=20字节)
	packet[1] = 0x00                  // 服务类型
	packet[2] = byte(totalLen >> 8)   // 总长度高字节
	packet[3] = byte(totalLen & 0xFF) // 总长度低字节
	packet[4] = 0x00                  // 标识高字节
	packet[5] = 0x00                  // 标识低字节
	packet[6] = 0x40                  // 标志(DF=1) + 片偏移高3位
	packet[7] = 0x00                  // 片偏移低8位
	packet[8] = 64                    // TTL
	packet[9] = 17                    // 协议(UDP)
	packet[10] = 0x00                 // 头部校验和(稍后计算)
	packet[11] = 0x00

	// 源IP(原始的目标IP)
	copy(packet[12:16], originalDstIP.To4())
	// 目标IP(原始的源IP)
	copy(packet[16:20], originalSrcIP.To4())

	// 计算IP头部校验和
	ipChecksum := checksum.CalculateIPv4Checksum(packet[:20])
	packet[10] = byte(ipChecksum >> 8)
	packet[11] = byte(ipChecksum & 0xFF)

	// 构造UDP头部
	udpStart := 20
	packet[udpStart] = byte(originalDstPort >> 8)     // 源端口高字节(原始的目标端口)
	packet[udpStart+1] = byte(originalDstPort & 0xFF) // 源端口低字节
	packet[udpStart+2] = byte(originalSrcPort >> 8)   // 目标端口高字节(原始的源端口)
	packet[udpStart+3] = byte(originalSrcPort & 0xFF) // 目标端口低字节

	udpLen := udpHeaderLen + len(responseData)
	packet[udpStart+4] = byte(udpLen >> 8)   // UDP长度高字节
	packet[udpStart+5] = byte(udpLen & 0xFF) // UDP长度低字节
	packet[udpStart+6] = 0x00                // UDP校验和(稍后计算)
	packet[udpStart+7] = 0x00

	// 复制响应数据
	copy(packet[udpStart+8:], responseData)

	// 计算UDP校验和
	udpChecksum := checksum.CalculateUDPChecksum(originalDstIP, originalSrcIP, packet[udpStart:])
	packet[udpStart+6] = byte(udpChecksum >> 8)
	packet[udpStart+7] = byte(udpChecksum & 0xFF)

	return packet, nil
}

// calculateIPChecksum 计算IP头部校验和
func (s *Server) calculateIPChecksum(header []byte) uint16 {
	var sum uint32

	// 将头部按16位分组求和
	for i := 0; i < len(header); i += 2 {
		if i+1 < len(header) {
			sum += uint32(header[i])<<8 + uint32(header[i+1])
		} else {
			sum += uint32(header[i]) << 8
		}
	}

	// 处理进位
	for sum>>16 != 0 {
		sum = (sum & 0xFFFF) + (sum >> 16)
	}

	// 取反
	return uint16(^sum)
}

// calculateICMPChecksum 计算ICMP校验和
func (s *Server) calculateICMPChecksum(icmpData []byte) uint16 {
	var sum uint32

	// 将ICMP数据按16位分组求和
	for i := 0; i < len(icmpData); i += 2 {
		if i+1 < len(icmpData) {
			sum += uint32(icmpData[i])<<8 + uint32(icmpData[i+1])
		} else {
			sum += uint32(icmpData[i]) << 8
		}
	}

	// 处理进位
	for sum>>16 != 0 {
		sum = (sum & 0xFFFF) + (sum >> 16)
	}

	// 取反
	return uint16(^sum)
}

// calculateUDPChecksum 计算UDP校验和（包含IPv4伪头部）
func (s *Server) calculateUDPChecksum(srcIP, dstIP net.IP, udpData []byte) uint16 {
	var sum uint32

	// IPv4伪头部
	// 源IP
	sum += uint32(srcIP[0])<<8 + uint32(srcIP[1])
	sum += uint32(srcIP[2])<<8 + uint32(srcIP[3])
	// 目标IP
	sum += uint32(dstIP[0])<<8 + uint32(dstIP[1])
	sum += uint32(dstIP[2])<<8 + uint32(dstIP[3])
	// 协议号 (17 for UDP)
	sum += 17
	// UDP长度
	sum += uint32(len(udpData))

	// UDP数据
	for i := 0; i < len(udpData); i += 2 {
		if i+1 < len(udpData) {
			sum += uint32(udpData[i])<<8 + uint32(udpData[i+1])
		} else {
			sum += uint32(udpData[i]) << 8
		}
	}

	// 处理进位
	for sum>>16 != 0 {
		sum = (sum & 0xFFFF) + (sum >> 16)
	}

	// 取反
	return uint16(^sum)
}

// calculateTCPChecksum 计算TCP校验和（包含IPv4伪头部）
func (s *Server) calculateTCPChecksum(srcIP, dstIP net.IP, tcpData []byte) uint16 {
	var sum uint32

	// IPv4伪头部
	// 源IP
	sum += uint32(srcIP[0])<<8 + uint32(srcIP[1])
	sum += uint32(srcIP[2])<<8 + uint32(srcIP[3])
	// 目标IP
	sum += uint32(dstIP[0])<<8 + uint32(dstIP[1])
	sum += uint32(dstIP[2])<<8 + uint32(dstIP[3])
	// 协议号 (6 for TCP)
	sum += 6
	// TCP长度
	sum += uint32(len(tcpData))

	// TCP数据
	for i := 0; i < len(tcpData); i += 2 {
		if i+1 < len(tcpData) {
			sum += uint32(tcpData[i])<<8 + uint32(tcpData[i+1])
		} else {
			sum += uint32(tcpData[i]) << 8
		}
	}

	// 处理进位
	for sum>>16 != 0 {
		sum = (sum & 0xFFFF) + (sum >> 16)
	}

	// 取反
	return uint16(^sum)
}

// buildICMPv6ResponsePacket 构造ICMPv6响应数据包
func (s *Server) buildICMPv6ResponsePacket(originalSrcIP net.IP, originalDstIP net.IP, responseData []byte) ([]byte, error) {
	// 构造IPv6 ICMPv6响应数据包
	ipv6HeaderLen := 40
	totalLen := ipv6HeaderLen + len(responseData)

	packet := make([]byte, totalLen)

	// 构造IPv6头部
	packet[0] = 0x60 // 版本(6) + 流量类别高4位
	packet[1] = 0x00 // 流量类别低4位 + 流标签高4位
	packet[2] = 0x00 // 流标签中8位
	packet[3] = 0x00 // 流标签低8位
	payloadLen := len(responseData)
	packet[4] = byte(payloadLen >> 8)   // 载荷长度高字节
	packet[5] = byte(payloadLen & 0xFF) // 载荷长度低字节
	packet[6] = 58                      // 下一个头部(ICMPv6)
	packet[7] = 64                      // 跳数限制

	// 源IP(原始的目标IP)
	copy(packet[8:24], originalDstIP.To16())
	// 目标IP(原始的源IP)
	copy(packet[24:40], originalSrcIP.To16())

	// 复制ICMPv6响应数据
	copy(packet[40:], responseData)

	// 重新计算ICMPv6校验和
	if len(responseData) >= 4 {
		// 清零ICMPv6校验和字段
		packet[42] = 0x00
		packet[43] = 0x00

		// 计算ICMPv6校验和
		icmpChecksum := checksum.CalculateICMPv6Checksum(originalDstIP, originalSrcIP, packet[40:])
		packet[42] = byte(icmpChecksum >> 8)
		packet[43] = byte(icmpChecksum & 0xFF)
	}

	return packet, nil
}

// buildICMPResponsePacket 构造ICMP响应数据包
func (s *Server) buildICMPResponsePacket(originalSrcIP net.IP, originalDstIP net.IP, responseData []byte) ([]byte, error) {
	// 构造IPv4 ICMP响应数据包
	// 响应数据包的源IP应该是原始的目标IP，目标IP应该是原始的源IP

	s.logger.Debug("Building ICMP response packet",
		zap.String("original_src_ip", originalSrcIP.String()),
		zap.String("original_dst_ip", originalDstIP.String()),
		zap.String("response_will_have_src", originalDstIP.String()),
		zap.String("response_will_have_dst", originalSrcIP.String()),
		zap.Int("icmp_data_len", len(responseData)))

	ipHeaderLen := 20
	totalLen := ipHeaderLen + len(responseData)

	packet := make([]byte, totalLen)

	// 构造IPv4头部
	packet[0] = 0x45                  // 版本(4) + 头部长度(5*4=20字节)
	packet[1] = 0x00                  // 服务类型
	packet[2] = byte(totalLen >> 8)   // 总长度高字节
	packet[3] = byte(totalLen & 0xFF) // 总长度低字节
	packet[4] = 0x00                  // 标识高字节
	packet[5] = 0x00                  // 标识低字节
	packet[6] = 0x40                  // 标志(DF=1) + 片偏移高3位
	packet[7] = 0x00                  // 片偏移低8位
	packet[8] = 64                    // TTL
	packet[9] = 1                     // 协议(ICMP)
	packet[10] = 0x00                 // 头部校验和(稍后计算)
	packet[11] = 0x00

	// 源IP(原始的目标IP)
	copy(packet[12:16], originalDstIP.To4())
	// 目标IP(原始的源IP)
	copy(packet[16:20], originalSrcIP.To4())

	s.logger.Debug("IP addresses set in response packet",
		zap.String("packet_src_ip", net.IP(packet[12:16]).String()),
		zap.String("packet_dst_ip", net.IP(packet[16:20]).String()))

	// 计算IP头部校验和
	ipChecksum := checksum.CalculateIPv4Checksum(packet[:20])
	packet[10] = byte(ipChecksum >> 8)
	packet[11] = byte(ipChecksum & 0xFF)

	// 复制ICMP响应数据
	copy(packet[20:], responseData)

	// 重新计算ICMP校验和
	if len(responseData) >= 4 {
		// 清零ICMP校验和字段
		packet[22] = 0x00
		packet[23] = 0x00

		// 计算ICMP校验和
		icmpChecksum := checksum.CalculateICMPChecksum(packet[20:])
		packet[22] = byte(icmpChecksum >> 8)
		packet[23] = byte(icmpChecksum & 0xFF)
	}

	return packet, nil
}

// buildTCPResponsePacket 构造TCP响应数据包
func (s *Server) buildTCPResponsePacket(originalSrcIP net.IP, originalSrcPort uint16, originalDstIP net.IP, originalDstPort uint16, responseData []byte) ([]byte, error) {
	// 构造IPv4 TCP响应数据包
	// 响应数据包的源IP应该是原始的目标IP，目标IP应该是原始的源IP

	ipHeaderLen := 20
	tcpHeaderLen := 20 // 简化的TCP头部，不包含选项
	totalLen := ipHeaderLen + tcpHeaderLen + len(responseData)

	packet := make([]byte, totalLen)

	// 构造IPv4头部
	packet[0] = 0x45                  // 版本(4) + 头部长度(5*4=20字节)
	packet[1] = 0x00                  // 服务类型
	packet[2] = byte(totalLen >> 8)   // 总长度高字节
	packet[3] = byte(totalLen & 0xFF) // 总长度低字节
	packet[4] = 0x00                  // 标识高字节
	packet[5] = 0x00                  // 标识低字节
	packet[6] = 0x40                  // 标志(DF=1) + 片偏移高3位
	packet[7] = 0x00                  // 片偏移低8位
	packet[8] = 64                    // TTL
	packet[9] = 6                     // 协议(TCP)
	packet[10] = 0x00                 // 头部校验和(稍后计算)
	packet[11] = 0x00

	// 源IP(原始的目标IP)
	copy(packet[12:16], originalDstIP.To4())
	// 目标IP(原始的源IP)
	copy(packet[16:20], originalSrcIP.To4())

	// 计算IP头部校验和
	ipChecksum := checksum.CalculateIPv4Checksum(packet[:20])
	packet[10] = byte(ipChecksum >> 8)
	packet[11] = byte(ipChecksum & 0xFF)

	// 构造TCP头部
	tcpStart := 20
	packet[tcpStart] = byte(originalDstPort >> 8)     // 源端口高字节(原始的目标端口)
	packet[tcpStart+1] = byte(originalDstPort & 0xFF) // 源端口低字节
	packet[tcpStart+2] = byte(originalSrcPort >> 8)   // 目标端口高字节(原始的源端口)
	packet[tcpStart+3] = byte(originalSrcPort & 0xFF) // 目标端口低字节

	// 序列号(简化处理)
	packet[tcpStart+4] = 0x00
	packet[tcpStart+5] = 0x00
	packet[tcpStart+6] = 0x00
	packet[tcpStart+7] = 0x01

	// 确认号(简化处理)
	packet[tcpStart+8] = 0x00
	packet[tcpStart+9] = 0x00
	packet[tcpStart+10] = 0x00
	packet[tcpStart+11] = 0x01

	// 数据偏移(5*4=20字节) + 保留位 + 标志位(PSH+ACK)
	packet[tcpStart+12] = 0x50 // 数据偏移(5) + 保留位(0)
	packet[tcpStart+13] = 0x18 // 标志位(PSH+ACK)

	// 窗口大小
	packet[tcpStart+14] = 0xFF
	packet[tcpStart+15] = 0xFF

	// 校验和(稍后计算)
	packet[tcpStart+16] = 0x00
	packet[tcpStart+17] = 0x00

	// 紧急指针
	packet[tcpStart+18] = 0x00
	packet[tcpStart+19] = 0x00

	// 复制响应数据
	copy(packet[tcpStart+20:], responseData)

	// 计算TCP校验和
	tcpChecksum := checksum.CalculateTCPChecksum(originalDstIP, originalSrcIP, packet[tcpStart:])
	packet[tcpStart+16] = byte(tcpChecksum >> 8)
	packet[tcpStart+17] = byte(tcpChecksum & 0xFF)

	return packet, nil
}

// buildUDPv6ResponsePacket 构造UDP over IPv6响应数据包
func (s *Server) buildUDPv6ResponsePacket(originalSrcIP net.IP, originalDstIP net.IP, originalSrcPort, originalDstPort uint16, responseData []byte) ([]byte, error) {
	// 构造IPv6 UDP响应数据包
	ipv6HeaderLen := 40
	udpHeaderLen := 8
	totalLen := ipv6HeaderLen + udpHeaderLen + len(responseData)

	packet := make([]byte, totalLen)

	// 构造IPv6头部
	packet[0] = 0x60 // 版本(6) + 流量类别高4位
	packet[1] = 0x00 // 流量类别低4位 + 流标签高4位
	packet[2] = 0x00 // 流标签中8位
	packet[3] = 0x00 // 流标签低8位
	payloadLen := udpHeaderLen + len(responseData)
	packet[4] = byte(payloadLen >> 8)   // 载荷长度高字节
	packet[5] = byte(payloadLen & 0xFF) // 载荷长度低字节
	packet[6] = 17                      // 下一个头部(UDP)
	packet[7] = 64                      // 跳数限制

	// 源IP(原始的目标IP)
	copy(packet[8:24], originalDstIP.To16())
	// 目标IP(原始的源IP)
	copy(packet[24:40], originalSrcIP.To16())

	// 构造UDP头部
	udpStart := 40
	packet[udpStart] = byte(originalDstPort >> 8)     // 源端口高字节(原始的目标端口)
	packet[udpStart+1] = byte(originalDstPort & 0xFF) // 源端口低字节
	packet[udpStart+2] = byte(originalSrcPort >> 8)   // 目标端口高字节(原始的源端口)
	packet[udpStart+3] = byte(originalSrcPort & 0xFF) // 目标端口低字节

	udpLen := udpHeaderLen + len(responseData)
	packet[udpStart+4] = byte(udpLen >> 8)   // UDP长度高字节
	packet[udpStart+5] = byte(udpLen & 0xFF) // UDP长度低字节
	packet[udpStart+6] = 0x00                // UDP校验和(稍后计算)
	packet[udpStart+7] = 0x00

	// 复制响应数据
	copy(packet[udpStart+8:], responseData)

	// 计算UDP校验和
	udpChecksum := checksum.CalculateUDPv6Checksum(originalDstIP, originalSrcIP, packet[udpStart:])
	packet[udpStart+6] = byte(udpChecksum >> 8)
	packet[udpStart+7] = byte(udpChecksum & 0xFF)

	return packet, nil
}

// buildTCPv6ResponsePacket 构造TCP over IPv6响应数据包
func (s *Server) buildTCPv6ResponsePacket(originalSrcIP net.IP, originalDstIP net.IP, originalSrcPort, originalDstPort uint16, responseData []byte) ([]byte, error) {
	// 构造IPv6 TCP响应数据包
	ipv6HeaderLen := 40
	tcpHeaderLen := 20
	totalLen := ipv6HeaderLen + tcpHeaderLen + len(responseData)

	packet := make([]byte, totalLen)

	// 构造IPv6头部
	packet[0] = 0x60 // 版本(6) + 流量类别高4位
	packet[1] = 0x00 // 流量类别低4位 + 流标签高4位
	packet[2] = 0x00 // 流标签中8位
	packet[3] = 0x00 // 流标签低8位
	payloadLen := tcpHeaderLen + len(responseData)
	packet[4] = byte(payloadLen >> 8)   // 载荷长度高字节
	packet[5] = byte(payloadLen & 0xFF) // 载荷长度低字节
	packet[6] = 6                       // 下一个头部(TCP)
	packet[7] = 64                      // 跳数限制

	// 源IP(原始的目标IP)
	copy(packet[8:24], originalDstIP.To16())
	// 目标IP(原始的源IP)
	copy(packet[24:40], originalSrcIP.To16())

	// 构造TCP头部
	tcpStart := 40
	packet[tcpStart] = byte(originalDstPort >> 8)     // 源端口高字节(原始的目标端口)
	packet[tcpStart+1] = byte(originalDstPort & 0xFF) // 源端口低字节
	packet[tcpStart+2] = byte(originalSrcPort >> 8)   // 目标端口高字节(原始的源端口)
	packet[tcpStart+3] = byte(originalSrcPort & 0xFF) // 目标端口低字节

	// 序列号(简化处理)
	packet[tcpStart+4] = 0x00
	packet[tcpStart+5] = 0x00
	packet[tcpStart+6] = 0x00
	packet[tcpStart+7] = 0x01

	// 确认号(简化处理)
	packet[tcpStart+8] = 0x00
	packet[tcpStart+9] = 0x00
	packet[tcpStart+10] = 0x00
	packet[tcpStart+11] = 0x01

	// 数据偏移(5*4=20字节) + 保留位 + 标志位(PSH+ACK)
	packet[tcpStart+12] = 0x50 // 数据偏移(5) + 保留位(0)
	packet[tcpStart+13] = 0x18 // 标志位(PSH+ACK)

	// 窗口大小
	packet[tcpStart+14] = 0xFF
	packet[tcpStart+15] = 0xFF

	// 校验和(稍后计算)
	packet[tcpStart+16] = 0x00
	packet[tcpStart+17] = 0x00

	// 紧急指针
	packet[tcpStart+18] = 0x00
	packet[tcpStart+19] = 0x00

	// 复制响应数据
	copy(packet[tcpStart+20:], responseData)

	// 计算TCP校验和
	tcpChecksum := checksum.CalculateTCPv6Checksum(originalDstIP, originalSrcIP, packet[tcpStart:])
	packet[tcpStart+16] = byte(tcpChecksum >> 8)
	packet[tcpStart+17] = byte(tcpChecksum & 0xFF)

	return packet, nil
}

// forwardICMPWithResponse 转发ICMP数据包并获取响应
func (s *Server) forwardICMPWithResponse(packet []byte, dstIP net.IP, clientID string) ([]byte, error) {
	// 获取源IP地址（这是客户端的真实IP，必须保持不变）
	originalClientIP := net.IP(packet[12:16])

	s.logger.Debug("Forwarding ICMP packet with response",
		zap.String("client_id", clientID),
		zap.String("original_client_ip", originalClientIP.String()),
		zap.String("target_ip", dstIP.String()),
		zap.Int("packet_size", len(packet)),
		zap.String("debug_note", "Client IP must be preserved for correct response routing"))

	// 检查是否是多播或广播地址
	if dstIP.IsMulticast() || dstIP.Equal(net.IPv4bcast) {
		s.logger.Debug("ICMP multicast/broadcast address detected, skipping response",
			zap.String("client_id", clientID),
			zap.String("dst_ip", dstIP.String()))
		return nil, nil
	}

	// 尝试创建ICMP连接，使用权限检测和降级策略
	var conn net.Conn
	var err error

	// 尝试不同的协议名称
	protocols := []string{"ip4:icmp", "ip4:1"}
	for _, protocol := range protocols {
		conn, err = s.createRawSocketWithFallback(protocol, dstIP.String(), 1)
		if err == nil {
			break
		}
		s.logger.Debug("Failed to create ICMP connection with protocol",
			zap.String("protocol", protocol),
			zap.Error(err))
	}

	if err != nil {
		s.logger.Warn("Failed to create ICMP connection with all protocols and fallbacks, skipping response",
			zap.String("client_id", clientID),
			zap.String("dst_ip", dstIP.String()),
			zap.Error(err))
		return nil, nil // 不返回错误，只是跳过响应
	}
	defer conn.Close()

	// 发送ICMP数据（跳过IP头部）
	icmpData := packet[20:] // 跳过20字节的IP头部

	// 记录发送的ICMP数据详情
	if len(icmpData) >= 8 {
		icmpType := icmpData[0]
		icmpCode := icmpData[1]
		s.logger.Debug("Sending ICMP packet details",
			zap.String("client_id", clientID),
			zap.String("dst_ip", dstIP.String()),
			zap.Uint8("icmp_type", icmpType),
			zap.Uint8("icmp_code", icmpCode),
			zap.Int("icmp_data_len", len(icmpData)))
	}

	_, err = conn.Write(icmpData)
	if err != nil {
		s.logger.Debug("Failed to send ICMP packet",
			zap.String("client_id", clientID),
			zap.String("dst_ip", dstIP.String()),
			zap.Error(err))
		return nil, nil
	}

	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))

	// 读取响应
	responseBuffer := make([]byte, 1500)
	n, err := conn.Read(responseBuffer)
	if err != nil {
		s.logger.Debug("No ICMP response received",
			zap.String("client_id", clientID),
			zap.String("dst_ip", dstIP.String()),
			zap.Error(err))
		return nil, nil
	}

	if n > 0 {
		s.logger.Debug("ICMP response received",
			zap.String("client_id", clientID),
			zap.String("dst_ip", dstIP.String()),
			zap.Int("response_size", n))

		// 检查响应数据是否包含IP头部
		var icmpData []byte
		if n >= 20 && (responseBuffer[0]>>4) == 4 {
			// 响应包含IP头部，提取ICMP部分
			ipHeaderLen := int(responseBuffer[0]&0x0F) * 4
			if n > ipHeaderLen {
				icmpData = responseBuffer[ipHeaderLen:n]
				s.logger.Debug("Extracted ICMP data from IP packet",
					zap.String("client_id", clientID),
					zap.Int("ip_header_len", ipHeaderLen),
					zap.Int("icmp_data_len", len(icmpData)))
			} else {
				s.logger.Warn("Invalid IP packet received",
					zap.String("client_id", clientID),
					zap.Int("packet_size", n),
					zap.Int("ip_header_len", ipHeaderLen))
				return nil, nil
			}
		} else {
			// 响应只包含ICMP数据
			icmpData = responseBuffer[:n]
			s.logger.Debug("Using raw ICMP data",
				zap.String("client_id", clientID),
				zap.Int("icmp_data_len", len(icmpData)))
		}

		// 检查ICMP数据的有效性并处理echo reply
		if len(icmpData) >= 8 {
			icmpType := icmpData[0]
			s.logger.Debug("ICMP response details",
				zap.String("client_id", clientID),
				zap.Uint8("icmp_type", icmpType),
				zap.Int("icmp_data_len", len(icmpData)))

			// 使用地址映射缓存确保正确的IP地址映射
			var clientRealIP net.IP = originalClientIP // 默认使用数据包中的源IP

			// 从地址映射缓存中获取客户端真实IP
			if mapping, exists := s.addressMappingCache.Get(clientID); exists {
				clientRealIP = mapping.ClientRealIP
				s.logger.Debug("Using cached address mapping for response",
					zap.String("client_id", clientID),
					zap.String("cached_client_real_ip", clientRealIP.String()),
					zap.String("packet_client_ip", originalClientIP.String()),
					zap.String("target_ip", dstIP.String()))
			} else {
				s.logger.Debug("No cached address mapping found, using packet source IP",
					zap.String("client_id", clientID),
					zap.String("packet_client_ip", originalClientIP.String()),
					zap.String("target_ip", dstIP.String()))
			}

			// 构造响应包时，确保IP地址映射正确：
			// - 响应包的源IP应该是原始目标IP (dstIP)
			// - 响应包的目标IP应该是数据包原始源IP (originalClientIP)
			// 注意：buildICMPResponsePacket的参数顺序是(originalSrcIP, originalDstIP, responseData)
			responsePacket, err := s.buildICMPResponsePacket(originalClientIP, dstIP, icmpData)
			if err != nil {
				return nil, fmt.Errorf("failed to build ICMP response packet: %w", err)
			}

			s.logger.Debug("ICMP response packet constructed with address mapping",
				zap.String("client_id", clientID),
				zap.String("response_src_ip", dstIP.String()),
				zap.String("response_dst_ip", originalClientIP.String()),
				zap.String("original_packet_src", originalClientIP.String()),
				zap.Int("response_packet_len", len(responsePacket)))

			return responsePacket, nil
		} else {
			s.logger.Warn("Invalid ICMP data received",
				zap.String("client_id", clientID),
				zap.Int("icmp_data_len", len(icmpData)))
			return nil, nil
		}
	}

	return nil, nil
}

// forwardTCPWithResponse 转发TCP数据包并获取响应
func (s *Server) forwardTCPWithResponse(packet []byte, dstIP net.IP, clientID string) ([]byte, error) {
	if len(packet) < 40 { // 最小TCP包长度（20字节IP头 + 20字节TCP头）
		return nil, fmt.Errorf("TCP packet too short")
	}

	// 获取源IP地址
	srcIP := net.IP(packet[12:16])

	// 解析TCP头部获取源端口和目标端口
	tcpHeader := packet[20:]
	srcPort := uint16(tcpHeader[0])<<8 | uint16(tcpHeader[1])
	dstPort := uint16(tcpHeader[2])<<8 | uint16(tcpHeader[3])

	// 解析TCP标志位
	tcpFlags := tcpHeader[13]
	isSyn := (tcpFlags & 0x02) != 0
	isAck := (tcpFlags & 0x10) != 0
	isFin := (tcpFlags & 0x01) != 0
	isRst := (tcpFlags & 0x04) != 0
	isPsh := (tcpFlags & 0x08) != 0

	s.logger.Debug("Forwarding TCP packet with response",
		zap.String("client_id", clientID),
		zap.String("src_ip", srcIP.String()),
		zap.Uint16("src_port", srcPort),
		zap.String("dst_ip", dstIP.String()),
		zap.Uint16("dst_port", dstPort),
		zap.Int("packet_size", len(packet)),
		zap.Bool("syn", isSyn),
		zap.Bool("ack", isAck),
		zap.Bool("fin", isFin),
		zap.Bool("rst", isRst),
		zap.Bool("psh", isPsh))

	// 🔧 TCP转发策略优化：优先使用应用层转发
	// 原始套接字转发在某些环境下可能有权限或响应接收问题
	// 对于SYN包，尝试应用层转发，如果失败再使用原始套接字
	if isSyn {
		s.logger.Debug("TCP SYN packet detected, trying application layer forwarding first",
			zap.String("client_id", clientID))

		// 先尝试应用层转发
		response, err := s.forwardTCPApplicationLayer(packet, dstIP, clientID)
		if err == nil && response != nil {
			s.logger.Debug("TCP SYN handled successfully with application layer forwarding",
				zap.String("client_id", clientID))
			return response, nil
		}

		s.logger.Debug("Application layer forwarding failed, falling back to raw socket",
			zap.String("client_id", clientID),
			zap.Error(err))

		// 尝试原始套接字转发
		rawResponse, rawErr := s.forwardTCPRawWithResponse(packet, dstIP, clientID)
		if rawErr == nil && rawResponse != nil {
			return rawResponse, nil
		}

		s.logger.Debug("Both application layer and raw socket forwarding failed",
			zap.String("client_id", clientID),
			zap.Error(rawErr))

		// 🔧 作为最后的降级策略，为SYN包生成一个简单的响应
		// 这可以帮助某些应用程序继续工作
		if len(packet) >= 34 {
			tcpFlags := packet[33]
			if (tcpFlags & 0x02) != 0 { // SYN标志
				return s.generateSimpleTCPResponse(packet, clientID)
			}
		}

		return nil, nil
	}

	// 对于其他控制包，使用原始套接字转发
	if isFin || isRst || (!isPsh && !isAck) {
		s.logger.Debug("TCP control packet detected, using raw socket forwarding",
			zap.String("client_id", clientID),
			zap.Bool("fin", isFin),
			zap.Bool("rst", isRst))

		return s.forwardTCPRawWithResponse(packet, dstIP, clientID)
	}

	addr := net.JoinHostPort(dstIP.String(), fmt.Sprintf("%d", dstPort))

	// 对于数据包，建立TCP连接进行应用层转发
	s.logger.Debug("TCP data packet detected, using application layer forwarding",
		zap.String("client_id", clientID),
		zap.String("dst_addr", addr))

	conn, err := net.DialTimeout("tcp", addr, 10*time.Second)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to %s: %w", addr, err)
	}
	defer conn.Close()

	// 提取TCP载荷数据
	ipHeaderLen := int(packet[0]&0x0F) * 4
	tcpHeaderLen := int(tcpHeader[12]>>4) * 4
	payloadStart := ipHeaderLen + tcpHeaderLen

	if payloadStart < len(packet) {
		payload := packet[payloadStart:]
		if len(payload) > 0 {
			_, err = conn.Write(payload)
			if err != nil {
				return nil, fmt.Errorf("failed to send TCP data: %w", err)
			}
		}
	}

	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))

	// 读取响应
	responseBuffer := make([]byte, 1500)
	n, err := conn.Read(responseBuffer)
	if err != nil {
		s.logger.Debug("No TCP response received",
			zap.String("client_id", clientID),
			zap.String("dst_addr", addr),
			zap.Error(err))
		return nil, nil
	}

	if n > 0 {
		s.logger.Debug("TCP response received",
			zap.String("client_id", clientID),
			zap.String("dst_addr", addr),
			zap.Int("response_size", n))

		// 构造完整的TCP响应数据包
		responsePacket, err := s.buildTCPResponsePacket(srcIP, srcPort, dstIP, dstPort, responseBuffer[:n])
		if err != nil {
			return nil, fmt.Errorf("failed to build TCP response packet: %w", err)
		}

		return responsePacket, nil
	}

	return nil, nil
}

// forwardRawWithResponse 转发其他协议的数据包并获取响应
func (s *Server) forwardRawWithResponse(packet []byte, dstIP net.IP, clientID string) ([]byte, error) {
	// 获取源IP地址
	srcIP := net.IP(packet[12:16])
	protocol := packet[9]

	s.logger.Debug("Forwarding raw packet with response",
		zap.String("client_id", clientID),
		zap.String("src_ip", srcIP.String()),
		zap.String("dst_ip", dstIP.String()),
		zap.Uint8("protocol", protocol),
		zap.Int("packet_size", len(packet)))

	// 对于原始协议，我们只能尝试发送，但很难获取响应
	// 这里我们记录日志并返回nil，表示没有响应
	s.logger.Debug("Raw protocol forwarding completed (no response expected)",
		zap.String("client_id", clientID),
		zap.String("dst_ip", dstIP.String()),
		zap.Uint8("protocol", protocol))

	return nil, nil
}

// forwardUDPv6WithResponse 转发UDP over IPv6数据包并获取响应
func (s *Server) forwardUDPv6WithResponse(packet []byte, dstIP net.IP, clientID string) ([]byte, error) {
	if len(packet) < 48 { // 最小UDP over IPv6包长度（40字节IPv6头 + 8字节UDP头）
		return nil, fmt.Errorf("UDP over IPv6 packet too short")
	}

	// 获取源IP地址 (IPv6源地址在字节8-23)
	srcIP := net.IP(packet[8:24])

	// 解析UDP头部获取源端口和目标端口
	udpHeader := packet[40:]
	srcPort := uint16(udpHeader[0])<<8 | uint16(udpHeader[1])
	dstPort := uint16(udpHeader[2])<<8 | uint16(udpHeader[3])

	s.logger.Debug("Forwarding UDP over IPv6 packet with response",
		zap.String("client_id", clientID),
		zap.String("src_ip", srcIP.String()),
		zap.Uint16("src_port", srcPort),
		zap.String("dst_ip", dstIP.String()),
		zap.Uint16("dst_port", dstPort),
		zap.Int("packet_size", len(packet)))

	addr := net.JoinHostPort(dstIP.String(), fmt.Sprintf("%d", dstPort))

	conn, err := net.Dial("udp", addr)
	if err != nil {
		return nil, fmt.Errorf("failed to create UDP connection: %w", err)
	}
	defer conn.Close()

	// 提取UDP载荷数据
	udpPayload := packet[48:] // 跳过IPv6头(40) + UDP头(8)
	_, err = conn.Write(udpPayload)
	if err != nil {
		return nil, fmt.Errorf("failed to send UDP packet: %w", err)
	}

	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))

	// 读取响应
	responseBuffer := make([]byte, 1500)
	n, err := conn.Read(responseBuffer)
	if err != nil {
		s.logger.Debug("No UDP over IPv6 response received",
			zap.String("client_id", clientID),
			zap.String("dst_addr", addr),
			zap.Error(err))
		return nil, nil
	}

	if n > 0 {
		s.logger.Debug("UDP over IPv6 response received",
			zap.String("client_id", clientID),
			zap.String("dst_addr", addr),
			zap.Int("response_size", n))

		// 构造完整的IPv6 UDP响应数据包
		responsePacket, err := s.buildUDPv6ResponsePacket(srcIP, dstIP, srcPort, dstPort, responseBuffer[:n])
		if err != nil {
			return nil, fmt.Errorf("failed to build UDP over IPv6 response packet: %w", err)
		}

		return responsePacket, nil
	}

	return nil, nil
}

// forwardICMPv6WithResponse 转发ICMPv6数据包并获取响应
func (s *Server) forwardICMPv6WithResponse(packet []byte, dstIP net.IP, clientID string) ([]byte, error) {
	// 获取源IP地址 (IPv6源地址在字节8-23)
	srcIP := net.IP(packet[8:24])

	s.logger.Debug("Forwarding ICMPv6 packet with response",
		zap.String("client_id", clientID),
		zap.String("src_ip", srcIP.String()),
		zap.String("dst_ip", dstIP.String()),
		zap.Int("packet_size", len(packet)))

	// 检查是否是多播地址
	if dstIP.IsMulticast() {
		s.logger.Debug("ICMPv6 multicast address detected, skipping response",
			zap.String("client_id", clientID),
			zap.String("dst_ip", dstIP.String()))
		return nil, nil
	}

	// 检查是否是链路本地地址
	if dstIP.IsLinkLocalUnicast() {
		s.logger.Debug("ICMPv6 link-local address detected, may not work properly",
			zap.String("client_id", clientID),
			zap.String("dst_ip", dstIP.String()))
	}

	// 尝试创建ICMPv6连接，使用不同的协议名称
	var conn net.Conn
	var err error

	// 尝试不同的协议名称，使用权限检测和降级策略
	protocols := []string{"ip6:icmp", "ip6:ipv6-icmp", "ip6:58"}
	for _, protocol := range protocols {
		conn, err = s.createRawSocketWithFallback(protocol, dstIP.String(), 58)
		if err == nil {
			break
		}
		s.logger.Debug("Failed to create ICMPv6 connection with protocol",
			zap.String("protocol", protocol),
			zap.Error(err))
	}

	if err != nil {
		s.logger.Warn("Failed to create ICMPv6 connection with all protocols and fallbacks, skipping response",
			zap.String("client_id", clientID),
			zap.String("dst_ip", dstIP.String()),
			zap.Error(err))
		return nil, nil // 不返回错误，只是跳过响应
	}
	defer conn.Close()

	// 发送ICMPv6数据（跳过IPv6头部）
	icmpData := packet[40:] // 跳过40字节的IPv6头部
	_, err = conn.Write(icmpData)
	if err != nil {
		s.logger.Debug("Failed to send ICMPv6 packet",
			zap.String("client_id", clientID),
			zap.String("dst_ip", dstIP.String()),
			zap.Error(err))
		return nil, nil
	}

	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))

	// 读取响应
	responseBuffer := make([]byte, 1500)
	n, err := conn.Read(responseBuffer)
	if err != nil {
		s.logger.Debug("No ICMPv6 response received",
			zap.String("client_id", clientID),
			zap.String("dst_ip", dstIP.String()),
			zap.Error(err))
		return nil, nil
	}

	if n > 0 {
		s.logger.Debug("ICMPv6 response received",
			zap.String("client_id", clientID),
			zap.String("dst_ip", dstIP.String()),
			zap.Int("response_size", n))

		// 检查响应数据是否包含IPv6头部
		var icmpData []byte
		if n >= 40 && (responseBuffer[0]>>4) == 6 {
			// 响应包含IPv6头部，提取ICMPv6部分
			icmpData = responseBuffer[40:n]
			s.logger.Debug("Extracted ICMPv6 data from IPv6 packet",
				zap.String("client_id", clientID),
				zap.Int("icmp_data_len", len(icmpData)))
		} else {
			// 响应只包含ICMPv6数据
			icmpData = responseBuffer[:n]
			s.logger.Debug("Using raw ICMPv6 data",
				zap.String("client_id", clientID),
				zap.Int("icmp_data_len", len(icmpData)))
		}

		// 检查ICMPv6数据的有效性
		if len(icmpData) >= 8 {
			icmpType := icmpData[0]
			s.logger.Debug("ICMPv6 response details",
				zap.String("client_id", clientID),
				zap.Uint8("icmp_type", icmpType),
				zap.Int("icmp_data_len", len(icmpData)))

			// 构造完整的ICMPv6响应数据包
			responsePacket, err := s.buildICMPv6ResponsePacket(srcIP, dstIP, icmpData)
			if err != nil {
				return nil, fmt.Errorf("failed to build ICMPv6 response packet: %w", err)
			}

			return responsePacket, nil
		} else {
			s.logger.Warn("Invalid ICMPv6 data received",
				zap.String("client_id", clientID),
				zap.Int("icmp_data_len", len(icmpData)))
			return nil, nil
		}
	}

	return nil, nil
}

// forwardTCPv6WithResponse 转发TCP over IPv6数据包并获取响应
func (s *Server) forwardTCPv6WithResponse(packet []byte, dstIP net.IP, clientID string) ([]byte, error) {
	if len(packet) < 60 { // 最小TCP over IPv6包长度（40字节IPv6头 + 20字节TCP头）
		return nil, fmt.Errorf("TCP over IPv6 packet too short")
	}

	// 获取源IP地址 (IPv6源地址在字节8-23)
	srcIP := net.IP(packet[8:24])

	// 解析TCP头部获取源端口和目标端口
	tcpHeader := packet[40:]
	srcPort := uint16(tcpHeader[0])<<8 | uint16(tcpHeader[1])
	dstPort := uint16(tcpHeader[2])<<8 | uint16(tcpHeader[3])

	s.logger.Debug("Forwarding TCP over IPv6 packet with response",
		zap.String("client_id", clientID),
		zap.String("src_ip", srcIP.String()),
		zap.Uint16("src_port", srcPort),
		zap.String("dst_ip", dstIP.String()),
		zap.Uint16("dst_port", dstPort),
		zap.Int("packet_size", len(packet)))

	addr := net.JoinHostPort(dstIP.String(), fmt.Sprintf("%d", dstPort))

	// 建立TCP连接
	conn, err := net.DialTimeout("tcp", addr, 10*time.Second)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to %s: %w", addr, err)
	}
	defer conn.Close()

	// 提取TCP载荷数据
	tcpHeaderLen := int(tcpHeader[12]>>4) * 4
	payloadStart := 40 + tcpHeaderLen // IPv6头(40) + TCP头长度

	if payloadStart < len(packet) {
		payload := packet[payloadStart:]
		if len(payload) > 0 {
			_, err = conn.Write(payload)
			if err != nil {
				return nil, fmt.Errorf("failed to send TCP data: %w", err)
			}
		}
	}

	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))

	// 读取响应
	responseBuffer := make([]byte, 1500)
	n, err := conn.Read(responseBuffer)
	if err != nil {
		s.logger.Debug("No TCP over IPv6 response received",
			zap.String("client_id", clientID),
			zap.String("dst_addr", addr),
			zap.Error(err))
		return nil, nil
	}

	if n > 0 {
		s.logger.Debug("TCP over IPv6 response received",
			zap.String("client_id", clientID),
			zap.String("dst_addr", addr),
			zap.Int("response_size", n))

		// 构造完整的IPv6 TCP响应数据包
		responsePacket, err := s.buildTCPv6ResponsePacket(srcIP, dstIP, srcPort, dstPort, responseBuffer[:n])
		if err != nil {
			return nil, fmt.Errorf("failed to build TCP over IPv6 response packet: %w", err)
		}

		return responsePacket, nil
	}

	return nil, nil
}

// forwardRawv6WithResponse 转发其他IPv6协议的数据包并获取响应
func (s *Server) forwardRawv6WithResponse(packet []byte, dstIP net.IP, clientID string) ([]byte, error) {
	// 获取源IP地址 (IPv6源地址在字节8-23)
	srcIP := net.IP(packet[8:24])
	nextHeader := packet[6]

	s.logger.Debug("Forwarding raw IPv6 packet with response",
		zap.String("client_id", clientID),
		zap.String("src_ip", srcIP.String()),
		zap.String("dst_ip", dstIP.String()),
		zap.Uint8("next_header", nextHeader),
		zap.Int("packet_size", len(packet)))

	// 对于原始IPv6协议，我们只能尝试发送，但很难获取响应
	s.logger.Debug("Raw IPv6 protocol forwarding completed (no response expected)",
		zap.String("client_id", clientID),
		zap.String("dst_ip", dstIP.String()),
		zap.Uint8("next_header", nextHeader))

	return nil, nil
}

// forwardTCPRawWithResponse 使用原始套接字转发TCP控制包并获取响应
func (s *Server) forwardTCPRawWithResponse(packet []byte, dstIP net.IP, clientID string) ([]byte, error) {
	// 获取源IP地址
	srcIP := net.IP(packet[12:16])

	// 解析原始包的TCP头部获取端口信息
	tcpHeader := packet[20:]
	originalSrcPort := uint16(tcpHeader[0])<<8 | uint16(tcpHeader[1])
	originalDstPort := uint16(tcpHeader[2])<<8 | uint16(tcpHeader[3])

	s.logger.Debug("Forwarding TCP packet using raw socket",
		zap.String("client_id", clientID),
		zap.String("src_ip", srcIP.String()),
		zap.String("dst_ip", dstIP.String()),
		zap.Uint16("src_port", originalSrcPort),
		zap.Uint16("dst_port", originalDstPort),
		zap.Int("packet_size", len(packet)))

	// 创建原始套接字，使用权限检测和降级策略
	conn, err := s.createRawSocketWithFallback("ip4:tcp", dstIP.String(), 6)
	if err != nil {
		s.logger.Debug("Failed to create raw TCP socket",
			zap.String("client_id", clientID),
			zap.String("dst_ip", dstIP.String()),
			zap.Error(err))
		return nil, nil // 不返回错误，只是跳过响应
	}
	defer conn.Close()

	// 发送TCP数据（跳过IP头部）
	tcpData := packet[20:] // 跳过20字节的IP头部

	// 记录发送的TCP数据详情
	if len(tcpData) >= 20 {
		tcpFlags := tcpData[13]
		s.logger.Debug("Sending TCP packet details",
			zap.String("client_id", clientID),
			zap.String("dst_ip", dstIP.String()),
			zap.Uint8("tcp_flags", tcpFlags),
			zap.Int("tcp_data_len", len(tcpData)))
	}

	_, err = conn.Write(tcpData)
	if err != nil {
		s.logger.Debug("Failed to send TCP packet",
			zap.String("client_id", clientID),
			zap.String("dst_ip", dstIP.String()),
			zap.Error(err))
		return nil, nil
	}

	// 设置读取超时（针对TCP控制包优化）
	readTimeout := 8 * time.Second // 增加超时时间
	conn.SetReadDeadline(time.Now().Add(readTimeout))

	s.logger.Debug("Waiting for TCP raw response",
		zap.String("client_id", clientID),
		zap.String("dst_ip", dstIP.String()),
		zap.Duration("timeout", readTimeout))

	// 读取响应
	responseBuffer := make([]byte, 1500)
	n, err := conn.Read(responseBuffer)
	if err != nil {
		// 区分不同类型的错误
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			s.logger.Debug("TCP raw response timeout",
				zap.String("client_id", clientID),
				zap.String("dst_ip", dstIP.String()),
				zap.Duration("timeout", readTimeout))
		} else {
			s.logger.Debug("TCP raw response read error",
				zap.String("client_id", clientID),
				zap.String("dst_ip", dstIP.String()),
				zap.Error(err))
		}
		return nil, nil
	}

	if n > 0 {
		s.logger.Debug("TCP raw response received",
			zap.String("client_id", clientID),
			zap.String("dst_ip", dstIP.String()),
			zap.Int("response_size", n))

		// 检查响应数据是否包含IP头部
		var tcpData []byte
		if n >= 20 && (responseBuffer[0]>>4) == 4 {
			// 响应包含IP头部，提取TCP部分
			ipHeaderLen := int(responseBuffer[0]&0x0F) * 4
			if n > ipHeaderLen {
				tcpData = responseBuffer[ipHeaderLen:n]
				s.logger.Debug("Extracted TCP data from IP packet",
					zap.String("client_id", clientID),
					zap.Int("ip_header_len", ipHeaderLen),
					zap.Int("tcp_data_len", len(tcpData)))
			} else {
				s.logger.Warn("Invalid IP packet received",
					zap.String("client_id", clientID),
					zap.Int("packet_size", n),
					zap.Int("ip_header_len", ipHeaderLen))
				return nil, nil
			}
		} else {
			// 响应只包含TCP数据
			tcpData = responseBuffer[:n]
			s.logger.Debug("Using raw TCP data",
				zap.String("client_id", clientID),
				zap.Int("tcp_data_len", len(tcpData)))
		}

		// 检查TCP数据的有效性
		if len(tcpData) >= 20 {
			tcpFlags := tcpData[13]
			responseSrcPort := uint16(tcpData[0])<<8 | uint16(tcpData[1])
			responseDstPort := uint16(tcpData[2])<<8 | uint16(tcpData[3])

			s.logger.Debug("TCP raw response details",
				zap.String("client_id", clientID),
				zap.Uint16("response_src_port", responseSrcPort),
				zap.Uint16("response_dst_port", responseDstPort),
				zap.Uint8("tcp_flags", tcpFlags),
				zap.Int("tcp_data_len", len(tcpData)))

			// 使用地址映射缓存确保正确的IP地址映射
			var clientRealIP net.IP = srcIP // 默认使用数据包中的源IP

			// 从地址映射缓存中获取客户端真实IP
			if mapping, exists := s.addressMappingCache.Get(clientID); exists {
				clientRealIP = mapping.ClientRealIP
				s.logger.Debug("Using cached address mapping for TCP raw response",
					zap.String("client_id", clientID),
					zap.String("cached_client_real_ip", clientRealIP.String()),
					zap.String("packet_client_ip", srcIP.String()),
					zap.String("target_ip", dstIP.String()))
			} else {
				s.logger.Debug("No cached address mapping found for TCP raw, using packet source IP",
					zap.String("client_id", clientID),
					zap.String("packet_client_ip", srcIP.String()),
					zap.String("target_ip", dstIP.String()))
			}

			// 构造完整的TCP响应数据包，使用数据包原始源IP
			responsePacket, err := s.buildTCPResponsePacket(srcIP, originalSrcPort, dstIP, originalDstPort, tcpData)
			if err != nil {
				return nil, fmt.Errorf("failed to build TCP response packet: %w", err)
			}

			s.logger.Debug("TCP raw response packet constructed with address mapping",
				zap.String("client_id", clientID),
				zap.String("response_src_ip", dstIP.String()),
				zap.String("response_dst_ip", srcIP.String()),
				zap.String("original_packet_src", srcIP.String()),
				zap.Int("response_packet_len", len(responsePacket)))

			return responsePacket, nil
		} else {
			s.logger.Warn("Invalid TCP data received",
				zap.String("client_id", clientID),
				zap.Int("tcp_data_len", len(tcpData)))
			return nil, nil
		}
	}

	return nil, nil
}

// forwardRawPacketWithResponse 统一的IPv4原始套接字转发函数
func (s *Server) forwardRawPacketWithResponse(packet []byte, dstIP net.IP, clientID string) ([]byte, error) {
	if len(packet) < 20 {
		return nil, fmt.Errorf("IPv4 packet too short")
	}

	// 获取源IP和协议信息
	srcIP := net.IP(packet[12:16])
	protocol := packet[9]
	protocolStr := tun.ProtocolToString(protocol, false)

	s.logger.Debug("Forwarding IPv4 packet using unified raw socket",
		zap.String("client_id", clientID),
		zap.String("src_ip", srcIP.String()),
		zap.String("dst_ip", dstIP.String()),
		zap.Int("packet_size", len(packet)),
		zap.String("protocol", protocolStr))

	// 根据协议类型选择合适的原始套接字
	var protocolName string
	var payloadOffset int

	switch protocol {
	case 1: // ICMP
		protocolName = "ip4:icmp"
		payloadOffset = 20 // 跳过IP头部
	case 6: // TCP
		protocolName = "ip4:tcp"
		payloadOffset = 20 // 跳过IP头部
	case 17: // UDP
		protocolName = "ip4:udp"
		payloadOffset = 20 // 跳过IP头部
	default:
		// 对于其他协议，尝试使用通用的IP原始套接字
		protocolName = fmt.Sprintf("ip4:%d", protocol)
		payloadOffset = 20
	}

	// 创建原始套接字，使用权限检测和降级策略
	conn, err := s.createRawSocketWithFallback(protocolName, dstIP.String(), protocol)
	if err != nil {
		s.logger.Debug("Failed to create raw socket, trying alternative",
			zap.String("client_id", clientID),
			zap.String("protocol_name", protocolName),
			zap.Error(err))

		// 如果特定协议失败，尝试使用通用IP套接字
		if protocolName != "ip4:ip" {
			conn, err = s.createRawSocketWithFallback("ip4:ip", dstIP.String(), protocol)
			if err != nil {
				s.logger.Debug("Failed to create fallback raw socket",
					zap.String("client_id", clientID),
					zap.Error(err))
				return nil, nil // 不返回错误，只是跳过响应
			}
			payloadOffset = 0 // 发送完整的IP包
		} else {
			return nil, nil
		}
	}
	defer conn.Close()

	// 发送数据包
	var dataToSend []byte
	if payloadOffset == 0 {
		// 发送完整的IP包
		dataToSend = packet
	} else {
		// 发送协议载荷
		dataToSend = packet[payloadOffset:]
	}

	s.logger.Debug("Sending raw packet",
		zap.String("client_id", clientID),
		zap.String("protocol_name", protocolName),
		zap.Int("payload_size", len(dataToSend)))

	_, err = conn.Write(dataToSend)
	if err != nil {
		s.logger.Debug("Failed to send raw packet",
			zap.String("client_id", clientID),
			zap.Error(err))
		return nil, nil
	}

	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))

	// 读取响应
	responseBuffer := make([]byte, 1500)
	n, err := conn.Read(responseBuffer)
	if err != nil {
		s.logger.Debug("No raw response received",
			zap.String("client_id", clientID),
			zap.String("protocol", protocolStr),
			zap.Error(err))
		return nil, nil
	}

	if n > 0 {
		s.logger.Debug("Raw response received",
			zap.String("client_id", clientID),
			zap.String("protocol", protocolStr),
			zap.Int("response_size", n))

		// 根据协议类型构造响应包
		return s.buildUnifiedResponsePacket(srcIP, dstIP, protocol, responseBuffer[:n], clientID)
	}

	return nil, nil
}

// forwardRawv6PacketWithResponse 统一的IPv6原始套接字转发函数
func (s *Server) forwardRawv6PacketWithResponse(packet []byte, dstIP net.IP, clientID string) ([]byte, error) {
	if len(packet) < 40 {
		return nil, fmt.Errorf("IPv6 packet too short")
	}

	// 获取源IP和协议信息
	srcIP := net.IP(packet[8:24])
	nextHeader := packet[6]
	protocolStr := tun.ProtocolToString(nextHeader, true)

	s.logger.Debug("Forwarding IPv6 packet using unified raw socket",
		zap.String("client_id", clientID),
		zap.String("src_ip", srcIP.String()),
		zap.String("dst_ip", dstIP.String()),
		zap.Int("packet_size", len(packet)),
		zap.String("protocol", protocolStr))

	// 检查特殊地址
	if dstIP.IsMulticast() || dstIP.IsLinkLocalUnicast() {
		s.logger.Debug("Skipping IPv6 multicast/link-local address",
			zap.String("client_id", clientID),
			zap.String("dst_ip", dstIP.String()))
		return nil, nil
	}

	// 根据协议类型选择合适的原始套接字
	var protocolName string
	var payloadOffset int

	switch nextHeader {
	case 58: // ICMPv6
		protocolName = "ip6:ipv6-icmp"
		payloadOffset = 40 // 跳过IPv6头部
	case 6: // TCP
		protocolName = "ip6:tcp"
		payloadOffset = 40 // 跳过IPv6头部
	case 17: // UDP
		protocolName = "ip6:udp"
		payloadOffset = 40 // 跳过IPv6头部
	default:
		// 对于其他协议，尝试使用通用的IPv6原始套接字
		protocolName = fmt.Sprintf("ip6:%d", nextHeader)
		payloadOffset = 40
	}

	// 创建原始套接字，使用权限检测和降级策略
	conn, err := s.createRawSocketWithFallback(protocolName, dstIP.String(), nextHeader)
	if err != nil {
		s.logger.Debug("Failed to create IPv6 raw socket, trying alternative",
			zap.String("client_id", clientID),
			zap.String("protocol_name", protocolName),
			zap.Error(err))

		// 如果特定协议失败，尝试使用通用IPv6套接字
		if protocolName != "ip6:ipv6" {
			conn, err = s.createRawSocketWithFallback("ip6:ipv6", dstIP.String(), nextHeader)
			if err != nil {
				s.logger.Debug("Failed to create fallback IPv6 raw socket",
					zap.String("client_id", clientID),
					zap.Error(err))
				return nil, nil
			}
			payloadOffset = 0 // 发送完整的IPv6包
		} else {
			return nil, nil
		}
	}
	defer conn.Close()

	// 发送数据包
	var dataToSend []byte
	if payloadOffset == 0 {
		// 发送完整的IPv6包
		dataToSend = packet
	} else {
		// 发送协议载荷
		dataToSend = packet[payloadOffset:]
	}

	s.logger.Debug("Sending IPv6 raw packet",
		zap.String("client_id", clientID),
		zap.String("protocol_name", protocolName),
		zap.Int("payload_size", len(dataToSend)))

	_, err = conn.Write(dataToSend)
	if err != nil {
		s.logger.Debug("Failed to send IPv6 raw packet",
			zap.String("client_id", clientID),
			zap.Error(err))
		return nil, nil
	}

	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))

	// 读取响应
	responseBuffer := make([]byte, 1500)
	n, err := conn.Read(responseBuffer)
	if err != nil {
		s.logger.Debug("No IPv6 raw response received",
			zap.String("client_id", clientID),
			zap.String("protocol", protocolStr),
			zap.Error(err))
		return nil, nil
	}

	if n > 0 {
		s.logger.Debug("IPv6 raw response received",
			zap.String("client_id", clientID),
			zap.String("protocol", protocolStr),
			zap.Int("response_size", n))

		// 根据协议类型构造IPv6响应包
		return s.buildUnifiedIPv6ResponsePacket(srcIP, dstIP, nextHeader, responseBuffer[:n], clientID)
	}

	return nil, nil
}

// buildUnifiedResponsePacket 统一的IPv4响应包构造函数
func (s *Server) buildUnifiedResponsePacket(originalSrcIP, originalDstIP net.IP, protocol uint8, responseData []byte, clientID string) ([]byte, error) {
	// 智能解析响应数据
	var protocolData []byte

	// 检查响应数据是否包含IP头部
	if len(responseData) >= 20 && (responseData[0]>>4) == 4 {
		// 响应包含IP头部，提取协议数据部分
		ipHeaderLen := int(responseData[0]&0x0F) * 4
		if len(responseData) > ipHeaderLen {
			protocolData = responseData[ipHeaderLen:]
			s.logger.Debug("Extracted protocol data from IP packet",
				zap.String("client_id", clientID),
				zap.Int("ip_header_len", ipHeaderLen),
				zap.Int("protocol_data_len", len(protocolData)))
		} else {
			s.logger.Warn("Invalid IP packet in response",
				zap.String("client_id", clientID),
				zap.Int("response_size", len(responseData)),
				zap.Int("ip_header_len", ipHeaderLen))
			return nil, nil
		}
	} else {
		// 响应只包含协议数据
		protocolData = responseData
		s.logger.Debug("Using raw protocol data",
			zap.String("client_id", clientID),
			zap.Int("protocol_data_len", len(protocolData)))
	}

	// 使用地址映射缓存确保正确的IP地址映射
	var clientRealIP net.IP = originalSrcIP // 默认使用数据包中的源IP

	// 从地址映射缓存中获取客户端真实IP
	if mapping, exists := s.addressMappingCache.Get(clientID); exists {
		clientRealIP = mapping.ClientRealIP
		s.logger.Debug("Using cached address mapping for raw socket response",
			zap.String("client_id", clientID),
			zap.String("cached_client_real_ip", clientRealIP.String()),
			zap.String("packet_client_ip", originalSrcIP.String()),
			zap.String("target_ip", originalDstIP.String()),
			zap.Uint8("protocol", protocol))
	} else {
		s.logger.Debug("No cached address mapping found for raw socket, using packet source IP",
			zap.String("client_id", clientID),
			zap.String("packet_client_ip", originalSrcIP.String()),
			zap.String("target_ip", originalDstIP.String()),
			zap.Uint8("protocol", protocol))
	}

	// 根据协议类型构造完整的响应包，使用客户端真实IP
	switch protocol {
	case 1: // ICMP
		responsePacket, err := s.buildICMPResponsePacket(originalSrcIP, originalDstIP, protocolData)
		if err == nil {
			s.logger.Debug("ICMP raw response packet constructed with address mapping",
				zap.String("client_id", clientID),
				zap.String("response_src_ip", originalDstIP.String()),
				zap.String("response_dst_ip", originalSrcIP.String()))
		}
		return responsePacket, err
	case 6: // TCP
		// 对于TCP，需要解析端口信息
		if len(protocolData) >= 4 {
			srcPort := uint16(protocolData[0])<<8 | uint16(protocolData[1])
			dstPort := uint16(protocolData[2])<<8 | uint16(protocolData[3])
			responsePacket, err := s.buildTCPResponsePacket(originalSrcIP, srcPort, originalDstIP, dstPort, protocolData)
			if err == nil {
				s.logger.Debug("TCP raw response packet constructed with address mapping",
					zap.String("client_id", clientID),
					zap.String("response_src_ip", originalDstIP.String()),
					zap.String("response_dst_ip", originalSrcIP.String()))
			}
			return responsePacket, err
		}
		return nil, fmt.Errorf("TCP response data too short")
	case 17: // UDP
		// 对于UDP，需要解析端口信息
		if len(protocolData) >= 4 {
			srcPort := uint16(protocolData[0])<<8 | uint16(protocolData[1])
			dstPort := uint16(protocolData[2])<<8 | uint16(protocolData[3])
			responsePacket, err := s.buildUDPResponsePacket(originalSrcIP, srcPort, originalDstIP, dstPort, protocolData[8:])
			if err == nil {
				s.logger.Debug("UDP raw response packet constructed with address mapping",
					zap.String("client_id", clientID),
					zap.String("response_src_ip", originalDstIP.String()),
					zap.String("response_dst_ip", originalSrcIP.String()))
			}
			return responsePacket, err
		}
		return nil, fmt.Errorf("UDP response data too short")
	default:
		// 对于其他协议，构造基本的IP响应包
		return s.buildGenericIPv4ResponsePacket(originalSrcIP, originalDstIP, protocol, protocolData)
	}
}

// buildUnifiedIPv6ResponsePacket 统一的IPv6响应包构造函数
func (s *Server) buildUnifiedIPv6ResponsePacket(originalSrcIP, originalDstIP net.IP, nextHeader uint8, responseData []byte, clientID string) ([]byte, error) {
	// 智能解析响应数据
	var protocolData []byte

	// 检查响应数据是否包含IPv6头部
	if len(responseData) >= 40 && (responseData[0]>>4) == 6 {
		// 响应包含IPv6头部，提取协议数据部分
		protocolData = responseData[40:]
		s.logger.Debug("Extracted protocol data from IPv6 packet",
			zap.String("client_id", clientID),
			zap.Int("protocol_data_len", len(protocolData)))
	} else {
		// 响应只包含协议数据
		protocolData = responseData
		s.logger.Debug("Using raw IPv6 protocol data",
			zap.String("client_id", clientID),
			zap.Int("protocol_data_len", len(protocolData)))
	}

	// 根据协议类型构造完整的IPv6响应包
	switch nextHeader {
	case 58: // ICMPv6
		return s.buildICMPv6ResponsePacket(originalSrcIP, originalDstIP, protocolData)
	case 6: // TCP
		// 对于TCP over IPv6，需要解析端口信息
		if len(protocolData) >= 4 {
			srcPort := uint16(protocolData[0])<<8 | uint16(protocolData[1])
			dstPort := uint16(protocolData[2])<<8 | uint16(protocolData[3])
			return s.buildTCPv6ResponsePacket(originalSrcIP, originalDstIP, srcPort, dstPort, protocolData)
		}
		return nil, fmt.Errorf("TCP over IPv6 response data too short")
	case 17: // UDP
		// 对于UDP over IPv6，需要解析端口信息
		if len(protocolData) >= 4 {
			srcPort := uint16(protocolData[0])<<8 | uint16(protocolData[1])
			dstPort := uint16(protocolData[2])<<8 | uint16(protocolData[3])
			return s.buildUDPv6ResponsePacket(originalSrcIP, originalDstIP, srcPort, dstPort, protocolData[8:])
		}
		return nil, fmt.Errorf("UDP over IPv6 response data too short")
	default:
		// 对于其他IPv6协议，构造基本的IPv6响应包
		return s.buildGenericIPv6ResponsePacket(originalSrcIP, originalDstIP, nextHeader, protocolData)
	}
}

// buildGenericIPv4ResponsePacket 构造通用的IPv4响应包
func (s *Server) buildGenericIPv4ResponsePacket(originalSrcIP, originalDstIP net.IP, protocol uint8, protocolData []byte) ([]byte, error) {
	// 构造IPv4响应数据包
	ipHeaderLen := 20
	totalLen := ipHeaderLen + len(protocolData)

	packet := make([]byte, totalLen)

	// 构造IPv4头部
	packet[0] = 0x45                  // 版本(4) + 头部长度(5*4=20)
	packet[1] = 0x00                  // 服务类型
	packet[2] = byte(totalLen >> 8)   // 总长度高字节
	packet[3] = byte(totalLen & 0xFF) // 总长度低字节
	packet[4] = 0x00                  // 标识高字节
	packet[5] = 0x00                  // 标识低字节
	packet[6] = 0x40                  // 标志位(DF=1) + 片偏移高5位
	packet[7] = 0x00                  // 片偏移低8位
	packet[8] = 64                    // TTL
	packet[9] = protocol              // 协议
	packet[10] = 0x00                 // 头部校验和高字节(稍后计算)
	packet[11] = 0x00                 // 头部校验和低字节

	// 源IP(原始的目标IP)
	copy(packet[12:16], originalDstIP.To4())
	// 目标IP(原始的源IP)
	copy(packet[16:20], originalSrcIP.To4())

	// 计算IP头部校验和
	ipChecksum := checksum.CalculateIPv4Checksum(packet[:20])
	packet[10] = byte(ipChecksum >> 8)
	packet[11] = byte(ipChecksum & 0xFF)

	// 复制协议数据
	copy(packet[20:], protocolData)

	return packet, nil
}

// isTCPControlPacket 检查是否是TCP控制包
func (s *Server) isTCPControlPacket(packet []byte) bool {
	if len(packet) < 40 { // 最小TCP包长度（20字节IP头 + 20字节TCP头）
		return false
	}

	// 解析TCP头部获取标志位
	tcpHeader := packet[20:]
	if len(tcpHeader) < 14 {
		return false
	}

	tcpFlags := tcpHeader[13]
	isSyn := (tcpFlags & 0x02) != 0
	isAck := (tcpFlags & 0x10) != 0
	isFin := (tcpFlags & 0x01) != 0
	isRst := (tcpFlags & 0x04) != 0
	isPsh := (tcpFlags & 0x08) != 0

	// 控制包：SYN、FIN、RST或纯ACK包（没有PSH标志）
	return isSyn || isFin || isRst || (isAck && !isPsh)
}

// forwardTCPWithHybridStrategy TCP混合转发策略
func (s *Server) forwardTCPWithHybridStrategy(packet []byte, dstIP net.IP, clientID string) ([]byte, error) {
	if len(packet) < 40 { // 最小TCP包长度（20字节IP头 + 20字节TCP头）
		return nil, fmt.Errorf("TCP packet too short")
	}

	// 获取源IP地址和端口信息
	srcIP := net.IP(packet[12:16])
	tcpHeader := packet[20:]
	srcPort := uint16(tcpHeader[0])<<8 | uint16(tcpHeader[1])
	dstPort := uint16(tcpHeader[2])<<8 | uint16(tcpHeader[3])

	// 解析TCP标志位
	tcpFlags := tcpHeader[13]
	isSyn := (tcpFlags & 0x02) != 0
	isAck := (tcpFlags & 0x10) != 0
	isFin := (tcpFlags & 0x01) != 0
	isRst := (tcpFlags & 0x04) != 0
	isPsh := (tcpFlags & 0x08) != 0

	s.logger.Debug("TCP hybrid forwarding strategy",
		zap.String("client_id", clientID),
		zap.String("src_ip", srcIP.String()),
		zap.Uint16("src_port", srcPort),
		zap.String("dst_ip", dstIP.String()),
		zap.Uint16("dst_port", dstPort),
		zap.Int("packet_size", len(packet)),
		zap.Bool("syn", isSyn),
		zap.Bool("ack", isAck),
		zap.Bool("fin", isFin),
		zap.Bool("rst", isRst),
		zap.Bool("psh", isPsh))

	// 根据TCP包类型选择转发策略
	if s.isTCPControlPacket(packet) {
		s.logger.Debug("TCP control packet detected, using raw socket forwarding",
			zap.String("client_id", clientID),
			zap.Bool("syn", isSyn),
			zap.Bool("fin", isFin),
			zap.Bool("rst", isRst))

		// 对于控制包，尝试原始套接字转发
		return s.forwardTCPRawWithResponse(packet, dstIP, clientID)
	} else {
		s.logger.Debug("TCP data packet detected, using application layer forwarding",
			zap.String("client_id", clientID),
			zap.Bool("psh", isPsh))

		// 对于数据包，使用应用层代理
		return s.forwardTCPApplicationLayer(packet, dstIP, clientID)
	}
}

// forwardTCPApplicationLayer TCP应用层转发
func (s *Server) forwardTCPApplicationLayer(packet []byte, dstIP net.IP, clientID string) ([]byte, error) {
	if len(packet) < 40 { // 最小TCP包长度（20字节IP头 + 20字节TCP头）
		return nil, fmt.Errorf("TCP packet too short")
	}

	// 获取源IP地址和端口信息
	srcIP := net.IP(packet[12:16])
	tcpHeader := packet[20:]
	srcPort := uint16(tcpHeader[0])<<8 | uint16(tcpHeader[1])
	dstPort := uint16(tcpHeader[2])<<8 | uint16(tcpHeader[3])

	addr := net.JoinHostPort(dstIP.String(), fmt.Sprintf("%d", dstPort))

	s.logger.Debug("TCP application layer forwarding",
		zap.String("client_id", clientID),
		zap.String("src_ip", srcIP.String()),
		zap.Uint16("src_port", srcPort),
		zap.String("dst_addr", addr))

	// 建立TCP连接（针对不同TCP包类型优化超时时间）
	// 检查TCP标志位以确定包类型
	timeout := 3 * time.Second // 默认超时
	if len(tcpHeader) >= 14 {
		tcpFlags := tcpHeader[13]
		isSyn := (tcpFlags & 0x02) != 0
		if isSyn {
			timeout = 8 * time.Second // SYN包需要更长时间建立连接
			s.logger.Debug("TCP SYN packet detected, using extended timeout",
				zap.String("client_id", clientID),
				zap.Duration("timeout", timeout))
		}
	}

	conn, err := net.DialTimeout("tcp", addr, timeout)
	if err != nil {
		s.logger.Debug("Failed to establish TCP connection",
			zap.String("client_id", clientID),
			zap.String("dst_addr", addr),
			zap.Error(err))
		return nil, nil // 不返回错误，避免中断其他包的处理
	}
	defer conn.Close()

	// 提取TCP载荷数据
	ipHeaderLen := int(packet[0]&0x0F) * 4
	tcpHeaderLen := int(tcpHeader[12]>>4) * 4
	payloadStart := ipHeaderLen + tcpHeaderLen

	if payloadStart < len(packet) {
		payload := packet[payloadStart:]
		if len(payload) > 0 {
			s.logger.Debug("Sending TCP payload",
				zap.String("client_id", clientID),
				zap.String("dst_addr", addr),
				zap.Int("payload_size", len(payload)))

			_, err = conn.Write(payload)
			if err != nil {
				s.logger.Debug("Failed to send TCP payload",
					zap.String("client_id", clientID),
					zap.String("dst_addr", addr),
					zap.Error(err))
				return nil, nil
			}
		}
	}

	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))

	// 读取响应
	responseBuffer := make([]byte, 1500)
	n, err := conn.Read(responseBuffer)
	if err != nil {
		s.logger.Debug("No TCP application layer response received",
			zap.String("client_id", clientID),
			zap.String("dst_addr", addr),
			zap.Error(err))
		return nil, nil
	}

	if n > 0 {
		s.logger.Debug("TCP application layer response received",
			zap.String("client_id", clientID),
			zap.String("dst_addr", addr),
			zap.Int("response_size", n))

		// 使用地址映射缓存确保正确的IP地址映射
		var clientRealIP net.IP = srcIP // 默认使用数据包中的源IP

		// 从地址映射缓存中获取客户端真实IP
		if mapping, exists := s.addressMappingCache.Get(clientID); exists {
			clientRealIP = mapping.ClientRealIP
			s.logger.Debug("Using cached address mapping for TCP response",
				zap.String("client_id", clientID),
				zap.String("cached_client_real_ip", clientRealIP.String()),
				zap.String("packet_client_ip", srcIP.String()),
				zap.String("target_ip", dstIP.String()))
		} else {
			s.logger.Debug("No cached address mapping found for TCP, using packet source IP",
				zap.String("client_id", clientID),
				zap.String("packet_client_ip", srcIP.String()),
				zap.String("target_ip", dstIP.String()))
		}

		// 构造完整的TCP响应数据包，使用数据包原始源IP
		responsePacket, err := s.buildTCPResponsePacket(srcIP, srcPort, dstIP, dstPort, responseBuffer[:n])
		if err != nil {
			s.logger.Error("Failed to build TCP response packet",
				zap.String("client_id", clientID),
				zap.Error(err))
			return nil, nil
		}

		s.logger.Debug("TCP response packet constructed with address mapping",
			zap.String("client_id", clientID),
			zap.String("response_src_ip", dstIP.String()),
			zap.String("response_dst_ip", srcIP.String()),
			zap.String("original_packet_src", srcIP.String()),
			zap.Int("response_packet_len", len(responsePacket)))

		return responsePacket, nil
	}

	return nil, nil
}

// isTCPv6ControlPacket 检查是否是TCP over IPv6控制包
func (s *Server) isTCPv6ControlPacket(packet []byte) bool {
	if len(packet) < 60 { // 最小TCP over IPv6包长度（40字节IPv6头 + 20字节TCP头）
		return false
	}

	// 解析TCP头部获取标志位
	tcpHeader := packet[40:]
	if len(tcpHeader) < 14 {
		return false
	}

	tcpFlags := tcpHeader[13]
	isSyn := (tcpFlags & 0x02) != 0
	isAck := (tcpFlags & 0x10) != 0
	isFin := (tcpFlags & 0x01) != 0
	isRst := (tcpFlags & 0x04) != 0
	isPsh := (tcpFlags & 0x08) != 0

	// 控制包：SYN、FIN、RST或纯ACK包（没有PSH标志）
	return isSyn || isFin || isRst || (isAck && !isPsh)
}

// forwardTCPv6WithHybridStrategy TCP over IPv6混合转发策略
func (s *Server) forwardTCPv6WithHybridStrategy(packet []byte, dstIP net.IP, clientID string) ([]byte, error) {
	if len(packet) < 60 { // 最小TCP over IPv6包长度（40字节IPv6头 + 20字节TCP头）
		return nil, fmt.Errorf("TCP over IPv6 packet too short")
	}

	// 获取源IP地址和端口信息
	srcIP := net.IP(packet[8:24])
	tcpHeader := packet[40:]
	srcPort := uint16(tcpHeader[0])<<8 | uint16(tcpHeader[1])
	dstPort := uint16(tcpHeader[2])<<8 | uint16(tcpHeader[3])

	// 解析TCP标志位
	tcpFlags := tcpHeader[13]
	isSyn := (tcpFlags & 0x02) != 0
	isAck := (tcpFlags & 0x10) != 0
	isFin := (tcpFlags & 0x01) != 0
	isRst := (tcpFlags & 0x04) != 0
	isPsh := (tcpFlags & 0x08) != 0

	s.logger.Debug("TCP over IPv6 hybrid forwarding strategy",
		zap.String("client_id", clientID),
		zap.String("src_ip", srcIP.String()),
		zap.Uint16("src_port", srcPort),
		zap.String("dst_ip", dstIP.String()),
		zap.Uint16("dst_port", dstPort),
		zap.Int("packet_size", len(packet)),
		zap.Bool("syn", isSyn),
		zap.Bool("ack", isAck),
		zap.Bool("fin", isFin),
		zap.Bool("rst", isRst),
		zap.Bool("psh", isPsh))

	// 根据TCP包类型选择转发策略
	if s.isTCPv6ControlPacket(packet) {
		s.logger.Debug("TCP over IPv6 control packet detected, using raw socket forwarding",
			zap.String("client_id", clientID),
			zap.Bool("syn", isSyn),
			zap.Bool("fin", isFin),
			zap.Bool("rst", isRst))

		// 对于控制包，尝试原始套接字转发
		return s.forwardTCPv6RawWithResponse(packet, dstIP, clientID)
	} else {
		s.logger.Debug("TCP over IPv6 data packet detected, using application layer forwarding",
			zap.String("client_id", clientID),
			zap.Bool("psh", isPsh))

		// 对于数据包，使用应用层代理
		return s.forwardTCPv6ApplicationLayer(packet, dstIP, clientID)
	}
}

// forwardTCPv6ApplicationLayer TCP over IPv6应用层转发
func (s *Server) forwardTCPv6ApplicationLayer(packet []byte, dstIP net.IP, clientID string) ([]byte, error) {
	if len(packet) < 60 { // 最小TCP over IPv6包长度（40字节IPv6头 + 20字节TCP头）
		return nil, fmt.Errorf("TCP over IPv6 packet too short")
	}

	// 获取源IP地址和端口信息
	srcIP := net.IP(packet[8:24])
	tcpHeader := packet[40:]
	srcPort := uint16(tcpHeader[0])<<8 | uint16(tcpHeader[1])
	dstPort := uint16(tcpHeader[2])<<8 | uint16(tcpHeader[3])

	addr := net.JoinHostPort(dstIP.String(), fmt.Sprintf("%d", dstPort))

	s.logger.Debug("TCP over IPv6 application layer forwarding",
		zap.String("client_id", clientID),
		zap.String("src_ip", srcIP.String()),
		zap.Uint16("src_port", srcPort),
		zap.String("dst_addr", addr))

	// 建立TCP连接（减少超时时间以避免阻塞）
	conn, err := net.DialTimeout("tcp", addr, 2*time.Second)
	if err != nil {
		s.logger.Debug("Failed to establish TCP over IPv6 connection",
			zap.String("client_id", clientID),
			zap.String("dst_addr", addr),
			zap.Error(err))
		return nil, nil // 不返回错误，避免中断其他包的处理
	}
	defer conn.Close()

	// 提取TCP载荷数据
	tcpHeaderLen := int(tcpHeader[12]>>4) * 4
	payloadStart := 40 + tcpHeaderLen // IPv6头部40字节 + TCP头部

	if payloadStart < len(packet) {
		payload := packet[payloadStart:]
		if len(payload) > 0 {
			s.logger.Debug("Sending TCP over IPv6 payload",
				zap.String("client_id", clientID),
				zap.String("dst_addr", addr),
				zap.Int("payload_size", len(payload)))

			_, err = conn.Write(payload)
			if err != nil {
				s.logger.Debug("Failed to send TCP over IPv6 payload",
					zap.String("client_id", clientID),
					zap.String("dst_addr", addr),
					zap.Error(err))
				return nil, nil
			}
		}
	}

	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))

	// 读取响应
	responseBuffer := make([]byte, 1500)
	n, err := conn.Read(responseBuffer)
	if err != nil {
		s.logger.Debug("No TCP over IPv6 application layer response received",
			zap.String("client_id", clientID),
			zap.String("dst_addr", addr),
			zap.Error(err))
		return nil, nil
	}

	if n > 0 {
		s.logger.Debug("TCP over IPv6 application layer response received",
			zap.String("client_id", clientID),
			zap.String("dst_addr", addr),
			zap.Int("response_size", n))

		// 构造完整的TCP over IPv6响应数据包
		responsePacket, err := s.buildTCPv6ResponsePacket(srcIP, dstIP, srcPort, dstPort, responseBuffer[:n])
		if err != nil {
			s.logger.Error("Failed to build TCP over IPv6 response packet",
				zap.String("client_id", clientID),
				zap.Error(err))
			return nil, nil
		}

		return responsePacket, nil
	}

	return nil, nil
}

// forwardTCPv6RawWithResponse TCP over IPv6原始套接字转发（用于控制包）
func (s *Server) forwardTCPv6RawWithResponse(packet []byte, dstIP net.IP, clientID string) ([]byte, error) {
	// 获取源IP地址
	srcIP := net.IP(packet[8:24])

	// 解析原始包的TCP头部获取端口信息
	tcpHeader := packet[40:]
	originalSrcPort := uint16(tcpHeader[0])<<8 | uint16(tcpHeader[1])
	originalDstPort := uint16(tcpHeader[2])<<8 | uint16(tcpHeader[3])

	s.logger.Debug("Forwarding TCP over IPv6 packet using raw socket",
		zap.String("client_id", clientID),
		zap.String("src_ip", srcIP.String()),
		zap.String("dst_ip", dstIP.String()),
		zap.Uint16("src_port", originalSrcPort),
		zap.Uint16("dst_port", originalDstPort),
		zap.Int("packet_size", len(packet)))

	// 创建原始套接字
	conn, err := net.Dial("ip6:tcp", dstIP.String())
	if err != nil {
		s.logger.Debug("Failed to create raw TCP over IPv6 socket",
			zap.String("client_id", clientID),
			zap.String("dst_ip", dstIP.String()),
			zap.Error(err))
		return nil, nil // 不返回错误，只是跳过响应
	}
	defer conn.Close()

	// 发送TCP数据（跳过IPv6头部）
	tcpData := packet[40:] // 跳过40字节的IPv6头部

	// 记录发送的TCP数据详情
	if len(tcpData) >= 20 {
		tcpFlags := tcpData[13]
		s.logger.Debug("Sending TCP over IPv6 packet details",
			zap.String("client_id", clientID),
			zap.String("dst_ip", dstIP.String()),
			zap.Uint8("tcp_flags", tcpFlags),
			zap.Int("tcp_data_len", len(tcpData)))
	}

	_, err = conn.Write(tcpData)
	if err != nil {
		s.logger.Debug("Failed to send TCP over IPv6 packet",
			zap.String("client_id", clientID),
			zap.String("dst_ip", dstIP.String()),
			zap.Error(err))
		return nil, nil
	}

	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))

	// 读取响应
	responseBuffer := make([]byte, 1500)
	n, err := conn.Read(responseBuffer)
	if err != nil {
		s.logger.Debug("No TCP over IPv6 raw response received",
			zap.String("client_id", clientID),
			zap.String("dst_ip", dstIP.String()),
			zap.Error(err))
		return nil, nil
	}

	if n > 0 {
		s.logger.Debug("TCP over IPv6 raw response received",
			zap.String("client_id", clientID),
			zap.String("dst_ip", dstIP.String()),
			zap.Int("response_size", n))

		// 检查响应数据是否包含IPv6头部
		var tcpData []byte
		if n >= 40 && (responseBuffer[0]>>4) == 6 {
			// 响应包含IPv6头部，提取TCP部分
			tcpData = responseBuffer[40:n]
			s.logger.Debug("Extracted TCP data from IPv6 packet",
				zap.String("client_id", clientID),
				zap.Int("tcp_data_len", len(tcpData)))
		} else {
			// 响应只包含TCP数据
			tcpData = responseBuffer[:n]
			s.logger.Debug("Using raw TCP over IPv6 data",
				zap.String("client_id", clientID),
				zap.Int("tcp_data_len", len(tcpData)))
		}

		// 检查TCP数据的有效性
		if len(tcpData) >= 20 {
			tcpFlags := tcpData[13]
			responseSrcPort := uint16(tcpData[0])<<8 | uint16(tcpData[1])
			responseDstPort := uint16(tcpData[2])<<8 | uint16(tcpData[3])

			s.logger.Debug("TCP over IPv6 raw response details",
				zap.String("client_id", clientID),
				zap.Uint16("response_src_port", responseSrcPort),
				zap.Uint16("response_dst_port", responseDstPort),
				zap.Uint8("tcp_flags", tcpFlags),
				zap.Int("tcp_data_len", len(tcpData)))

			// 构造完整的TCP over IPv6响应数据包
			responsePacket, err := s.buildTCPv6ResponsePacket(srcIP, dstIP, originalSrcPort, originalDstPort, tcpData)
			if err != nil {
				return nil, fmt.Errorf("failed to build TCP over IPv6 response packet: %w", err)
			}

			return responsePacket, nil
		}
	}

	return nil, nil
}

// buildGenericIPv6ResponsePacket 构造通用的IPv6响应包
func (s *Server) buildGenericIPv6ResponsePacket(originalSrcIP, originalDstIP net.IP, nextHeader uint8, protocolData []byte) ([]byte, error) {
	// 构造IPv6响应数据包
	ipv6HeaderLen := 40
	totalLen := ipv6HeaderLen + len(protocolData)

	packet := make([]byte, totalLen)

	// 构造IPv6头部
	packet[0] = 0x60 // 版本(6) + 流量类别高4位
	packet[1] = 0x00 // 流量类别低4位 + 流标签高4位
	packet[2] = 0x00 // 流标签中8位
	packet[3] = 0x00 // 流标签低8位
	payloadLen := len(protocolData)
	packet[4] = byte(payloadLen >> 8)   // 载荷长度高字节
	packet[5] = byte(payloadLen & 0xFF) // 载荷长度低字节
	packet[6] = nextHeader              // 下一个头部
	packet[7] = 64                      // 跳数限制

	// 源IP(原始的目标IP)
	copy(packet[8:24], originalDstIP.To16())
	// 目标IP(原始的源IP)
	copy(packet[24:40], originalSrcIP.To16())

	// 复制协议数据
	copy(packet[40:], protocolData)

	return packet, nil
}

// createRawSocketWithFallback 创建原始套接字，支持权限检测和降级策略
func (s *Server) createRawSocketWithFallback(protocol, dest string, protocolNum uint8) (net.Conn, error) {
	// 首先尝试创建原始套接字
	conn, err := net.Dial(protocol, dest)
	if err != nil {
		// 检查是否是权限问题
		if s.isPermissionError(err) {
			s.logger.Warn("Raw socket permission denied, falling back to user-space implementation",
				zap.String("protocol", protocol),
				zap.String("dest", dest),
				zap.Error(err))

			// 根据协议类型选择降级策略
			return s.createUserSpaceSocket(protocolNum, dest)
		}

		// 其他错误直接返回
		return nil, err
	}

	return conn, nil
}

// isPermissionError 检查是否是权限相关错误
func (s *Server) isPermissionError(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()
	return strings.Contains(errStr, "operation not permitted") ||
		strings.Contains(errStr, "permission denied") ||
		strings.Contains(errStr, "access denied") ||
		strings.Contains(errStr, "socket: operation not permitted")
}

// createUserSpaceSocket 创建用户空间套接字作为降级方案
func (s *Server) createUserSpaceSocket(protocolNum uint8, dest string) (net.Conn, error) {
	switch protocolNum {
	case 1: // ICMP - 使用UDP套接字模拟
		return s.createICMPFallbackSocket(dest)
	case 6: // TCP - 使用标准TCP连接
		return s.createTCPFallbackSocket(dest)
	case 17: // UDP - 使用标准UDP连接
		return s.createUDPFallbackSocket(dest)
	default:
		// 对于其他协议，尝试使用TCP连接
		s.logger.Warn("Unknown protocol, falling back to TCP",
			zap.Uint8("protocol", protocolNum),
			zap.String("dest", dest))
		return s.createTCPFallbackSocket(dest)
	}
}

// createICMPFallbackSocket 创建ICMP降级套接字（使用UDP模拟）
func (s *Server) createICMPFallbackSocket(dest string) (net.Conn, error) {
	// 对于ICMP，我们可以尝试使用UDP连接到目标主机的一个不太可能开放的端口
	// 这样可以触发ICMP响应
	fallbackPort := "33434" // traceroute常用端口
	udpAddr := net.JoinHostPort(dest, fallbackPort)

	conn, err := net.Dial("udp", udpAddr)
	if err != nil {
		return nil, fmt.Errorf("failed to create ICMP fallback socket: %w", err)
	}

	s.logger.Debug("Created ICMP fallback socket using UDP",
		zap.String("dest", dest),
		zap.String("fallback_addr", udpAddr))

	return conn, nil
}

// createTCPFallbackSocket 创建TCP降级套接字
func (s *Server) createTCPFallbackSocket(dest string) (net.Conn, error) {
	// 尝试连接到常见的TCP端口
	commonPorts := []string{"80", "443", "22", "21", "25", "53"}

	for _, port := range commonPorts {
		addr := net.JoinHostPort(dest, port)
		conn, err := net.DialTimeout("tcp", addr, time.Second*2)
		if err == nil {
			s.logger.Debug("Created TCP fallback socket",
				zap.String("dest", dest),
				zap.String("port", port))
			return conn, nil
		}
	}

	// 如果所有常见端口都失败，返回错误
	return nil, fmt.Errorf("failed to create TCP fallback socket to %s: no accessible ports", dest)
}

// createUDPFallbackSocket 创建UDP降级套接字
func (s *Server) createUDPFallbackSocket(dest string) (net.Conn, error) {
	// 尝试连接到常见的UDP端口
	commonPorts := []string{"53", "123", "161", "514"}

	for _, port := range commonPorts {
		addr := net.JoinHostPort(dest, port)
		conn, err := net.Dial("udp", addr)
		if err == nil {
			s.logger.Debug("Created UDP fallback socket",
				zap.String("dest", dest),
				zap.String("port", port))
			return conn, nil
		}
	}

	// 如果失败，使用默认端口53（DNS）
	addr := net.JoinHostPort(dest, "53")
	conn, err := net.Dial("udp", addr)
	if err != nil {
		return nil, fmt.Errorf("failed to create UDP fallback socket: %w", err)
	}

	return conn, nil
}

// PacketResult 数据包处理结果
type PacketResult struct {
	Packet []byte
	Error  error
}

// forwardPacketWithResponseAsync 异步转发数据包以避免阻塞
func (s *Server) forwardPacketWithResponseAsync(packet []byte, clientID string) ([]byte, error) {
	// 创建结果通道
	resultChan := make(chan *PacketResult, 1)

	// 启动异步处理
	go func() {
		defer func() {
			if r := recover(); r != nil {
				s.logger.Error("Panic in async packet forwarding",
					zap.String("client_id", clientID),
					zap.Any("panic", r))
				resultChan <- &PacketResult{
					Packet: nil,
					Error:  fmt.Errorf("panic in packet forwarding: %v", r),
				}
			}
		}()

		// 调用原始的同步转发函数
		responsePacket, err := s.forwardPacketWithResponse(packet, clientID)
		resultChan <- &PacketResult{
			Packet: responsePacket,
			Error:  err,
		}
	}()

	// 等待结果，设置合理的超时时间
	select {
	case result := <-resultChan:
		return result.Packet, result.Error
	case <-time.After(3 * time.Second): // 3秒超时，更快响应
		s.logger.Warn("Async packet forwarding timeout",
			zap.String("client_id", clientID),
			zap.Int("packet_size", len(packet)))
		return nil, fmt.Errorf("packet forwarding timeout after 3 seconds")
	}
}
