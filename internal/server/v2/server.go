package v2

import (
	"context"
	"fmt"
	"net"
	"sync"
	"time"

	"cyber-bastion/internal/forwarding"
	"cyber-bastion/internal/server/listener"
	"cyber-bastion/internal/server/nat"
	"cyber-bastion/pkg/config"

	"go.uber.org/zap"
)

// Server 新架构服务器
type Server struct {
	config *config.ServerConfig
	logger *zap.Logger
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
	mu     sync.RWMutex

	// 核心组件
	listenerManager   listener.ListenerManager
	forwardingHandler *forwarding.OptimizedForwardingHandler
	natManager        nat.NATManager

	// 地址映射缓存
	addressMappingCache        *AddressMappingCache
	addressMappingCacheAdapter *AddressMappingCacheAdapter

	// 连接管理
	clients map[string]*ClientSession

	// 状态
	state     string
	startTime time.Time
	metrics   *ServerMetrics
}

// ClientSession 客户端会话
type ClientSession struct {
	ID           string
	Connection   listener.Connection
	RemoteAddr   string
	ConnectTime  time.Time
	LastActivity time.Time
	BytesSent    int64
	BytesRecv    int64
	PacketsSent  int64
	PacketsRecv  int64
}

// ServerMetrics 服务器指标
type ServerMetrics struct {
	StartTime         time.Time     `json:"start_time"`
	Uptime            time.Duration `json:"uptime"`
	TotalConnections  int64         `json:"total_connections"`
	ActiveConnections int           `json:"active_connections"`
	BytesForwarded    int64         `json:"bytes_forwarded"`
	PacketsForwarded  int64         `json:"packets_forwarded"`
	ForwardingErrors  int64         `json:"forwarding_errors"`
}

// NewServer 创建新架构服务器
func NewServer(cfg *config.ServerConfig, logger *zap.Logger) (*Server, error) {
	ctx, cancel := context.WithCancel(context.Background())

	server := &Server{
		config:  cfg,
		logger:  logger,
		ctx:     ctx,
		cancel:  cancel,
		clients: make(map[string]*ClientSession),
		state:   "stopped",
		metrics: &ServerMetrics{},
	}

	// 创建监听器管理器
	server.listenerManager = listener.NewListenerManager(logger)

	// 创建地址映射缓存
	server.addressMappingCache = NewAddressMappingCache(time.Minute * 10)
	server.addressMappingCacheAdapter = NewAddressMappingCacheAdapter(server.addressMappingCache)

	// 创建优化转发处理器
	if err := server.createForwardingHandler(); err != nil {
		cancel()
		return nil, fmt.Errorf("failed to create forwarding handler: %w", err)
	}

	// 创建NAT管理器
	if err := server.createNATManager(); err != nil {
		cancel()
		return nil, fmt.Errorf("failed to create NAT manager: %w", err)
	}

	return server, nil
}

// Start 启动服务器
func (s *Server) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.state == "running" {
		return fmt.Errorf("server is already running")
	}

	s.logger.Info("Starting server v2")
	s.state = "starting"
	s.startTime = time.Now()
	s.metrics.StartTime = s.startTime

	// 配置监听器
	if err := s.configureListeners(); err != nil {
		s.state = "error"
		return fmt.Errorf("failed to configure listeners: %w", err)
	}

	// 启动监听器管理器
	if err := s.listenerManager.Start(s.ctx); err != nil {
		s.state = "error"
		return fmt.Errorf("failed to start listener manager: %w", err)
	}

	// 启动优化转发处理器
	if err := s.forwardingHandler.Start(); err != nil {
		s.state = "error"
		return fmt.Errorf("failed to start forwarding handler: %w", err)
	}

	// 启动连接处理循环
	s.wg.Add(1)
	go s.connectionLoop()

	// 启动清理循环
	s.wg.Add(1)
	go s.cleanupLoop()

	s.state = "running"
	s.logger.Info("Server v2 started successfully")

	return nil
}

// Stop 停止服务器
func (s *Server) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.state == "stopped" {
		return nil
	}

	s.logger.Info("Stopping server v2")
	s.state = "stopping"

	// 取消上下文
	s.cancel()

	// 停止优化转发处理器
	if s.forwardingHandler != nil {
		if err := s.forwardingHandler.Stop(); err != nil {
			s.logger.Error("Failed to stop forwarding handler", zap.Error(err))
		}
	}

	// 停止监听器管理器
	if s.listenerManager != nil {
		if err := s.listenerManager.Stop(); err != nil {
			s.logger.Error("Failed to stop listener manager", zap.Error(err))
		}
	}

	// 关闭所有客户端连接
	for _, client := range s.clients {
		client.Connection.Close()
	}
	s.clients = make(map[string]*ClientSession)

	// 等待所有协程完成
	s.wg.Wait()

	s.state = "stopped"
	s.logger.Info("Server v2 stopped")

	return nil
}

// GetMetrics 获取服务器指标
func (s *Server) GetMetrics() *ServerMetrics {
	s.mu.RLock()
	defer s.mu.RUnlock()

	metrics := *s.metrics
	metrics.Uptime = time.Since(s.startTime)
	metrics.ActiveConnections = len(s.clients)

	return &metrics
}

// createForwardingHandler 创建优化转发处理器
func (s *Server) createForwardingHandler() error {
	// 创建优化转发处理器配置
	handlerConfig := &forwarding.HandlerConfig{
		WorkerCount:     4,
		QueueSize:       1000,
		MaxIdleConns:    10,
		MaxActiveConns:  100,
		IdleTimeout:     time.Minute * 5,
		DialTimeout:     time.Second * 10,
		EnableMetrics:   true,
		MetricsInterval: time.Second * 30,
		EnableCache:     true,
		CacheSize:       1000,
		CacheTTL:        time.Minute * 5,
	}

	// 创建优化转发处理器
	handler, err := forwarding.NewOptimizedForwardingHandler(handlerConfig, s.logger)
	if err != nil {
		return fmt.Errorf("failed to create optimized forwarding handler: %w", err)
	}

	// 设置地址映射缓存
	handler.SetAddressMappingCache(s.addressMappingCacheAdapter)

	s.forwardingHandler = handler
	s.logger.Info("Optimized forwarding handler created and configured")

	return nil
}

// createNATManager 创建NAT管理器
func (s *Server) createNATManager() error {
	// TODO: 实现NAT管理器创建
	s.logger.Info("NAT manager creation placeholder")
	return nil
}

// configureListeners 配置监听器
func (s *Server) configureListeners() error {
	// 配置主TCP监听器
	tcpConfig := &listener.ListenerConfig{
		Protocol:       "tcp",
		Address:        s.config.Host,
		Port:           s.config.Port,
		EnableTLS:      s.config.Security != nil && s.config.Security.EnableTLS,
		TLSCertFile:    "",
		TLSKeyFile:     "",
		MaxConnections: 1000,
		ReadTimeout:    time.Second * 30,
		WriteTimeout:   time.Second * 30,
		IdleTimeout:    time.Minute * 5,
	}

	// 设置TLS配置
	if s.config.Security != nil {
		tcpConfig.TLSCertFile = s.config.Security.TLSCertFile
		tcpConfig.TLSKeyFile = s.config.Security.TLSKeyFile
	}

	if err := s.listenerManager.AddListener(tcpConfig); err != nil {
		return fmt.Errorf("failed to add TCP listener: %w", err)
	}

	s.logger.Info("TCP listener configured",
		zap.String("address", s.config.Host),
		zap.Int("port", s.config.Port))

	// TODO: 根据配置添加其他协议监听器（WebSocket、QUIC等）

	return nil
}

// connectionLoop 连接处理循环
func (s *Server) connectionLoop() {
	defer s.wg.Done()

	s.logger.Info("Starting connection processing loop")

	for {
		select {
		case <-s.ctx.Done():
			return
		default:
			// 从所有监听器接受连接
			s.acceptConnections()
		}
	}
}

// acceptConnections 接受连接
func (s *Server) acceptConnections() {
	listeners := s.listenerManager.GetListeners()

	for _, listener := range listeners {
		// 非阻塞接受连接
		conn, err := listener.Accept()
		if err != nil {
			// 这里可能是超时或其他非致命错误
			continue
		}

		// 处理新连接
		go s.handleConnection(conn)
	}
}

// handleConnection 处理连接
func (s *Server) handleConnection(conn listener.Connection) {
	clientID := conn.GetID()
	s.logger.Info("New client connection",
		zap.String("client_id", clientID),
		zap.String("remote_addr", conn.GetRemoteAddr().String()))

	// 创建客户端会话
	session := &ClientSession{
		ID:           clientID,
		Connection:   conn,
		RemoteAddr:   conn.GetRemoteAddr().String(),
		ConnectTime:  time.Now(),
		LastActivity: time.Now(),
	}

	// 添加到客户端列表
	s.mu.Lock()
	s.clients[clientID] = session
	s.metrics.TotalConnections++
	s.mu.Unlock()

	// 处理客户端数据
	s.processClientData(session)

	// 清理连接
	s.mu.Lock()
	delete(s.clients, clientID)
	s.mu.Unlock()

	conn.Close()
	s.logger.Info("Client connection closed", zap.String("client_id", clientID))
}

// processClientData 处理客户端数据
func (s *Server) processClientData(session *ClientSession) {
	buffer := make([]byte, 4096)

	for {
		select {
		case <-s.ctx.Done():
			return
		default:
			// 读取数据
			n, err := session.Connection.Read(buffer)
			if err != nil {
				s.logger.Debug("Failed to read from client",
					zap.String("client_id", session.ID),
					zap.Error(err))
				return
			}

			if n == 0 {
				continue
			}

			// 更新会话统计
			session.LastActivity = time.Now()
			session.BytesRecv += int64(n)
			session.PacketsRecv++

			// 转发数据包
			packet := buffer[:n]
			s.forwardingHandler.HandlePacket(packet, session.ID, func(response []byte, err error) {
				if err != nil {
					s.logger.Error("Packet forwarding failed",
						zap.String("client_id", session.ID),
						zap.Error(err))
					s.metrics.ForwardingErrors++
					return
				}

				if response != nil {
					// 发送响应
					if _, writeErr := session.Connection.Write(response); writeErr != nil {
						s.logger.Error("Failed to write response",
							zap.String("client_id", session.ID),
							zap.Error(writeErr))
					} else {
						session.BytesSent += int64(len(response))
						session.PacketsSent++
						s.metrics.BytesForwarded += int64(len(response))
						s.metrics.PacketsForwarded++
					}
				}
			})
		}
	}
}

// cleanupLoop 清理循环
func (s *Server) cleanupLoop() {
	defer s.wg.Done()

	ticker := time.NewTicker(time.Minute * 5)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			s.performCleanup()
		}
	}
}

// performCleanup 执行清理
func (s *Server) performCleanup() {
	// 清理地址映射缓存
	if s.addressMappingCache != nil {
		s.addressMappingCache.Cleanup()
	}

	// 清理空闲连接
	s.cleanupIdleConnections()

	s.logger.Debug("Cleanup completed")
}

// cleanupIdleConnections 清理空闲连接
func (s *Server) cleanupIdleConnections() {
	s.mu.Lock()
	defer s.mu.Unlock()

	idleTimeout := time.Minute * 10
	now := time.Now()

	for clientID, session := range s.clients {
		if now.Sub(session.LastActivity) > idleTimeout {
			s.logger.Info("Closing idle connection",
				zap.String("client_id", clientID),
				zap.Duration("idle_time", now.Sub(session.LastActivity)))

			session.Connection.Close()
			delete(s.clients, clientID)
		}
	}
}

// AddressMappingCache 地址映射缓存
type AddressMappingCache struct {
	mappings map[string]*AddressMapping
	mu       sync.RWMutex
	ttl      time.Duration
}

// AddressMapping 地址映射
type AddressMapping struct {
	ClientRealIP  net.IP
	ServerLocalIP net.IP
	TargetIP      net.IP
	LastUsed      time.Time
	PacketCount   int64
}

// NewAddressMappingCache 创建地址映射缓存
func NewAddressMappingCache(ttl time.Duration) *AddressMappingCache {
	return &AddressMappingCache{
		mappings: make(map[string]*AddressMapping),
		ttl:      ttl,
	}
}

// Set 设置地址映射
func (amc *AddressMappingCache) Set(clientID string, clientRealIP, serverLocalIP, targetIP net.IP) {
	amc.mu.Lock()
	defer amc.mu.Unlock()

	amc.mappings[clientID] = &AddressMapping{
		ClientRealIP:  clientRealIP,
		ServerLocalIP: serverLocalIP,
		TargetIP:      targetIP,
		LastUsed:      time.Now(),
		PacketCount:   1,
	}
}

// Get 获取地址映射
func (amc *AddressMappingCache) Get(clientID string) (*AddressMapping, bool) {
	amc.mu.RLock()
	defer amc.mu.RUnlock()

	mapping, exists := amc.mappings[clientID]
	if !exists {
		return nil, false
	}

	if time.Since(mapping.LastUsed) > amc.ttl {
		return nil, false
	}

	return mapping, true
}

// GetForwarding 获取地址映射（forwarding包兼容）
func (amc *AddressMappingCache) GetForwarding(clientID string) (*forwarding.AddressMapping, bool) {
	amc.mu.RLock()
	defer amc.mu.RUnlock()

	mapping, exists := amc.mappings[clientID]
	if !exists {
		return nil, false
	}

	if time.Since(mapping.LastUsed) > amc.ttl {
		return nil, false
	}

	forwardingMapping := &forwarding.AddressMapping{
		ClientRealIP:  mapping.ClientRealIP,
		ServerLocalIP: mapping.ServerLocalIP,
		TargetIP:      mapping.TargetIP,
		LastUsed:      mapping.LastUsed,
		PacketCount:   mapping.PacketCount,
	}

	return forwardingMapping, true
}

// Update 更新地址映射
func (amc *AddressMappingCache) Update(clientID string) {
	amc.mu.Lock()
	defer amc.mu.Unlock()

	if mapping, exists := amc.mappings[clientID]; exists {
		mapping.LastUsed = time.Now()
		mapping.PacketCount++
	}
}

// Cleanup 清理过期映射
func (amc *AddressMappingCache) Cleanup() {
	amc.mu.Lock()
	defer amc.mu.Unlock()

	now := time.Now()
	for clientID, mapping := range amc.mappings {
		if now.Sub(mapping.LastUsed) > amc.ttl {
			delete(amc.mappings, clientID)
		}
	}
}

// AddressMappingCacheAdapter 地址映射缓存适配器
type AddressMappingCacheAdapter struct {
	cache *AddressMappingCache
}

// NewAddressMappingCacheAdapter 创建地址映射缓存适配器
func NewAddressMappingCacheAdapter(cache *AddressMappingCache) *AddressMappingCacheAdapter {
	return &AddressMappingCacheAdapter{cache: cache}
}

// Set 设置地址映射
func (a *AddressMappingCacheAdapter) Set(clientID string, clientRealIP, serverLocalIP, targetIP net.IP) {
	a.cache.Set(clientID, clientRealIP, serverLocalIP, targetIP)
}

// Get 获取地址映射
func (a *AddressMappingCacheAdapter) Get(clientID string) (*forwarding.AddressMapping, bool) {
	return a.cache.GetForwarding(clientID)
}

// Update 更新地址映射的使用时间和计数
func (a *AddressMappingCacheAdapter) Update(clientID string) {
	a.cache.Update(clientID)
}

// Cleanup 清理过期的映射
func (a *AddressMappingCacheAdapter) Cleanup() {
	a.cache.Cleanup()
}
