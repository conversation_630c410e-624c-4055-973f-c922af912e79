package nat

import (
	"net"
	"time"
)

// NATManager NAT管理器接口
type NATManager interface {
	// CreateMapping 创建NAT映射
	CreateMapping(clientID string, clientIP, serverIP, targetIP net.IP, protocol string) (*NATMapping, error)
	
	// GetMapping 获取NAT映射
	GetMapping(clientID string) (*NATMapping, bool)
	
	// UpdateMapping 更新NAT映射
	UpdateMapping(clientID string) error
	
	// DeleteMapping 删除NAT映射
	DeleteMapping(clientID string) error
	
	// TranslateOutbound 出站地址转换
	TranslateOutbound(packet []byte, clientID string) ([]byte, error)
	
	// TranslateInbound 入站地址转换
	TranslateInbound(packet []byte, targetIP net.IP) ([]byte, string, error)
	
	// GetMappings 获取所有映射
	GetMappings() map[string]*NATMapping
	
	// Cleanup 清理过期映射
	Cleanup() int
	
	// GetMetrics 获取NAT指标
	GetMetrics() *NATMetrics
}

// NATMapping NAT映射
type NATMapping struct {
	ClientID     string    `json:"client_id"`
	ClientIP     net.IP    `json:"client_ip"`
	ServerIP     net.IP    `json:"server_ip"`
	TargetIP     net.IP    `json:"target_ip"`
	Protocol     string    `json:"protocol"`
	CreatedAt    time.Time `json:"created_at"`
	LastUsed     time.Time `json:"last_used"`
	PacketCount  int64     `json:"packet_count"`
	BytesCount   int64     `json:"bytes_count"`
	
	// 端口映射（TCP/UDP）
	ClientPort   uint16 `json:"client_port,omitempty"`
	ServerPort   uint16 `json:"server_port,omitempty"`
	TargetPort   uint16 `json:"target_port,omitempty"`
	
	// ICMP映射
	ICMPId       uint16 `json:"icmp_id,omitempty"`
	ICMPSequence uint16 `json:"icmp_sequence,omitempty"`
}

// NATConfig NAT配置
type NATConfig struct {
	// 映射超时配置
	MappingTimeout    time.Duration `json:"mapping_timeout"`
	CleanupInterval   time.Duration `json:"cleanup_interval"`
	
	// 端口范围配置
	PortRangeStart    uint16 `json:"port_range_start"`
	PortRangeEnd      uint16 `json:"port_range_end"`
	
	// 服务器IP配置
	ServerIP          string `json:"server_ip"`
	
	// 性能配置
	MaxMappings       int    `json:"max_mappings"`
	EnableMetrics     bool   `json:"enable_metrics"`
}

// NATMetrics NAT指标
type NATMetrics struct {
	// 映射指标
	TotalMappings     int64     `json:"total_mappings"`
	ActiveMappings    int       `json:"active_mappings"`
	ExpiredMappings   int64     `json:"expired_mappings"`
	FailedMappings    int64     `json:"failed_mappings"`
	
	// 数据包指标
	OutboundPackets   int64     `json:"outbound_packets"`
	InboundPackets    int64     `json:"inbound_packets"`
	DroppedPackets    int64     `json:"dropped_packets"`
	
	// 字节指标
	OutboundBytes     int64     `json:"outbound_bytes"`
	InboundBytes      int64     `json:"inbound_bytes"`
	
	// 协议分布
	TCPMappings       int64     `json:"tcp_mappings"`
	UDPMappings       int64     `json:"udp_mappings"`
	ICMPMappings      int64     `json:"icmp_mappings"`
	
	// 时间指标
	LastCleanup       time.Time `json:"last_cleanup"`
	LastMapping       time.Time `json:"last_mapping"`
	
	// 错误指标
	TranslationErrors int64     `json:"translation_errors"`
	MappingErrors     int64     `json:"mapping_errors"`
}

// PacketTranslator 数据包转换器接口
type PacketTranslator interface {
	// TranslatePacket 转换数据包
	TranslatePacket(packet []byte, mapping *NATMapping, direction TranslationDirection) ([]byte, error)
	
	// GetProtocol 获取数据包协议
	GetProtocol(packet []byte) (string, error)
	
	// ExtractAddresses 提取地址信息
	ExtractAddresses(packet []byte) (*AddressInfo, error)
}

// TranslationDirection 转换方向
type TranslationDirection int

const (
	DirectionOutbound TranslationDirection = iota // 出站：客户端 -> 目标
	DirectionInbound                              // 入站：目标 -> 客户端
)

// AddressInfo 地址信息
type AddressInfo struct {
	SrcIP    net.IP `json:"src_ip"`
	DstIP    net.IP `json:"dst_ip"`
	SrcPort  uint16 `json:"src_port,omitempty"`
	DstPort  uint16 `json:"dst_port,omitempty"`
	Protocol string `json:"protocol"`
	
	// ICMP特定信息
	ICMPType     uint8  `json:"icmp_type,omitempty"`
	ICMPCode     uint8  `json:"icmp_code,omitempty"`
	ICMPId       uint16 `json:"icmp_id,omitempty"`
	ICMPSequence uint16 `json:"icmp_sequence,omitempty"`
}

// PortAllocator 端口分配器接口
type PortAllocator interface {
	// AllocatePort 分配端口
	AllocatePort(protocol string) (uint16, error)
	
	// ReleasePort 释放端口
	ReleasePort(port uint16, protocol string) error
	
	// IsPortAvailable 检查端口是否可用
	IsPortAvailable(port uint16, protocol string) bool
	
	// GetAllocatedPorts 获取已分配端口
	GetAllocatedPorts() map[string][]uint16
}

// NATError NAT错误
type NATError struct {
	Type    string `json:"type"`
	Message string `json:"message"`
	Code    int    `json:"code,omitempty"`
}

func (e *NATError) Error() string {
	return e.Message
}

// NewNATError 创建NAT错误
func NewNATError(errType, message string) *NATError {
	return &NATError{
		Type:    errType,
		Message: message,
	}
}

// 常见错误类型
const (
	ErrorTypeMappingNotFound   = "mapping_not_found"
	ErrorTypeMappingExists     = "mapping_exists"
	ErrorTypePortUnavailable   = "port_unavailable"
	ErrorTypeInvalidPacket     = "invalid_packet"
	ErrorTypeTranslationFailed = "translation_failed"
	ErrorTypeMaxMappingsReached = "max_mappings_reached"
)
