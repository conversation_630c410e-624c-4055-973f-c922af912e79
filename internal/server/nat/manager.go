package nat

import (
	"fmt"
	"net"
	"sync"
	"time"
)

// SimpleNATManager 简单NAT管理器实现
type SimpleNATManager struct {
	mappings     map[string]*NATMapping
	portMappings map[uint16]*NATMapping
	mu           sync.RWMutex
	config       *NATConfig
	metrics      *NATMetrics
	portPool     *SimplePortAllocator
}

// SimplePortAllocator 简单端口分配器
type SimplePortAllocator struct {
	used    map[uint16]bool
	mu      sync.Mutex
	start   uint16
	end     uint16
	current uint16
}

// NewSimplePortAllocator 创建简单端口分配器
func NewSimplePortAllocator(start, end uint16) *SimplePortAllocator {
	return &SimplePortAllocator{
		used:    make(map[uint16]bool),
		start:   start,
		end:     end,
		current: start,
	}
}

// AllocatePort 分配端口
func (spa *SimplePortAllocator) AllocatePort(protocol string) (uint16, error) {
	spa.mu.Lock()
	defer spa.mu.Unlock()
	
	// 查找可用端口
	for i := 0; i < int(spa.end-spa.start+1); i++ {
		port := spa.start + uint16(i+int(spa.current-spa.start))%uint16(spa.end-spa.start+1)
		
		if !spa.used[port] {
			spa.used[port] = true
			spa.current = port + 1
			if spa.current > spa.end {
				spa.current = spa.start
			}
			return port, nil
		}
	}
	
	return 0, NewNATError(ErrorTypePortUnavailable, 
		fmt.Sprintf("no available ports in range %d-%d", spa.start, spa.end))
}

// ReleasePort 释放端口
func (spa *SimplePortAllocator) ReleasePort(port uint16, protocol string) error {
	spa.mu.Lock()
	defer spa.mu.Unlock()
	
	delete(spa.used, port)
	return nil
}

// IsPortAvailable 检查端口是否可用
func (spa *SimplePortAllocator) IsPortAvailable(port uint16, protocol string) bool {
	spa.mu.Lock()
	defer spa.mu.Unlock()
	
	return !spa.used[port]
}

// GetAllocatedPorts 获取已分配端口
func (spa *SimplePortAllocator) GetAllocatedPorts() map[string][]uint16 {
	spa.mu.Lock()
	defer spa.mu.Unlock()
	
	result := make(map[string][]uint16)
	var ports []uint16
	
	for port := range spa.used {
		ports = append(ports, port)
	}
	
	result["tcp"] = ports
	result["udp"] = ports
	
	return result
}

// NewSimpleNATManager 创建简单NAT管理器
func NewSimpleNATManager(config *NATConfig) *SimpleNATManager {
	if config == nil {
		config = &NATConfig{
			MappingTimeout:  time.Minute * 10,
			CleanupInterval: time.Minute * 5,
			PortRangeStart:  10000,
			PortRangeEnd:    65535,
			MaxMappings:     10000,
			EnableMetrics:   true,
		}
	}
	
	return &SimpleNATManager{
		mappings:     make(map[string]*NATMapping),
		portMappings: make(map[uint16]*NATMapping),
		config:       config,
		metrics:      &NATMetrics{},
		portPool:     NewSimplePortAllocator(config.PortRangeStart, config.PortRangeEnd),
	}
}

// CreateMapping 创建NAT映射
func (snm *SimpleNATManager) CreateMapping(clientID string, clientIP, serverIP, targetIP net.IP, protocol string) (*NATMapping, error) {
	snm.mu.Lock()
	defer snm.mu.Unlock()
	
	// 检查是否已存在映射
	if existing, exists := snm.mappings[clientID]; exists {
		// 更新现有映射
		existing.LastUsed = time.Now()
		existing.PacketCount++
		return existing, nil
	}
	
	// 检查最大映射数限制
	if len(snm.mappings) >= snm.config.MaxMappings {
		return nil, NewNATError(ErrorTypeMaxMappingsReached, 
			fmt.Sprintf("maximum mappings reached: %d", snm.config.MaxMappings))
	}
	
	// 创建新映射
	mapping := &NATMapping{
		ClientID:    clientID,
		ClientIP:    clientIP,
		ServerIP:    serverIP,
		TargetIP:    targetIP,
		Protocol:    protocol,
		CreatedAt:   time.Now(),
		LastUsed:    time.Now(),
		PacketCount: 1,
	}
	
	// 为TCP/UDP分配端口
	if protocol == "tcp" || protocol == "udp" {
		serverPort, err := snm.portPool.AllocatePort(protocol)
		if err != nil {
			return nil, err
		}
		mapping.ServerPort = serverPort
		snm.portMappings[serverPort] = mapping
	}
	
	// 保存映射
	snm.mappings[clientID] = mapping
	
	// 更新指标
	if snm.config.EnableMetrics {
		snm.metrics.TotalMappings++
		snm.metrics.LastMapping = time.Now()
		
		switch protocol {
		case "tcp":
			snm.metrics.TCPMappings++
		case "udp":
			snm.metrics.UDPMappings++
		case "icmp":
			snm.metrics.ICMPMappings++
		}
	}
	
	return mapping, nil
}

// GetMapping 获取NAT映射
func (snm *SimpleNATManager) GetMapping(clientID string) (*NATMapping, bool) {
	snm.mu.RLock()
	defer snm.mu.RUnlock()
	
	mapping, exists := snm.mappings[clientID]
	if !exists {
		return nil, false
	}
	
	// 检查是否过期
	if time.Since(mapping.LastUsed) > snm.config.MappingTimeout {
		return nil, false
	}
	
	return mapping, true
}

// GetMappingByPort 根据端口获取映射
func (snm *SimpleNATManager) GetMappingByPort(serverPort uint16) (*NATMapping, bool) {
	snm.mu.RLock()
	defer snm.mu.RUnlock()
	
	mapping, exists := snm.portMappings[serverPort]
	if !exists {
		return nil, false
	}
	
	// 检查是否过期
	if time.Since(mapping.LastUsed) > snm.config.MappingTimeout {
		return nil, false
	}
	
	return mapping, true
}

// UpdateMapping 更新NAT映射
func (snm *SimpleNATManager) UpdateMapping(clientID string) error {
	snm.mu.Lock()
	defer snm.mu.Unlock()
	
	mapping, exists := snm.mappings[clientID]
	if !exists {
		return NewNATError(ErrorTypeMappingNotFound, 
			fmt.Sprintf("mapping not found for client %s", clientID))
	}
	
	mapping.LastUsed = time.Now()
	mapping.PacketCount++
	
	return nil
}

// DeleteMapping 删除NAT映射
func (snm *SimpleNATManager) DeleteMapping(clientID string) error {
	snm.mu.Lock()
	defer snm.mu.Unlock()
	
	mapping, exists := snm.mappings[clientID]
	if !exists {
		return NewNATError(ErrorTypeMappingNotFound, 
			fmt.Sprintf("mapping not found for client %s", clientID))
	}
	
	// 释放端口
	if mapping.ServerPort != 0 {
		snm.portPool.ReleasePort(mapping.ServerPort, mapping.Protocol)
		delete(snm.portMappings, mapping.ServerPort)
	}
	
	// 删除映射
	delete(snm.mappings, clientID)
	
	return nil
}

// TranslateOutbound 出站地址转换
func (snm *SimpleNATManager) TranslateOutbound(packet []byte, clientID string) ([]byte, error) {
	// 获取映射
	mapping, exists := snm.GetMapping(clientID)
	if !exists {
		return nil, NewNATError(ErrorTypeMappingNotFound, 
			fmt.Sprintf("no mapping found for client %s", clientID))
	}
	
	// 更新映射
	snm.UpdateMapping(clientID)
	
	// 修改数据包
	modifiedPacket := make([]byte, len(packet))
	copy(modifiedPacket, packet)
	
	// 修改源IP为服务器IP
	if len(modifiedPacket) >= 16 {
		copy(modifiedPacket[12:16], mapping.ServerIP.To4())
	}
	
	// 修改源端口（TCP/UDP）
	if (mapping.Protocol == "tcp" || mapping.Protocol == "udp") && len(modifiedPacket) >= 22 {
		modifiedPacket[20] = byte(mapping.ServerPort >> 8)
		modifiedPacket[21] = byte(mapping.ServerPort & 0xFF)
	}
	
	// 更新指标
	if snm.config.EnableMetrics {
		snm.metrics.OutboundPackets++
		snm.metrics.OutboundBytes += int64(len(packet))
	}
	
	return modifiedPacket, nil
}

// TranslateInbound 入站地址转换
func (snm *SimpleNATManager) TranslateInbound(packet []byte, targetIP net.IP) ([]byte, string, error) {
	// 这个方法需要根据目标IP或端口找到对应的映射
	// 简化实现：遍历所有映射查找匹配的
	snm.mu.RLock()
	defer snm.mu.RUnlock()
	
	for clientID, mapping := range snm.mappings {
		if mapping.TargetIP.Equal(targetIP) {
			// 找到匹配的映射
			modifiedPacket := make([]byte, len(packet))
			copy(modifiedPacket, packet)
			
			// 修改目标IP为客户端IP
			if len(modifiedPacket) >= 20 {
				copy(modifiedPacket[16:20], mapping.ClientIP.To4())
			}
			
			// 更新指标
			if snm.config.EnableMetrics {
				snm.metrics.InboundPackets++
				snm.metrics.InboundBytes += int64(len(packet))
			}
			
			return modifiedPacket, clientID, nil
		}
	}
	
	return nil, "", NewNATError(ErrorTypeMappingNotFound, 
		fmt.Sprintf("no mapping found for target IP %s", targetIP))
}

// GetMappings 获取所有映射
func (snm *SimpleNATManager) GetMappings() map[string]*NATMapping {
	snm.mu.RLock()
	defer snm.mu.RUnlock()
	
	result := make(map[string]*NATMapping)
	for k, v := range snm.mappings {
		result[k] = v
	}
	
	return result
}

// Cleanup 清理过期映射
func (snm *SimpleNATManager) Cleanup() int {
	snm.mu.Lock()
	defer snm.mu.Unlock()
	
	now := time.Now()
	var expiredClients []string
	var expiredPorts []uint16
	
	// 查找过期映射
	for clientID, mapping := range snm.mappings {
		if now.Sub(mapping.LastUsed) > snm.config.MappingTimeout {
			expiredClients = append(expiredClients, clientID)
			if mapping.ServerPort != 0 {
				expiredPorts = append(expiredPorts, mapping.ServerPort)
			}
		}
	}
	
	// 删除过期映射
	for i, clientID := range expiredClients {
		delete(snm.mappings, clientID)
		if i < len(expiredPorts) {
			delete(snm.portMappings, expiredPorts[i])
			snm.portPool.ReleasePort(expiredPorts[i], "")
		}
	}
	
	// 更新指标
	if snm.config.EnableMetrics {
		snm.metrics.ExpiredMappings += int64(len(expiredClients))
		snm.metrics.LastCleanup = now
	}
	
	return len(expiredClients)
}

// GetMetrics 获取NAT指标
func (snm *SimpleNATManager) GetMetrics() *NATMetrics {
	snm.mu.RLock()
	defer snm.mu.RUnlock()
	
	// 更新活跃映射数
	snm.metrics.ActiveMappings = len(snm.mappings)
	
	return snm.metrics
}
