package server

import (
	"fmt"
	"net"
	"sync"
	"testing"
	"time"

	"go.uber.org/zap/zaptest"
)

// MockTCPServer 模拟TCP服务器用于测试
type MockTCPServer struct {
	listener     net.Listener
	address      string
	responses    [][]byte
	requests     [][]byte
	mu           sync.Mutex
	closeAfter   int // 在多少个请求后关闭连接
	requestCount int
}

// NewMockTCPServer 创建模拟TCP服务器
func NewMockTCPServer(responses [][]byte, closeAfter int) (*MockTCPServer, error) {
	listener, err := net.Listen("tcp", "127.0.0.1:0")
	if err != nil {
		return nil, err
	}

	server := &MockTCPServer{
		listener:   listener,
		address:    listener.Addr().String(),
		responses:  responses,
		closeAfter: closeAfter,
	}

	go server.serve()
	return server, nil
}

func (s *MockTCPServer) serve() {
	for {
		conn, err := s.listener.Accept()
		if err != nil {
			return
		}
		go s.handleConnection(conn)
	}
}

func (s *MockTCPServer) handleConnection(conn net.Conn) {
	defer conn.Close()

	buffer := make([]byte, 4096)
	responseIndex := 0

	for {
		n, err := conn.Read(buffer)
		if err != nil {
			return
		}

		s.mu.Lock()
		s.requests = append(s.requests, buffer[:n])
		s.requestCount++
		shouldClose := s.closeAfter > 0 && s.requestCount >= s.closeAfter
		s.mu.Unlock()

		// 发送响应
		if responseIndex < len(s.responses) {
			conn.Write(s.responses[responseIndex])
			responseIndex++
		}

		// 如果设置了closeAfter，在指定请求数后关闭连接
		if shouldClose {
			return
		}
	}
}

func (s *MockTCPServer) Close() error {
	return s.listener.Close()
}

func (s *MockTCPServer) GetRequests() [][]byte {
	s.mu.Lock()
	defer s.mu.Unlock()
	return append([][]byte(nil), s.requests...)
}

func (s *MockTCPServer) GetAddress() string {
	return s.address
}

// TestTCPConnectionPool 测试TCP连接池基本功能
func TestTCPConnectionPool(t *testing.T) {
	logger := zaptest.NewLogger(t)

	// 创建模拟服务器
	responses := [][]byte{
		[]byte("HTTP/1.1 200 OK\r\nContent-Length: 5\r\n\r\nHello"),
		[]byte("HTTP/1.1 200 OK\r\nContent-Length: 5\r\n\r\nWorld"),
	}
	mockServer, err := NewMockTCPServer(responses, 0) // 不自动关闭连接
	if err != nil {
		t.Fatalf("Failed to create mock server: %v", err)
	}
	defer mockServer.Close()

	// 创建连接池
	pool := NewTCPConnectionPool(10, 50, time.Minute*5, logger)

	// 测试获取连接
	conn1, err := pool.GetConnection(mockServer.GetAddress(), time.Second*5)
	if err != nil {
		t.Fatalf("Failed to get connection: %v", err)
	}

	// 测试连接复用
	conn2, err := pool.GetConnection(mockServer.GetAddress(), time.Second*5)
	if err != nil {
		t.Fatalf("Failed to get connection: %v", err)
	}

	// 应该复用同一个连接
	if conn1.ID != conn2.ID {
		t.Errorf("Expected connection reuse, got different connections: %s vs %s", conn1.ID, conn2.ID)
	}

	// 测试连接健康检查
	if !pool.isConnectionHealthy(conn1) {
		t.Error("Expected connection to be healthy")
	}

	// 测试连接清理
	pool.Cleanup()
}

// TestTCPConnectionPoolBrokenConnection 测试连接断开处理
func TestTCPConnectionPoolBrokenConnection(t *testing.T) {
	logger := zaptest.NewLogger(t)

	// 创建会在第一个请求后关闭连接的模拟服务器
	responses := [][]byte{
		[]byte("HTTP/1.1 200 OK\r\nContent-Length: 5\r\n\r\nHello"),
	}
	mockServer, err := NewMockTCPServer(responses, 1) // 1个请求后关闭连接
	if err != nil {
		t.Fatalf("Failed to create mock server: %v", err)
	}
	defer mockServer.Close()

	pool := NewTCPConnectionPool(10, 50, time.Minute*5, logger)

	// 获取连接并发送数据
	conn, err := pool.GetConnection(mockServer.GetAddress(), time.Second*5)
	if err != nil {
		t.Fatalf("Failed to get connection: %v", err)
	}

	// 发送第一个请求
	_, err = conn.Conn.Write([]byte("GET / HTTP/1.1\r\n\r\n"))
	if err != nil {
		t.Fatalf("Failed to write to connection: %v", err)
	}

	// 读取响应
	buffer := make([]byte, 1024)
	_, err = conn.Conn.Read(buffer)
	if err != nil {
		t.Fatalf("Failed to read response: %v", err)
	}

	// 等待服务器关闭连接
	time.Sleep(time.Millisecond * 100)

	// 尝试再次写入，应该失败
	_, err = conn.Conn.Write([]byte("GET /second HTTP/1.1\r\n\r\n"))
	if err == nil {
		t.Error("Expected write to fail on broken connection")
	}

	// 验证连接断开检测
	if !isConnectionBroken(err) {
		t.Errorf("Expected connection broken error, got: %v", err)
	}

	// 移除断开的连接
	pool.RemoveConnection(mockServer.GetAddress())

	// 获取新连接应该成功
	newConn, err := pool.GetConnection(mockServer.GetAddress(), time.Second*5)
	if err != nil {
		t.Fatalf("Failed to get new connection: %v", err)
	}

	// 应该是不同的连接
	if conn.ID == newConn.ID {
		t.Error("Expected new connection, got same connection ID")
	}
}

// TestTCPHandlerDataForwarding 测试TCP数据转发
func TestTCPHandlerDataForwarding(t *testing.T) {
	logger := zaptest.NewLogger(t)

	// 创建模拟服务器
	responses := [][]byte{
		[]byte("HTTP/1.1 200 OK\r\nContent-Length: 13\r\n\r\nHello, World!"),
	}
	mockServer, err := NewMockTCPServer(responses, 0)
	if err != nil {
		t.Fatalf("Failed to create mock server: %v", err)
	}
	defer mockServer.Close()

	// 创建模拟Server
	mockServerInstance := &Server{
		logger:              logger,
		addressMappingCache: NewAddressMappingCache(time.Minute * 10),
	}

	// 创建TCP处理器
	handler := NewTCPHandler(mockServerInstance, logger)
	err = handler.Start()
	if err != nil {
		t.Fatalf("Failed to start TCP handler: %v", err)
	}
	defer handler.Stop()

	// 解析目标地址
	host, portStr, err := net.SplitHostPort(mockServer.GetAddress())
	if err != nil {
		t.Fatalf("Failed to parse address: %v", err)
	}

	var port uint16
	fmt.Sscanf(portStr, "%d", &port)

	// 创建TCP包信息
	tcpInfo := &TCPPacketInfo{
		SrcIP:   net.ParseIP("*************"),
		DstIP:   net.ParseIP(host),
		SrcPort: 12345,
		DstPort: port,
		PSH:     true,
		ACK:     true,
		Payload: []byte("GET / HTTP/1.1\r\nHost: example.com\r\n\r\n"),
	}

	// 获取连接
	conn, err := handler.connectionPool.GetConnection(mockServer.GetAddress(), time.Second*5)
	if err != nil {
		t.Fatalf("Failed to get connection: %v", err)
	}

	// 转发数据
	response, err := handler.forwardTCPData(conn, tcpInfo, "test-client")
	if err != nil {
		t.Fatalf("Failed to forward TCP data: %v", err)
	}

	if response == nil {
		t.Error("Expected response packet, got nil")
	}

	// 验证请求被正确发送
	requests := mockServer.GetRequests()
	if len(requests) != 1 {
		t.Errorf("Expected 1 request, got %d", len(requests))
	}

	expectedRequest := "GET / HTTP/1.1\r\nHost: example.com\r\n\r\n"
	if string(requests[0]) != expectedRequest {
		t.Errorf("Expected request %q, got %q", expectedRequest, string(requests[0]))
	}
}

// TestTCPHandlerRetryMechanism 测试重试机制
func TestTCPHandlerRetryMechanism(t *testing.T) {
	logger := zaptest.NewLogger(t)

	// 创建会在第一个请求后关闭连接的模拟服务器
	responses := [][]byte{
		[]byte("HTTP/1.1 200 OK\r\nContent-Length: 5\r\n\r\nHello"),
		[]byte("HTTP/1.1 200 OK\r\nContent-Length: 5\r\n\r\nRetry"),
	}
	mockServer, err := NewMockTCPServer(responses, 1) // 1个请求后关闭连接
	if err != nil {
		t.Fatalf("Failed to create mock server: %v", err)
	}
	defer mockServer.Close()

	// 创建模拟Server
	mockServerInstance := &Server{
		logger:              logger,
		addressMappingCache: NewAddressMappingCache(time.Minute * 10),
	}

	handler := NewTCPHandler(mockServerInstance, logger)
	err = handler.Start()
	if err != nil {
		t.Fatalf("Failed to start TCP handler: %v", err)
	}
	defer handler.Stop()

	// 解析目标地址
	host, portStr, err := net.SplitHostPort(mockServer.GetAddress())
	if err != nil {
		t.Fatalf("Failed to parse address: %v", err)
	}

	var port uint16
	fmt.Sscanf(portStr, "%d", &port)

	tcpInfo := &TCPPacketInfo{
		SrcIP:   net.ParseIP("*************"),
		DstIP:   net.ParseIP(host),
		SrcPort: 12345,
		DstPort: port,
		PSH:     true,
		ACK:     true,
		Payload: []byte("GET / HTTP/1.1\r\n\r\n"),
	}

	// 获取初始连接
	conn, err := handler.connectionPool.GetConnection(mockServer.GetAddress(), time.Second*5)
	if err != nil {
		t.Fatalf("Failed to get connection: %v", err)
	}

	// 第一次转发 - 应该成功但连接会被关闭
	response, err := handler.forwardTCPData(conn, tcpInfo, "test-client")
	if err != nil {
		t.Fatalf("Failed to forward TCP data: %v", err)
	}
	if response == nil {
		t.Error("Expected response packet, got nil")
	}

	// 等待连接关闭
	time.Sleep(time.Millisecond * 100)

	// 第二次转发 - 应该触发重试机制
	response, err = handler.forwardTCPData(conn, tcpInfo, "test-client")
	if err != nil {
		t.Fatalf("Failed to forward TCP data on retry: %v", err)
	}
	if response == nil {
		t.Error("Expected response packet on retry, got nil")
	}
}

// TestTCPHandlerConcurrentForwarding 测试并发转发
func TestTCPHandlerConcurrentForwarding(t *testing.T) {
	logger := zaptest.NewLogger(t)

	// 创建模拟服务器 - 每个连接都响应
	responses := make([][]byte, 50) // 足够多的响应
	for i := 0; i < 50; i++ {
		responses[i] = []byte(fmt.Sprintf("HTTP/1.1 200 OK\r\nContent-Length: 2\r\n\r\n%02d", i))
	}
	mockServer, err := NewMockTCPServer(responses, 0) // 不自动关闭连接
	if err != nil {
		t.Fatalf("Failed to create mock server: %v", err)
	}
	defer mockServer.Close()

	// 创建模拟Server
	mockServerInstance := &Server{
		logger:              logger,
		addressMappingCache: NewAddressMappingCache(time.Minute * 10),
	}

	handler := NewTCPHandler(mockServerInstance, logger)
	// 减少读取超时以加快测试
	handler.readTimeout = time.Second * 2
	err = handler.Start()
	if err != nil {
		t.Fatalf("Failed to start TCP handler: %v", err)
	}
	defer handler.Stop()

	// 解析目标地址
	host, portStr, err := net.SplitHostPort(mockServer.GetAddress())
	if err != nil {
		t.Fatalf("Failed to parse address: %v", err)
	}

	var port uint16
	fmt.Sscanf(portStr, "%d", &port)

	// 并发测试 - 减少并发数和请求数
	const numGoroutines = 3
	const requestsPerGoroutine = 2

	var wg sync.WaitGroup
	errors := make(chan error, numGoroutines*requestsPerGoroutine)

	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()

			for j := 0; j < requestsPerGoroutine; j++ {
				tcpInfo := &TCPPacketInfo{
					SrcIP:   net.ParseIP("*************"),
					DstIP:   net.ParseIP(host),
					SrcPort: uint16(12345 + goroutineID*100 + j),
					DstPort: port,
					PSH:     true,
					ACK:     true,
					Payload: []byte(fmt.Sprintf("GET /%d-%d HTTP/1.1\r\n\r\n", goroutineID, j)),
				}

				conn, err := handler.connectionPool.GetConnection(mockServer.GetAddress(), time.Second*5)
				if err != nil {
					errors <- fmt.Errorf("goroutine %d request %d: failed to get connection: %w", goroutineID, j, err)
					return
				}

				_, err = handler.forwardTCPData(conn, tcpInfo, fmt.Sprintf("client-%d-%d", goroutineID, j))
				if err != nil {
					errors <- fmt.Errorf("goroutine %d request %d: failed to forward data: %w", goroutineID, j, err)
					return
				}
			}
		}(i)
	}

	wg.Wait()
	close(errors)

	// 检查错误
	var errorList []error
	for err := range errors {
		errorList = append(errorList, err)
	}

	if len(errorList) > 0 {
		t.Errorf("Concurrent forwarding failed with %d errors:", len(errorList))
		for _, err := range errorList {
			t.Errorf("  - %v", err)
		}
	}

	// 验证请求被处理（允许一些失败，因为并发可能导致连接竞争）
	requests := mockServer.GetRequests()
	expectedRequests := numGoroutines * requestsPerGoroutine
	if len(requests) < expectedRequests/2 { // 至少处理一半的请求
		t.Errorf("Expected at least %d requests, got %d", expectedRequests/2, len(requests))
	}
}

// TestIsConnectionBroken 测试连接断开检测
func TestIsConnectionBroken(t *testing.T) {
	testCases := []struct {
		name     string
		err      error
		expected bool
	}{
		{"nil error", nil, false},
		{"EOF error", fmt.Errorf("EOF"), true},
		{"broken pipe", fmt.Errorf("write tcp: broken pipe"), true},
		{"connection reset", fmt.Errorf("connection reset by peer"), true},
		{"connection refused", fmt.Errorf("connection refused"), true},
		{"connection aborted", fmt.Errorf("connection aborted"), true},
		{"other error", fmt.Errorf("some other error"), false},
		{"timeout error", fmt.Errorf("timeout"), false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := isConnectionBroken(tc.err)
			if result != tc.expected {
				t.Errorf("isConnectionBroken(%v) = %v, expected %v", tc.err, result, tc.expected)
			}
		})
	}
}
