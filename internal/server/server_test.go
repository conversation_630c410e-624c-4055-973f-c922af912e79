package server

import (
	"net"
	"strings"
	"testing"
	"time"

	"cyber-bastion/pkg/config"
	"cyber-bastion/pkg/logger"
	"cyber-bastion/pkg/protocol"
)

func TestNewClient(t *testing.T) {
	// Create a mock connection
	server, client := net.Pipe()
	defer server.Close()
	defer client.Close()

	c := NewClient("test-id", client)

	if c.ID != "test-id" {
		t.<PERSON><PERSON>("Expected ID 'test-id', got '%s'", c.ID)
	}
	if c.Conn != client {
		t.Error("Expected connection to match")
	}
	if c.Authorized {
		t.Error("Expected client to be unauthorized initially")
	}
	if c.LastSeen.IsZero() {
		t.<PERSON>("Expected LastSeen to be set")
	}
}

func TestClientUpdateLastSeen(t *testing.T) {
	server, client := net.Pipe()
	defer server.Close()
	defer client.Close()

	c := NewClient("test-id", client)
	originalTime := c.LastSeen

	time.Sleep(10 * time.Millisecond)
	c.UpdateLastSeen()

	if !c.LastSeen.After(originalTime) {
		t.<PERSON>r("Expected LastSeen to be updated")
	}
}

func TestClientAuthorization(t *testing.T) {
	server, client := net.Pipe()
	defer server.Close()
	defer client.Close()

	c := NewClient("test-id", client)

	if c.IsAuthorized() {
		t.Error("Expected client to be unauthorized initially")
	}

	c.SetAuthorized(true)
	if !c.IsAuthorized() {
		t.Error("Expected client to be authorized after setting")
	}

	c.SetAuthorized(false)
	if c.IsAuthorized() {
		t.Error("Expected client to be unauthorized after unsetting")
	}
}

func TestNewServer(t *testing.T) {
	cfg := &config.ServerConfig{
		Host:         "localhost",
		Port:         8080,
		ReadTimeout:  30,
		WriteTimeout: 30,
		MaxClients:   100,
		AuthToken:    "test-token",
	}

	log, err := logger.NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	srv := NewServer(cfg, log)

	if srv.config != cfg {
		t.Error("Expected config to match")
	}
	if srv.logger != log {
		t.Error("Expected logger to match")
	}
	if srv.clients == nil {
		t.Error("Expected clients map to be initialized")
	}
	if srv.ctx == nil {
		t.Error("Expected context to be initialized")
	}
}

func TestServerClientManagement(t *testing.T) {
	cfg := &config.ServerConfig{
		Host:         "localhost",
		Port:         8080,
		ReadTimeout:  30,
		WriteTimeout: 30,
		MaxClients:   100,
		AuthToken:    "test-token",
	}

	log, err := logger.NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	srv := NewServer(cfg, log)

	// Test adding client
	server, client := net.Pipe()
	defer server.Close()
	defer client.Close()

	c := NewClient("test-id", client)
	srv.addClient(c)

	if srv.getClientCount() != 1 {
		t.Errorf("Expected 1 client, got %d", srv.getClientCount())
	}

	// Test removing client
	srv.removeClient("test-id")
	if srv.getClientCount() != 0 {
		t.Errorf("Expected 0 clients, got %d", srv.getClientCount())
	}
}

func TestServerHandleAuth(t *testing.T) {
	cfg := &config.ServerConfig{
		Host:         "localhost",
		Port:         8080,
		ReadTimeout:  30,
		WriteTimeout: 30,
		MaxClients:   100,
		AuthToken:    "test-token",
	}

	log, err := logger.NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	srv := NewServer(cfg, log)

	// Create mock client
	server, clientConn := net.Pipe()
	defer server.Close()
	defer clientConn.Close()

	client := NewClient("test-id", clientConn)

	// Test successful authentication
	authMsg := protocol.NewAuthMessage("test-token")

	// Handle auth in goroutine since it will try to send response
	done := make(chan error, 1)
	go func() {
		done <- srv.handleAuth(client, authMsg)
	}()

	// Read the response
	go func() {
		_, err := protocol.ReceiveMessage(server)
		if err != nil {
			t.Logf("Error receiving auth response: %v", err)
		}
	}()

	select {
	case err := <-done:
		if err != nil {
			t.Errorf("Expected successful auth, got error: %v", err)
		}
	case <-time.After(1 * time.Second):
		t.Error("Auth handling timed out")
	}

	if !client.IsAuthorized() {
		t.Error("Expected client to be authorized after successful auth")
	}

	// Test failed authentication
	client.SetAuthorized(false)
	authMsg = protocol.NewAuthMessage("wrong-token")

	// Handle failed auth in goroutine
	done2 := make(chan error, 1)
	go func() {
		done2 <- srv.handleAuth(client, authMsg)
	}()

	// Read the error response
	go func() {
		_, err := protocol.ReceiveMessage(server)
		if err != nil {
			t.Logf("Error receiving auth error response: %v", err)
		}
	}()

	select {
	case err := <-done2:
		if err != nil {
			t.Errorf("Expected error handling to succeed, got error: %v", err)
		}
	case <-time.After(1 * time.Second):
		t.Error("Failed auth handling timed out")
	}

	if client.IsAuthorized() {
		t.Error("Expected client to remain unauthorized after failed auth")
	}
}

func TestServerHandleHeartbeat(t *testing.T) {
	cfg := &config.ServerConfig{
		Host:         "localhost",
		Port:         8080,
		ReadTimeout:  30,
		WriteTimeout: 30,
		MaxClients:   100,
		AuthToken:    "test-token",
	}

	log, err := logger.NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	srv := NewServer(cfg, log)

	// Create mock client
	server, clientConn := net.Pipe()
	defer server.Close()
	defer clientConn.Close()

	client := NewClient("test-id", clientConn)

	// Test heartbeat handling
	heartbeatMsg := protocol.NewHeartbeat()

	// Handle heartbeat in goroutine since it will try to send response
	done := make(chan error, 1)
	go func() {
		done <- srv.handleHeartbeat(client, heartbeatMsg)
	}()

	// Read the response
	go func() {
		_, err := protocol.ReceiveMessage(server)
		if err != nil {
			t.Logf("Error receiving heartbeat response: %v", err)
		}
	}()

	select {
	case err := <-done:
		if err != nil {
			t.Errorf("Expected successful heartbeat handling, got error: %v", err)
		}
	case <-time.After(1 * time.Second):
		t.Error("Heartbeat handling timed out")
	}
}

func TestServerHandleData(t *testing.T) {
	cfg := &config.ServerConfig{
		Host:         "localhost",
		Port:         8080,
		ReadTimeout:  30,
		WriteTimeout: 30,
		MaxClients:   100,
		AuthToken:    "test-token",
	}

	log, err := logger.NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	srv := NewServer(cfg, log)

	// Create mock client
	server, clientConn := net.Pipe()
	defer server.Close()
	defer clientConn.Close()

	client := NewClient("test-id", clientConn)
	client.SetAuthorized(true) // Authorize client for data handling

	// Test data handling
	dataMsg := protocol.NewDataMessage("data-123", []byte("test data"))

	// Handle data in goroutine since it will try to send response
	done := make(chan error, 1)
	go func() {
		done <- srv.handleData(client, dataMsg)
	}()

	// Read the response
	go func() {
		response, err := protocol.ReceiveMessage(server)
		if err != nil {
			t.Logf("Error receiving data response: %v", err)
			return
		}
		if response.Type != protocol.MessageTypeResponse {
			t.Errorf("Expected response message, got type %d", response.Type)
		}
	}()

	select {
	case err := <-done:
		if err != nil {
			t.Errorf("Expected successful data handling, got error: %v", err)
		}
	case <-time.After(1 * time.Second):
		t.Error("Data handling timed out")
	}
}

func TestServerHandleDataUnauthorized(t *testing.T) {
	cfg := &config.ServerConfig{
		Host:         "localhost",
		Port:         8080,
		ReadTimeout:  30,
		WriteTimeout: 30,
		MaxClients:   100,
		AuthToken:    "test-token",
	}

	log, err := logger.NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	srv := NewServer(cfg, log)

	// Create mock client (not authorized)
	server, clientConn := net.Pipe()
	defer server.Close()
	defer clientConn.Close()

	client := NewClient("test-id", clientConn)
	// Don't authorize client

	// Test data handling with unauthorized client
	dataMsg := protocol.NewDataMessage("data-123", []byte("test data"))

	// Handle message (not handleData directly) since authorization check is in handleMessage
	done := make(chan error, 1)
	go func() {
		done <- srv.handleMessage(client, dataMsg)
	}()

	// Read the error response
	go func() {
		response, err := protocol.ReceiveMessage(server)
		if err != nil {
			t.Logf("Error receiving error response: %v", err)
			return
		}
		if response.Type != protocol.MessageTypeError {
			t.Errorf("Expected error message, got type %d", response.Type)
		}
	}()

	select {
	case err := <-done:
		if err != nil {
			t.Errorf("Expected error handling to succeed, got error: %v", err)
		}
	case <-time.After(1 * time.Second):
		t.Error("Error handling timed out")
	}
}

func TestServerStop(t *testing.T) {
	cfg := &config.ServerConfig{
		Host:         "localhost",
		Port:         0, // Use random port
		ReadTimeout:  30,
		WriteTimeout: 30,
		MaxClients:   100,
		AuthToken:    "test-token",
	}

	log, err := logger.NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	srv := NewServer(cfg, log)

	// Test stopping server without starting
	err = srv.Stop()
	if err != nil {
		t.Errorf("Expected successful stop, got error: %v", err)
	}
}

func TestServerMaxClients(t *testing.T) {
	cfg := &config.ServerConfig{
		Host:         "localhost",
		Port:         8080,
		ReadTimeout:  30,
		WriteTimeout: 30,
		MaxClients:   2, // Set low limit for testing
		AuthToken:    "test-token",
	}

	log, err := logger.NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	srv := NewServer(cfg, log)

	// Add clients up to the limit
	for i := 0; i < 2; i++ {
		server, client := net.Pipe()
		defer server.Close()
		defer client.Close()

		c := NewClient("test-id-"+string(rune(i)), client)
		srv.addClient(c)
	}

	if srv.getClientCount() != 2 {
		t.Errorf("Expected 2 clients, got %d", srv.getClientCount())
	}

	// Test that we've reached the limit
	if srv.getClientCount() >= cfg.MaxClients {
		t.Log("Max clients limit reached as expected")
	}
}

func TestServerHTTPMasquerading(t *testing.T) {
	cfg := &config.ServerConfig{
		Host:         "localhost",
		Port:         8080,
		ReadTimeout:  30,
		WriteTimeout: 30,
		MaxClients:   100,
		AuthToken:    "test-token",
		Security: &config.SecurityConfig{
			EnableTLS:   true,
			TLSCertFile: "../../certs/server.crt",
			TLSKeyFile:  "../../certs/server.key",
			TLSCAFile:   "../../certs/ca.crt",
		},
	}

	log, err := logger.NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	srv := NewServer(cfg, log)

	// Test HTTP request detection
	t.Run("HTTP Request Detection", func(t *testing.T) {
		// Create a mock connection with HTTP data
		server, client := net.Pipe()
		defer server.Close()
		defer client.Close()

		// Send HTTP request from client side
		go func() {
			client.Write([]byte("GET / HTTP/1.1\r\nHost: localhost\r\n\r\n"))
		}()

		// Test HTTP detection
		peekConn := newPeekableConn(server)
		isHTTP := srv.isHTTPRequestWithPeek(peekConn, "test-client")

		if !isHTTP {
			t.Error("Expected HTTP request to be detected")
		}
	})

	t.Run("Non-HTTP Request Detection", func(t *testing.T) {
		// Create a mock connection with non-HTTP data
		server, client := net.Pipe()
		defer server.Close()
		defer client.Close()

		// Send non-HTTP data from client side
		go func() {
			client.Write([]byte("\x16\x03\x01\x01\x3a\x01\x00\x01\x36\x03\x03"))
		}()

		// Test non-HTTP detection
		peekConn := newPeekableConn(server)
		isHTTP := srv.isHTTPRequestWithPeek(peekConn, "test-client")

		if isHTTP {
			t.Error("Expected non-HTTP request to not be detected as HTTP")
		}
	})

	t.Run("HTTP Response Building", func(t *testing.T) {
		response := srv.buildHTTPResponse()

		if !strings.Contains(response, "HTTP/1.1 200 OK") {
			t.Error("Expected HTTP response to contain status line")
		}

		if !strings.Contains(response, "Content-Type: application/json") {
			t.Error("Expected HTTP response to contain JSON content type")
		}

		if !strings.Contains(response, `"status": "ok"`) {
			t.Error("Expected HTTP response to contain JSON status")
		}

		if !strings.Contains(response, "Server: nginx/1.18.0") {
			t.Error("Expected HTTP response to contain server header for masquerading")
		}
	})
}
