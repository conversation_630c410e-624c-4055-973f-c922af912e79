package server

import (
	"net"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
)

// TestTCPTransparentForwarding 测试TCP透明转发功能
func TestTCPTransparentForwarding(t *testing.T) {
	logger := zap.NewNop()
	server := &Server{
		logger: logger,
	}

	t.Run("TCP包解析测试", func(t *testing.T) {
		// 构造一个TCP SYN包
		packet := buildTestTCPPacket(
			net.ParseIP("*************"), 12345, // 源IP和端口
			net.ParseIP("*******"), 80,          // 目标IP和端口
			0x02, // SYN标志
			[]byte{}, // 无载荷
		)

		require.True(t, len(packet) >= 40, "TCP包长度应该至少40字节")

		// 解析包头信息
		srcIP := net.IP(packet[12:16])
		dstIP := net.IP(packet[16:20])
		tcpHeader := packet[20:]
		srcPort := uint16(tcpHeader[0])<<8 | uint16(tcpHeader[1])
		dstPort := uint16(tcpHeader[2])<<8 | uint16(tcpHeader[3])
		flags := tcpHeader[13]

		assert.Equal(t, "*************", srcIP.String())
		assert.Equal(t, "*******", dstIP.String())
		assert.Equal(t, uint16(12345), srcPort)
		assert.Equal(t, uint16(80), dstPort)
		assert.Equal(t, uint8(0x02), flags) // SYN标志
	})

	t.Run("TCP响应包构造测试", func(t *testing.T) {
		srcIP := net.ParseIP("*******")
		srcPort := uint16(80)
		dstIP := net.ParseIP("*************")
		dstPort := uint16(12345)
		tcpData := []byte{0x50, 0x18, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00} // 简单的TCP头部

		responsePacket, err := server.buildTCPTransparentResponsePacket(srcIP, srcPort, dstIP, dstPort, tcpData)
		require.NoError(t, err)
		require.True(t, len(responsePacket) >= 20, "响应包应该包含IP头部")

		// 验证IP头部
		assert.Equal(t, uint8(0x45), responsePacket[0]) // 版本和头长度
		assert.Equal(t, uint8(0x06), responsePacket[9]) // TCP协议

		// 验证源IP和目标IP
		responseSrcIP := net.IP(responsePacket[12:16])
		responseDstIP := net.IP(responsePacket[16:20])
		assert.Equal(t, srcIP.String(), responseSrcIP.String())
		assert.Equal(t, dstIP.String(), responseDstIP.String())

		// 验证TCP数据
		tcpDataStart := 20
		assert.Equal(t, tcpData, responsePacket[tcpDataStart:tcpDataStart+len(tcpData)])
	})
}

// TestTCPTransparentVsApplicationLayer 测试透明代理与应用层代理的区别
func TestTCPTransparentVsApplicationLayer(t *testing.T) {
	t.Run("透明代理特性验证", func(t *testing.T) {
		// 透明代理应该：
		// 1. 不解析应用层协议
		// 2. 直接转发原始TCP数据
		// 3. 保持端到端连接的透明性
		// 4. 不干预TLS握手

		testData := []struct {
			name        string
			tcpData     []byte
			shouldParse bool
		}{
			{
				name:        "TLS握手数据",
				tcpData:     []byte{0x16, 0x03, 0x01, 0x00, 0x01, 0x01}, // TLS握手
				shouldParse: false, // 透明代理不应该解析TLS
			},
			{
				name:        "HTTP请求数据",
				tcpData:     []byte("GET / HTTP/1.1\r\nHost: example.com\r\n\r\n"),
				shouldParse: false, // 透明代理不应该解析HTTP
			},
			{
				name:        "任意二进制数据",
				tcpData:     []byte{0x00, 0x01, 0x02, 0x03, 0xFF, 0xFE},
				shouldParse: false, // 透明代理不应该解析任何应用层数据
			},
		}

		for _, tt := range testData {
			t.Run(tt.name, func(t *testing.T) {
				// 透明代理的核心原则：不解析载荷内容
				// 只需要验证数据能够原样转发
				assert.False(t, tt.shouldParse, "透明代理不应该解析应用层数据")
				
				// 验证数据完整性
				assert.True(t, len(tt.tcpData) > 0, "测试数据不应为空")
			})
		}
	})
}

// TestTCPPacketConstruction 测试TCP包构造的正确性
func TestTCPPacketConstruction(t *testing.T) {
	t.Run("IP头部校验和计算", func(t *testing.T) {
		// 创建一个简单的IP头部
		ipHeader := make([]byte, 20)
		ipHeader[0] = 0x45  // 版本和头长度
		ipHeader[1] = 0x00  // 服务类型
		ipHeader[2] = 0x00  // 总长度高字节
		ipHeader[3] = 0x14  // 总长度低字节 (20字节)
		ipHeader[8] = 0x40  // TTL
		ipHeader[9] = 0x06  // 协议(TCP)

		// 源IP: ***********
		ipHeader[12] = 192
		ipHeader[13] = 168
		ipHeader[14] = 1
		ipHeader[15] = 1

		// 目标IP: *******
		ipHeader[16] = 8
		ipHeader[17] = 8
		ipHeader[18] = 8
		ipHeader[19] = 8

		// 计算校验和前先清零
		ipHeader[10] = 0
		ipHeader[11] = 0

		// 手动计算校验和进行验证
		var sum uint32
		for i := 0; i < 20; i += 2 {
			sum += uint32(ipHeader[i])<<8 + uint32(ipHeader[i+1])
		}
		for sum>>16 != 0 {
			sum = (sum & 0xffff) + (sum >> 16)
		}
		expectedChecksum := uint16(^sum)

		// 使用服务器的校验和计算方法
		server := &Server{}
		server.calculateIPChecksum(ipHeader)
		actualChecksum := uint16(ipHeader[10])<<8 | uint16(ipHeader[11])

		assert.Equal(t, expectedChecksum, actualChecksum, "IP头部校验和计算错误")
	})
}

// buildTestTCPPacket 构造测试用的TCP数据包
func buildTestTCPPacket(srcIP net.IP, srcPort uint16, dstIP net.IP, dstPort uint16, flags uint8, payload []byte) []byte {
	ipHeaderLen := 20
	tcpHeaderLen := 20
	totalLen := ipHeaderLen + tcpHeaderLen + len(payload)
	
	packet := make([]byte, totalLen)
	
	// IP头部
	packet[0] = 0x45                    // 版本(4) + 头长度(5*4=20字节)
	packet[1] = 0x00                    // 服务类型
	packet[2] = byte(totalLen >> 8)     // 总长度高字节
	packet[3] = byte(totalLen & 0xff)   // 总长度低字节
	packet[4] = 0x00                    // 标识
	packet[5] = 0x00
	packet[6] = 0x40                    // 标志(DF=1)
	packet[7] = 0x00
	packet[8] = 0x40                    // TTL
	packet[9] = 0x06                    // 协议(TCP=6)
	packet[10] = 0x00                   // 校验和(稍后计算)
	packet[11] = 0x00
	
	// 源IP和目标IP
	copy(packet[12:16], srcIP.To4())
	copy(packet[16:20], dstIP.To4())
	
	// TCP头部
	tcpStart := ipHeaderLen
	packet[tcpStart] = byte(srcPort >> 8)     // 源端口
	packet[tcpStart+1] = byte(srcPort & 0xff)
	packet[tcpStart+2] = byte(dstPort >> 8)   // 目标端口
	packet[tcpStart+3] = byte(dstPort & 0xff)
	
	// 序列号和确认号(简化为0)
	// packet[tcpStart+4:tcpStart+12] 保持为0
	
	packet[tcpStart+12] = 0x50            // 数据偏移(5*4=20字节)
	packet[tcpStart+13] = flags           // TCP标志
	packet[tcpStart+14] = 0x20            // 窗口大小
	packet[tcpStart+15] = 0x00
	// 校验和和紧急指针保持为0
	
	// 复制载荷
	if len(payload) > 0 {
		copy(packet[tcpStart+tcpHeaderLen:], payload)
	}
	
	return packet
}
