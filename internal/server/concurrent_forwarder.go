package server

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"sync/atomic"
	"time"

	"go.uber.org/zap"
)

// ForwardingJob 转发任务
type ForwardingJob struct {
	ID        string
	Packet    []byte
	ClientID  string
	MessageID string
	Client    *Client
	Timestamp time.Time
	ResultCh  chan *ForwardingResult
}

// ForwardingResult 转发结果
type ForwardingResult struct {
	JobID        string
	ResponseData []byte
	Error        error
	Duration     time.Duration
}

// ConcurrentForwarder 并发转发器
type ConcurrentForwarder struct {
	server      *Server
	logger      *zap.Logger
	ctx         context.Context
	cancel      context.CancelFunc
	wg          sync.WaitGroup

	// 工作池配置
	workerCount   int
	jobQueue      chan *ForwardingJob
	queueSize     int

	// 统计信息
	totalJobs     int64
	completedJobs int64
	failedJobs    int64
	activeJobs    int64

	// 性能监控
	avgDuration   time.Duration
	maxDuration   time.Duration
	durationMutex sync.RWMutex

	// 控制参数
	maxConcurrent int
	timeout       time.Duration
}

// NewConcurrentForwarder 创建并发转发器
func NewConcurrentForwarder(server *Server, logger *zap.Logger) *ConcurrentForwarder {
	ctx, cancel := context.WithCancel(context.Background())
	
	// 根据CPU核心数确定工作协程数量
	workerCount := runtime.NumCPU() * 2
	if workerCount < 4 {
		workerCount = 4
	}
	if workerCount > 32 {
		workerCount = 32
	}

	queueSize := workerCount * 10 // 队列大小为工作协程数的10倍

	return &ConcurrentForwarder{
		server:        server,
		logger:        logger,
		ctx:           ctx,
		cancel:        cancel,
		workerCount:   workerCount,
		jobQueue:      make(chan *ForwardingJob, queueSize),
		queueSize:     queueSize,
		maxConcurrent: workerCount * 2,
		timeout:       time.Second * 10,
	}
}

// Start 启动并发转发器
func (cf *ConcurrentForwarder) Start() error {
	cf.logger.Info("Starting concurrent forwarder",
		zap.Int("worker_count", cf.workerCount),
		zap.Int("queue_size", cf.queueSize),
		zap.Int("max_concurrent", cf.maxConcurrent))

	// 启动工作协程
	for i := 0; i < cf.workerCount; i++ {
		cf.wg.Add(1)
		go cf.worker(i)
	}

	// 启动统计协程
	cf.wg.Add(1)
	go cf.statsWorker()

	return nil
}

// Stop 停止并发转发器
func (cf *ConcurrentForwarder) Stop() error {
	cf.logger.Info("Stopping concurrent forwarder")
	
	cf.cancel()
	close(cf.jobQueue)
	cf.wg.Wait()
	
	cf.logger.Info("Concurrent forwarder stopped")
	return nil
}

// SubmitJob 提交转发任务
func (cf *ConcurrentForwarder) SubmitJob(packet []byte, clientID, messageID string, client *Client) (*ForwardingResult, error) {
	// 检查队列是否已满
	if len(cf.jobQueue) >= cf.queueSize-1 {
		atomic.AddInt64(&cf.failedJobs, 1)
		return nil, fmt.Errorf("forwarding queue is full")
	}

	// 创建任务
	job := &ForwardingJob{
		ID:        fmt.Sprintf("job-%d-%s", time.Now().UnixNano(), clientID),
		Packet:    packet,
		ClientID:  clientID,
		MessageID: messageID,
		Client:    client,
		Timestamp: time.Now(),
		ResultCh:  make(chan *ForwardingResult, 1),
	}

	// 提交任务
	select {
	case cf.jobQueue <- job:
		atomic.AddInt64(&cf.totalJobs, 1)
		atomic.AddInt64(&cf.activeJobs, 1)
	case <-time.After(time.Millisecond * 100):
		atomic.AddInt64(&cf.failedJobs, 1)
		return nil, fmt.Errorf("failed to submit job: timeout")
	}

	// 等待结果
	select {
	case result := <-job.ResultCh:
		atomic.AddInt64(&cf.activeJobs, -1)
		if result.Error != nil {
			atomic.AddInt64(&cf.failedJobs, 1)
		} else {
			atomic.AddInt64(&cf.completedJobs, 1)
		}
		cf.updateDurationStats(result.Duration)
		return result, nil
	case <-time.After(cf.timeout):
		atomic.AddInt64(&cf.activeJobs, -1)
		atomic.AddInt64(&cf.failedJobs, 1)
		return nil, fmt.Errorf("job timeout after %v", cf.timeout)
	case <-cf.ctx.Done():
		atomic.AddInt64(&cf.activeJobs, -1)
		return nil, fmt.Errorf("forwarder is shutting down")
	}
}

// worker 工作协程
func (cf *ConcurrentForwarder) worker(workerID int) {
	defer cf.wg.Done()

	cf.logger.Debug("Worker started", zap.Int("worker_id", workerID))

	for {
		select {
		case job, ok := <-cf.jobQueue:
			if !ok {
				cf.logger.Debug("Worker stopping", zap.Int("worker_id", workerID))
				return
			}
			cf.processJob(job, workerID)
		case <-cf.ctx.Done():
			cf.logger.Debug("Worker stopping due to context cancellation", zap.Int("worker_id", workerID))
			return
		}
	}
}

// processJob 处理转发任务
func (cf *ConcurrentForwarder) processJob(job *ForwardingJob, workerID int) {
	startTime := time.Now()
	
	defer func() {
		if r := recover(); r != nil {
			cf.logger.Error("Panic in job processing",
				zap.Int("worker_id", workerID),
				zap.String("job_id", job.ID),
				zap.Any("panic", r))
			
			result := &ForwardingResult{
				JobID:    job.ID,
				Error:    fmt.Errorf("panic in job processing: %v", r),
				Duration: time.Since(startTime),
			}
			
			select {
			case job.ResultCh <- result:
			default:
			}
		}
	}()

	cf.logger.Debug("Processing job",
		zap.Int("worker_id", workerID),
		zap.String("job_id", job.ID),
		zap.String("client_id", job.ClientID))

	// 执行实际的数据包转发
	responsePacket, err := cf.server.forwardPacketWithResponse(job.Packet, job.ClientID)
	
	duration := time.Since(startTime)
	
	result := &ForwardingResult{
		JobID:        job.ID,
		ResponseData: responsePacket,
		Error:        err,
		Duration:     duration,
	}

	// 发送结果
	select {
	case job.ResultCh <- result:
		cf.logger.Debug("Job completed",
			zap.Int("worker_id", workerID),
			zap.String("job_id", job.ID),
			zap.Duration("duration", duration),
			zap.Bool("success", err == nil))
	default:
		cf.logger.Warn("Failed to send job result",
			zap.Int("worker_id", workerID),
			zap.String("job_id", job.ID))
	}
}

// statsWorker 统计信息工作协程
func (cf *ConcurrentForwarder) statsWorker() {
	defer cf.wg.Done()

	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			cf.logStats()
		case <-cf.ctx.Done():
			return
		}
	}
}

// logStats 记录统计信息
func (cf *ConcurrentForwarder) logStats() {
	total := atomic.LoadInt64(&cf.totalJobs)
	completed := atomic.LoadInt64(&cf.completedJobs)
	failed := atomic.LoadInt64(&cf.failedJobs)
	active := atomic.LoadInt64(&cf.activeJobs)

	cf.durationMutex.RLock()
	avgDuration := cf.avgDuration
	maxDuration := cf.maxDuration
	cf.durationMutex.RUnlock()

	queueLength := len(cf.jobQueue)
	
	cf.logger.Info("Concurrent forwarder stats",
		zap.Int64("total_jobs", total),
		zap.Int64("completed_jobs", completed),
		zap.Int64("failed_jobs", failed),
		zap.Int64("active_jobs", active),
		zap.Int("queue_length", queueLength),
		zap.Duration("avg_duration", avgDuration),
		zap.Duration("max_duration", maxDuration),
		zap.Float64("success_rate", float64(completed)/float64(total)*100))
}

// updateDurationStats 更新持续时间统计
func (cf *ConcurrentForwarder) updateDurationStats(duration time.Duration) {
	cf.durationMutex.Lock()
	defer cf.durationMutex.Unlock()

	if duration > cf.maxDuration {
		cf.maxDuration = duration
	}

	// 简单的移动平均
	if cf.avgDuration == 0 {
		cf.avgDuration = duration
	} else {
		cf.avgDuration = (cf.avgDuration*9 + duration) / 10
	}
}

// GetStats 获取统计信息
func (cf *ConcurrentForwarder) GetStats() map[string]interface{} {
	total := atomic.LoadInt64(&cf.totalJobs)
	completed := atomic.LoadInt64(&cf.completedJobs)
	failed := atomic.LoadInt64(&cf.failedJobs)
	active := atomic.LoadInt64(&cf.activeJobs)

	cf.durationMutex.RLock()
	avgDuration := cf.avgDuration
	maxDuration := cf.maxDuration
	cf.durationMutex.RUnlock()

	return map[string]interface{}{
		"total_jobs":     total,
		"completed_jobs": completed,
		"failed_jobs":    failed,
		"active_jobs":    active,
		"queue_length":   len(cf.jobQueue),
		"queue_capacity": cf.queueSize,
		"worker_count":   cf.workerCount,
		"avg_duration":   avgDuration.String(),
		"max_duration":   maxDuration.String(),
		"success_rate":   float64(completed) / float64(total) * 100,
	}
}
