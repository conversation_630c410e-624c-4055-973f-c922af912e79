package client

import (
	"net"
	"testing"
	"time"

	"cyber-bastion/pkg/config"
	"cyber-bastion/pkg/logger"
	"cyber-bastion/pkg/protocol"
)

func TestNewClient(t *testing.T) {
	cfg := &config.ClientConfig{
		ServerHost:        "localhost",
		ServerPort:        8080,
		ConnectTimeout:    10,
		HeartbeatInterval: 30,
		ReconnectDelay:    5,
		MaxReconnectDelay: 60,
		MaxReconnectTries: 10,
		AuthToken:         "test-token",
	}

	log, err := logger.NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	client := NewClient(cfg, log)

	if client.config != cfg {
		t.Error("Expected config to match")
	}
	if client.logger != log {
		t.Error("Expected logger to match")
	}
	if client.connected {
		t.Error("Expected client to be disconnected initially")
	}
	if client.authorized {
		t.Error("Expected client to be unauthorized initially")
	}
	if client.ctx == nil {
		t.Error("Expected context to be initialized")
	}
}

func TestClientIsConnected(t *testing.T) {
	cfg := createTestConfig()

	log, err := logger.NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	client := NewClient(cfg, log)

	if client.IsConnected() {
		t.Error("Expected client to be disconnected initially")
	}

	// Simulate connection
	client.mu.Lock()
	client.connected = true
	client.mu.Unlock()

	if !client.IsConnected() {
		t.Error("Expected client to be connected after setting")
	}
}

func TestClientIsAuthorized(t *testing.T) {
	cfg := &config.ClientConfig{
		ServerHost:        "localhost",
		ServerPort:        8080,
		ConnectTimeout:    10,
		HeartbeatInterval: 30,
		ReconnectDelay:    5,
		AuthToken:         "test-token",
	}

	log, err := logger.NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	client := NewClient(cfg, log)

	if client.IsAuthorized() {
		t.Error("Expected client to be unauthorized initially")
	}

	// Simulate authorization
	client.mu.Lock()
	client.authorized = true
	client.mu.Unlock()

	if !client.IsAuthorized() {
		t.Error("Expected client to be authorized after setting")
	}
}

func TestClientSendDataNotConnected(t *testing.T) {
	cfg := &config.ClientConfig{
		ServerHost:        "localhost",
		ServerPort:        8080,
		ConnectTimeout:    10,
		HeartbeatInterval: 30,
		ReconnectDelay:    5,
		AuthToken:         "test-token",
	}

	log, err := logger.NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	client := NewClient(cfg, log)

	// Try to send data when not connected
	err = client.SendData([]byte("test data"))
	if err == nil {
		t.Error("Expected error when sending data while not connected")
	}
}

func TestClientSendDataNotAuthorized(t *testing.T) {
	cfg := &config.ClientConfig{
		ServerHost:        "localhost",
		ServerPort:        8080,
		ConnectTimeout:    10,
		HeartbeatInterval: 30,
		ReconnectDelay:    5,
		AuthToken:         "test-token",
	}

	log, err := logger.NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	client := NewClient(cfg, log)

	// Simulate connection but not authorization
	server, clientConn := net.Pipe()
	defer server.Close()
	defer clientConn.Close()

	client.mu.Lock()
	client.conn = clientConn
	client.connected = true
	client.authorized = false
	client.mu.Unlock()

	// Try to send data when not authorized
	err = client.SendData([]byte("test data"))
	if err == nil {
		t.Error("Expected error when sending data while not authorized")
	}
}

func TestClientSendDataSuccess(t *testing.T) {
	cfg := &config.ClientConfig{
		ServerHost:        "localhost",
		ServerPort:        8080,
		ConnectTimeout:    10,
		HeartbeatInterval: 30,
		ReconnectDelay:    5,
		AuthToken:         "test-token",
	}

	log, err := logger.NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	client := NewClient(cfg, log)

	// Create mock connection
	server, clientConn := net.Pipe()
	defer server.Close()
	defer clientConn.Close()

	// Simulate connected and authorized state
	client.mu.Lock()
	client.conn = clientConn
	client.connected = true
	client.authorized = true
	client.mu.Unlock()

	// Send data in goroutine
	done := make(chan error, 1)
	go func() {
		done <- client.SendData([]byte("test data"))
	}()

	// Read the message on server side
	go func() {
		_, err := protocol.ReceiveMessage(server)
		if err != nil {
			t.Logf("Error receiving message: %v", err)
		}
	}()

	select {
	case err := <-done:
		if err != nil {
			t.Errorf("Expected successful data send, got error: %v", err)
		}
	case <-time.After(1 * time.Second):
		t.Error("Send data timed out")
	}
}

func TestClientHandleMessage(t *testing.T) {
	cfg := &config.ClientConfig{
		ServerHost:        "localhost",
		ServerPort:        8080,
		ConnectTimeout:    10,
		HeartbeatInterval: 30,
		ReconnectDelay:    5,
		AuthToken:         "test-token",
	}

	log, err := logger.NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	client := NewClient(cfg, log)

	testCases := []struct {
		name string
		msg  *protocol.Message
	}{
		{
			name: "Response Message",
			msg:  protocol.NewResponse("test-123", []byte("response data")),
		},
		{
			name: "Error Message",
			msg:  protocol.NewError("test-123", &testError{"test error"}),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// This should not panic or error
			client.handleMessage(tc.msg)
		})
	}
}

func TestClientStop(t *testing.T) {
	cfg := &config.ClientConfig{
		ServerHost:        "localhost",
		ServerPort:        8080,
		ConnectTimeout:    10,
		HeartbeatInterval: 30,
		ReconnectDelay:    5,
		AuthToken:         "test-token",
	}

	log, err := logger.NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	client := NewClient(cfg, log)

	// Test stopping client without starting
	err = client.Stop()
	if err != nil {
		t.Errorf("Expected successful stop, got error: %v", err)
	}

	// Verify state after stop
	if client.IsConnected() {
		t.Error("Expected client to be disconnected after stop")
	}
	if client.IsAuthorized() {
		t.Error("Expected client to be unauthorized after stop")
	}
}

func TestClientHandleDisconnection(t *testing.T) {
	cfg := &config.ClientConfig{
		ServerHost:        "localhost",
		ServerPort:        8080,
		ConnectTimeout:    10,
		HeartbeatInterval: 30,
		ReconnectDelay:    1, // Short delay for testing
		MaxReconnectDelay: 60,
		MaxReconnectTries: 10,
		AuthToken:         "test-token",
	}

	log, err := logger.NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	client := NewClient(cfg, log)

	// Create mock connection
	server, clientConn := net.Pipe()
	defer server.Close()

	// Simulate connected and authorized state
	client.mu.Lock()
	client.conn = clientConn
	client.connected = true
	client.authorized = true
	client.mu.Unlock()

	// Close the connection to simulate disconnection
	clientConn.Close()

	// Handle disconnection
	client.handleDisconnection()

	// Verify state after disconnection
	if client.IsConnected() {
		t.Error("Expected client to be disconnected after handling disconnection")
	}
	if client.IsAuthorized() {
		t.Error("Expected client to be unauthorized after handling disconnection")
	}
}

func TestClientSendHeartbeat(t *testing.T) {
	cfg := &config.ClientConfig{
		ServerHost:        "localhost",
		ServerPort:        8080,
		ConnectTimeout:    10,
		HeartbeatInterval: 30,
		ReconnectDelay:    5,
		MaxReconnectDelay: 60,
		MaxReconnectTries: 10,
		AuthToken:         "test-token",
	}

	log, err := logger.NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	client := NewClient(cfg, log)

	// Test sending heartbeat when not connected
	err = client.sendHeartbeat()
	if err == nil {
		t.Error("Expected error when sending heartbeat while not connected")
	}

	// Create mock connection
	server, clientConn := net.Pipe()
	defer server.Close()
	defer clientConn.Close()

	// Simulate connected state
	client.mu.Lock()
	client.conn = clientConn
	client.connected = true
	client.mu.Unlock()

	// Send heartbeat in goroutine
	done := make(chan error, 1)
	go func() {
		done <- client.sendHeartbeat()
	}()

	// Start a goroutine to handle the message exchange
	go func() {
		// Read the heartbeat message on server side
		msg, err := protocol.ReceiveMessage(server)
		if err != nil {
			t.Logf("Error receiving heartbeat: %v", err)
			return
		}
		if msg.Type != protocol.MessageTypeHeartbeat {
			t.Errorf("Expected heartbeat message, got type %d", msg.Type)
			return
		}

		// Send heartbeat response
		response := protocol.NewResponse(msg.ID, []byte("pong"))
		if err := protocol.SendMessage(server, response); err != nil {
			t.Logf("Error sending heartbeat response: %v", err)
			return
		}
	}()

	// Start a goroutine to handle the response on client side
	go func() {
		// Read the response message
		response, err := protocol.ReceiveMessage(clientConn)
		if err != nil {
			t.Logf("Error receiving response: %v", err)
			return
		}
		// Handle the response message
		client.handleMessage(response)
	}()

	select {
	case err := <-done:
		if err != nil {
			t.Errorf("Expected successful heartbeat send, got error: %v", err)
		}
	case <-time.After(6 * time.Second): // Increased timeout to account for heartbeat response wait
		t.Error("Send heartbeat timed out")
	}
}

// Mock authentication test
func TestClientAuthenticate(t *testing.T) {
	cfg := &config.ClientConfig{
		ServerHost:        "localhost",
		ServerPort:        8080,
		ConnectTimeout:    10,
		HeartbeatInterval: 30,
		ReconnectDelay:    5,
		AuthToken:         "test-token",
	}

	log, err := logger.NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	client := NewClient(cfg, log)

	// Test authenticate when not connected
	err = client.authenticate()
	if err == nil {
		t.Error("Expected error when authenticating while not connected")
	}

	// Create mock connection
	server, clientConn := net.Pipe()
	defer server.Close()
	defer clientConn.Close()

	// Simulate connected state
	client.mu.Lock()
	client.conn = clientConn
	client.mu.Unlock()

	// Test successful authentication
	go func() {
		// Read auth message
		msg, err := protocol.ReceiveMessage(server)
		if err != nil {
			t.Logf("Error receiving auth message: %v", err)
			return
		}
		if msg.Type != protocol.MessageTypeAuth {
			t.Errorf("Expected auth message, got type %d", msg.Type)
			return
		}

		// Send success response
		response := protocol.NewResponse(msg.ID, []byte("authenticated"))
		protocol.SendMessage(server, response)
	}()

	err = client.authenticate()
	if err != nil {
		t.Errorf("Expected successful authentication, got error: %v", err)
	}

	if !client.IsAuthorized() {
		t.Error("Expected client to be authorized after successful authentication")
	}
}

// Helper function to create test config
func createTestConfig() *config.ClientConfig {
	return &config.ClientConfig{
		ServerHost:        "localhost",
		ServerPort:        8080,
		ConnectTimeout:    10,
		HeartbeatInterval: 30,
		ReconnectDelay:    5,
		MaxReconnectDelay: 60,
		MaxReconnectTries: 10,
		AuthToken:         "test-token",
	}
}

// Helper types for testing
type testError struct {
	message string
}

func (e *testError) Error() string {
	return e.message
}
