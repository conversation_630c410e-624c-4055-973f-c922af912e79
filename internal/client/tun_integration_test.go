package client

import (
	"testing"

	"cyber-bastion/pkg/config"
	"cyber-bastion/pkg/protocol"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap/zaptest"
)

// TestClientTunIntegration 测试客户端TUN接口集成
func TestClientTunIntegration(t *testing.T) {
	logger := zaptest.NewLogger(t)

	// 创建测试配置（不启用TUN接口，避免需要root权限）
	cfg := &config.ClientConfig{
		ServerHost:     "127.0.0.1",
		ServerPort:     0, // 不实际连接
		ConnectTimeout: 5,
		Tun: &config.TunConfig{
			Enabled: false, // 在测试中禁用实际TUN接口
			Name:    "test-tun",
			IP:      "********",
			Netmask: "*************",
			MTU:     1500,
		},
	}

	// 创建客户端
	client := NewClient(cfg, logger)
	require.NotNil(t, client)

	t.Run("客户端创建和配置", func(t *testing.T) {
		assert.Equal(t, cfg, client.config)
		assert.Equal(t, logger, client.logger)
		assert.False(t, client.connected)
		assert.False(t, client.authorized)
		assert.False(t, client.tunEnabled)
	})

	t.Run("TUN数据包缓存功能", func(t *testing.T) {
		// 启用数据包缓存
		client.bufferEnabled = true
		client.maxBufferSize = 10

		// 创建测试数据包
		testPacket := []byte{0x45, 0x00, 0x00, 0x1C} // IPv4头部开始

		// 缓存数据包
		client.bufferPacket(testPacket)

		// 验证缓存
		bufferSize := client.getBufferSize()
		assert.Equal(t, 1, bufferSize)

		// 清理缓存
		client.clearPacketBuffer()
		bufferSize = client.getBufferSize()
		assert.Equal(t, 0, bufferSize)
	})
}

// TestTunResponseHandling 测试TUN响应处理
func TestTunResponseHandling(t *testing.T) {
	logger := zaptest.NewLogger(t)

	cfg := &config.ClientConfig{
		ServerHost: "127.0.0.1",
		ServerPort: 0,
		Tun: &config.TunConfig{
			Enabled: false, // 禁用实际TUN接口
		},
	}

	client := NewClient(cfg, logger)
	require.NotNil(t, client)

	t.Run("TUN接口未启用时的响应处理", func(t *testing.T) {
		// 创建TUN响应消息
		responseData := buildTestIPv4ICMPResponsePacket()
		msg := protocol.NewTunResponseMessage("test-response", responseData)

		// 处理响应（应该被忽略，因为TUN接口未启用）
		client.handleTunResponse(msg)

		// 验证没有错误（函数应该正常返回）
		assert.True(t, true) // 如果到达这里说明没有panic
	})

	t.Run("大数据包响应处理", func(t *testing.T) {
		// 创建大的响应数据包（8KB）
		largeResponseData := make([]byte, 8192)
		// 填充IPv4头部
		largeResponseData[0] = 0x45 // 版本 + 头部长度
		largeResponseData[9] = 0x01 // 协议 (ICMP)

		msg := protocol.NewTunResponseMessage("test-large-response", largeResponseData)

		// 处理大数据包响应
		client.handleTunResponse(msg)

		// 验证没有错误
		assert.True(t, true)
	})

	t.Run("超大数据包响应处理", func(t *testing.T) {
		// 创建超大的响应数据包（10KB，超过8KB限制）
		oversizedResponseData := make([]byte, 10240)
		// 填充IPv4头部
		oversizedResponseData[0] = 0x45 // 版本 + 头部长度
		oversizedResponseData[9] = 0x01 // 协议 (ICMP)

		msg := protocol.NewTunResponseMessage("test-oversized-response", oversizedResponseData)

		// 处理超大数据包响应（应该记录警告但不崩溃）
		client.handleTunResponse(msg)

		// 验证没有错误
		assert.True(t, true)
	})
}

// TestTunDataSending 测试TUN数据发送
func TestTunDataSending(t *testing.T) {
	logger := zaptest.NewLogger(t)

	cfg := &config.ClientConfig{
		ServerHost: "127.0.0.1",
		ServerPort: 0,
	}

	client := NewClient(cfg, logger)
	require.NotNil(t, client)

	t.Run("未连接时的数据发送", func(t *testing.T) {
		// 确保缓存被禁用
		client.bufferEnabled = false

		testPacket := buildTestIPv4ICMPPacket()

		// 尝试发送数据（应该失败，因为未连接且未启用缓存）
		err := client.SendTunData(testPacket)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "not connected")
	})

	t.Run("启用缓存时的数据发送", func(t *testing.T) {
		// 启用缓存
		client.bufferEnabled = true
		client.maxBufferSize = 10

		testPacket := buildTestIPv4ICMPPacket()

		// 发送数据（应该被缓存）
		err := client.SendTunData(testPacket)
		assert.NoError(t, err) // 缓存模式下不应该返回错误

		// 验证数据包被缓存
		bufferSize := client.getBufferSize()
		assert.Equal(t, 1, bufferSize)
	})
}

// TestConnectionStats 测试连接统计
func TestConnectionStats(t *testing.T) {
	logger := zaptest.NewLogger(t)

	cfg := &config.ClientConfig{
		ServerHost: "127.0.0.1",
		ServerPort: 0,
	}

	client := NewClient(cfg, logger)
	require.NotNil(t, client)

	t.Run("初始统计信息", func(t *testing.T) {
		stats := client.GetConnectionStats()
		assert.Equal(t, int64(0), stats.MessagesSent)
		assert.Equal(t, int64(0), stats.MessagesReceived)
		assert.Equal(t, 0, stats.HeartbeatFailures)
		assert.Equal(t, 0, stats.ConsecutiveFailures)
	})

	t.Run("更新统计信息", func(t *testing.T) {
		// 模拟发送消息
		client.updateMessageSent()
		client.updateMessageSent()

		// 模拟接收消息
		client.updateMessageReceived()

		stats := client.GetConnectionStats()
		assert.Equal(t, int64(2), stats.MessagesSent)
		assert.Equal(t, int64(1), stats.MessagesReceived)
	})
}

// buildTestIPv4ICMPPacket 构造测试用的IPv4 ICMP数据包
func buildTestIPv4ICMPPacket() []byte {
	// IPv4头部 (20字节) + ICMP头部 (8字节)
	packet := make([]byte, 28)

	// IPv4头部
	packet[0] = 0x45 // 版本(4) + 头部长度(5*4=20字节)
	packet[1] = 0x00 // 服务类型
	packet[2] = 0x00 // 总长度高字节
	packet[3] = 0x1C // 总长度低字节 (28字节)
	packet[9] = 0x01 // 协议 (1 = ICMP)
	// 源IP: ********
	packet[12] = 10
	packet[13] = 1
	packet[14] = 0
	packet[15] = 2
	// 目标IP: *******
	packet[16] = 8
	packet[17] = 8
	packet[18] = 8
	packet[19] = 8

	// ICMP头部 (Echo Request)
	packet[20] = 0x08 // 类型 (8 = Echo Request)
	packet[21] = 0x00 // 代码

	return packet
}

// buildTestIPv4ICMPResponsePacket 构造测试用的IPv4 ICMP响应数据包
func buildTestIPv4ICMPResponsePacket() []byte {
	// IPv4头部 (20字节) + ICMP头部 (8字节)
	packet := make([]byte, 28)

	// IPv4头部
	packet[0] = 0x45 // 版本(4) + 头部长度(5*4=20字节)
	packet[1] = 0x00 // 服务类型
	packet[2] = 0x00 // 总长度高字节
	packet[3] = 0x1C // 总长度低字节 (28字节)
	packet[9] = 0x01 // 协议 (1 = ICMP)
	// 源IP: ******* (响应包的源IP是原请求的目标IP)
	packet[12] = 8
	packet[13] = 8
	packet[14] = 8
	packet[15] = 8
	// 目标IP: ******** (响应包的目标IP是原请求的源IP)
	packet[16] = 10
	packet[17] = 1
	packet[18] = 0
	packet[19] = 2

	// ICMP头部 (Echo Reply)
	packet[20] = 0x00 // 类型 (0 = Echo Reply)
	packet[21] = 0x00 // 代码

	return packet
}

// TestPacketBufferManagement 测试数据包缓存管理
func TestPacketBufferManagement(t *testing.T) {
	logger := zaptest.NewLogger(t)

	cfg := &config.ClientConfig{
		ServerHost: "127.0.0.1",
		ServerPort: 0,
	}

	client := NewClient(cfg, logger)
	require.NotNil(t, client)

	// 启用缓存
	client.bufferEnabled = true
	client.maxBufferSize = 3 // 小的缓存大小用于测试

	t.Run("缓存溢出处理", func(t *testing.T) {
		// 添加数据包直到缓存满
		for i := 0; i < 5; i++ {
			packet := make([]byte, 28)
			packet[0] = byte(i) // 用于区分不同的数据包
			client.bufferPacket(packet)
		}

		// 验证缓存大小不超过最大值
		bufferSize := client.getBufferSize()
		assert.Equal(t, 3, bufferSize)
	})

	t.Run("缓存清理", func(t *testing.T) {
		// 添加一些数据包
		for i := 0; i < 2; i++ {
			packet := make([]byte, 28)
			client.bufferPacket(packet)
		}

		// 清理缓存
		client.clearPacketBuffer()

		// 验证缓存已清空
		bufferSize := client.getBufferSize()
		assert.Equal(t, 0, bufferSize)
	})
}
