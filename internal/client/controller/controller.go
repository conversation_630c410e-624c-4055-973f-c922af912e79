package controller

import (
	"context"
	"fmt"
	"sync"
	"time"

	"cyber-bastion/pkg/config"
	"cyber-bastion/pkg/tun"

	"go.uber.org/zap"
)

// DefaultController 默认控制器实现
type DefaultController struct {
	config *config.ClientConfig
	logger *zap.Logger
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
	mu     sync.RWMutex

	// 组件
	tunnelManager TunnelManager
	router        Router
	forwarder     Forwarder
	tunInterface  *tun.TunInterface

	// 状态
	state     string
	startTime time.Time
	lastError string

	// 指标
	metrics *ControllerMetrics
}

// ControllerMetrics 控制器指标
type ControllerMetrics struct {
	StartTime       time.Time     `json:"start_time"`
	Uptime          time.Duration `json:"uptime"`
	RestartCount    int64         `json:"restart_count"`
	ErrorCount      int64         `json:"error_count"`
	LastHealthCheck time.Time     `json:"last_health_check"`
	mu              sync.RWMutex
}

// NewController 创建新的控制器
func NewController(cfg *config.ClientConfig, logger *zap.Logger) (Controller, error) {
	ctx, cancel := context.WithCancel(context.Background())

	controller := &DefaultController{
		config:  cfg,
		logger:  logger,
		ctx:     ctx,
		cancel:  cancel,
		state:   "stopped",
		metrics: &ControllerMetrics{},
	}

	// 创建隧道管理器
	tunnelManager, err := NewTunnelManager(cfg, logger)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("failed to create tunnel manager: %w", err)
	}
	controller.tunnelManager = tunnelManager

	// 创建路由器
	router, err := NewRouter(cfg.Routing, logger)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("failed to create router: %w", err)
	}
	controller.router = router

	// 创建转发器
	forwarder, err := NewForwarder(cfg, tunnelManager, router, logger)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("failed to create forwarder: %w", err)
	}
	controller.forwarder = forwarder

	return controller, nil
}

// Start 启动控制器
func (c *DefaultController) Start(ctx context.Context) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.state == "running" {
		return fmt.Errorf("controller is already running")
	}

	c.logger.Info("Starting client controller")
	c.state = "starting"
	c.startTime = time.Now()
	c.metrics.StartTime = c.startTime

	// 初始化TUN接口（如果启用）
	if c.config.Tun != nil && c.config.Tun.Enabled {
		if err := c.initTunInterface(); err != nil {
			c.state = "error"
			c.lastError = err.Error()
			return fmt.Errorf("failed to initialize TUN interface: %w", err)
		}
	}

	// 启动隧道管理器
	if err := c.tunnelManager.Start(c.ctx); err != nil {
		c.state = "error"
		c.lastError = err.Error()
		return fmt.Errorf("failed to start tunnel manager: %w", err)
	}

	// 启动路由器
	if err := c.router.Start(c.ctx); err != nil {
		c.state = "error"
		c.lastError = err.Error()
		return fmt.Errorf("failed to start router: %w", err)
	}

	// 启动转发器
	if err := c.forwarder.Start(c.ctx); err != nil {
		c.state = "error"
		c.lastError = err.Error()
		return fmt.Errorf("failed to start forwarder: %w", err)
	}

	// 启动健康检查
	c.wg.Add(1)
	go c.healthCheckLoop()

	// 启动TUN数据处理（如果启用）
	if c.tunInterface != nil {
		c.wg.Add(1)
		go c.tunDataLoop()
	}

	c.state = "running"
	c.logger.Info("Client controller started successfully")

	return nil
}

// Stop 停止控制器
func (c *DefaultController) Stop() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.state == "stopped" {
		return nil
	}

	c.logger.Info("Stopping client controller")
	c.state = "stopping"

	// 取消上下文
	c.cancel()

	// 停止转发器
	if c.forwarder != nil {
		if err := c.forwarder.Stop(); err != nil {
			c.logger.Error("Failed to stop forwarder", zap.Error(err))
		}
	}

	// 停止路由器
	if c.router != nil {
		if err := c.router.Stop(); err != nil {
			c.logger.Error("Failed to stop router", zap.Error(err))
		}
	}

	// 停止隧道管理器
	if c.tunnelManager != nil {
		if err := c.tunnelManager.Stop(); err != nil {
			c.logger.Error("Failed to stop tunnel manager", zap.Error(err))
		}
	}

	// 停止TUN接口
	if c.tunInterface != nil {
		if err := c.tunInterface.Stop(); err != nil {
			c.logger.Error("Failed to stop TUN interface", zap.Error(err))
		}
	}

	// 等待所有协程完成
	c.wg.Wait()

	c.state = "stopped"
	c.logger.Info("Client controller stopped")

	return nil
}

// GetStatus 获取控制器状态
func (c *DefaultController) GetStatus() *ControllerStatus {
	c.mu.RLock()
	defer c.mu.RUnlock()

	var tunnelCount, activeTunnels int
	if c.tunnelManager != nil {
		tunnels := c.tunnelManager.GetTunnels()
		tunnelCount = len(tunnels)
		activeTunnels = len(c.tunnelManager.GetActiveTunnels())
	}

	return &ControllerStatus{
		State:         c.state,
		StartTime:     c.startTime,
		Uptime:        time.Since(c.startTime),
		TunnelCount:   tunnelCount,
		ActiveTunnels: activeTunnels,
		LastError:     c.lastError,
	}
}

// GetTunnelManager 获取隧道管理器
func (c *DefaultController) GetTunnelManager() TunnelManager {
	return c.tunnelManager
}

// GetRouter 获取路由器
func (c *DefaultController) GetRouter() Router {
	return c.router
}

// GetForwarder 获取转发器
func (c *DefaultController) GetForwarder() Forwarder {
	return c.forwarder
}

// initTunInterface 初始化TUN接口
func (c *DefaultController) initTunInterface() error {
	// 创建TUN配置
	tunConfig := &tun.Config{
		Name:    c.config.Tun.Name,
		IP:      c.config.Tun.IP,
		Netmask: c.config.Tun.Netmask,
		MTU:     c.config.Tun.MTU,
		Enabled: c.config.Tun.Enabled,
	}

	// 创建TUN接口
	tunInterface, err := tun.NewTunInterface(tunConfig, c.logger)
	if err != nil {
		return fmt.Errorf("failed to create TUN interface: %w", err)
	}

	// 启动TUN接口
	if err := tunInterface.Start(); err != nil {
		return fmt.Errorf("failed to start TUN interface: %w", err)
	}

	c.tunInterface = tunInterface

	c.logger.Info("TUN interface initialized successfully",
		zap.String("name", tunInterface.Name()),
		zap.String("ip", c.config.Tun.IP),
		zap.Int("mtu", c.config.Tun.MTU))

	return nil
}

// healthCheckLoop 健康检查循环
func (c *DefaultController) healthCheckLoop() {
	defer c.wg.Done()

	if c.config.Controller == nil {
		return
	}

	interval := time.Duration(c.config.Controller.HealthCheckInterval) * time.Second
	if interval <= 0 {
		interval = 30 * time.Second
	}

	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			c.performHealthCheck()
		}
	}
}

// performHealthCheck 执行健康检查
func (c *DefaultController) performHealthCheck() {
	c.metrics.mu.Lock()
	c.metrics.LastHealthCheck = time.Now()
	c.metrics.mu.Unlock()

	// 检查隧道管理器状态
	if c.tunnelManager != nil {
		activeTunnels := c.tunnelManager.GetActiveTunnels()
		if len(activeTunnels) == 0 {
			c.logger.Warn("No active tunnels available")
		}
	}

	c.logger.Debug("Health check completed")
}

// tunDataLoop TUN数据处理循环
func (c *DefaultController) tunDataLoop() {
	defer c.wg.Done()

	if c.tunInterface == nil {
		return
	}

	c.logger.Info("Starting TUN data processing loop")

	for {
		select {
		case <-c.ctx.Done():
			return
		default:
			// 读取TUN数据包
			buffer := make([]byte, 1500) // MTU大小的缓冲区
			n, err := c.tunInterface.Read(buffer)
			if err != nil {
				if c.ctx.Err() != nil {
					return
				}
				c.logger.Error("Failed to read from TUN interface", zap.Error(err))
				continue
			}

			// 转发数据包
			packet := buffer[:n]
			if err := c.forwarder.Forward(packet, ""); err != nil {
				c.logger.Error("Failed to forward packet", zap.Error(err))
			}
		}
	}
}
