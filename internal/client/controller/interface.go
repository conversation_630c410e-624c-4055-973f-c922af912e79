package controller

import (
	"context"
	"time"

	"cyber-bastion/pkg/config"
)

// Controller 客户端控制器接口
type Controller interface {
	// Start 启动控制器
	Start(ctx context.Context) error
	
	// Stop 停止控制器
	Stop() error
	
	// GetStatus 获取控制器状态
	GetStatus() *ControllerStatus
	
	// GetTunnelManager 获取隧道管理器
	GetTunnelManager() TunnelManager
	
	// GetRouter 获取路由器
	GetRouter() Router
	
	// GetForwarder 获取转发器
	GetForwarder() Forwarder
}

// TunnelManager 隧道管理器接口
type TunnelManager interface {
	// Start 启动隧道管理器
	Start(ctx context.Context) error
	
	// Stop 停止隧道管理器
	Stop() error
	
	// GetTunnels 获取所有隧道
	GetTunnels() map[string]Tunnel
	
	// GetActiveTunnels 获取活跃隧道
	GetActiveTunnels() []Tunnel
	
	// GetTunnel 根据名称获取隧道
	GetTunnel(name string) (Tunnel, bool)
	
	// SelectTunnel 根据策略选择隧道
	SelectTunnel(destination string) (Tunnel, error)
	
	// GetMetrics 获取隧道指标
	GetMetrics() *TunnelMetrics
}

// Tunnel 隧道接口
type Tunnel interface {
	// GetName 获取隧道名称
	GetName() string
	
	// GetConfig 获取隧道配置
	GetConfig() *config.TunnelConfig
	
	// Connect 连接隧道
	Connect() error
	
	// Disconnect 断开隧道
	Disconnect() error
	
	// IsConnected 检查是否已连接
	IsConnected() bool
	
	// Send 发送数据
	Send(data []byte) error
	
	// Receive 接收数据
	Receive() ([]byte, error)
	
	// GetStatus 获取隧道状态
	GetStatus() *TunnelStatus
	
	// GetMetrics 获取隧道指标
	GetMetrics() *TunnelMetrics
}

// Router 路由器接口
type Router interface {
	// Start 启动路由器
	Start(ctx context.Context) error
	
	// Stop 停止路由器
	Stop() error
	
	// Route 路由决策
	Route(destination string) (*RoutingDecision, error)
	
	// UpdateRules 更新路由规则
	UpdateRules(rules []*config.RoutingRule) error
	
	// GetRules 获取路由规则
	GetRules() []*config.RoutingRule
	
	// GetMetrics 获取路由指标
	GetMetrics() *RoutingMetrics
}

// Forwarder 转发器接口
type Forwarder interface {
	// Start 启动转发器
	Start(ctx context.Context) error
	
	// Stop 停止转发器
	Stop() error
	
	// Forward 转发数据包
	Forward(packet []byte, destination string) error
	
	// GetMetrics 获取转发指标
	GetMetrics() *ForwardingMetrics
}

// ControllerStatus 控制器状态
type ControllerStatus struct {
	State       string    `json:"state"`
	StartTime   time.Time `json:"start_time"`
	Uptime      time.Duration `json:"uptime"`
	TunnelCount int       `json:"tunnel_count"`
	ActiveTunnels int     `json:"active_tunnels"`
	LastError   string    `json:"last_error,omitempty"`
}

// TunnelStatus 隧道状态
type TunnelStatus struct {
	Name        string        `json:"name"`
	State       string        `json:"state"`
	Connected   bool          `json:"connected"`
	LastConnect time.Time     `json:"last_connect"`
	LastError   string        `json:"last_error,omitempty"`
	Latency     time.Duration `json:"latency"`
	BytesSent   int64         `json:"bytes_sent"`
	BytesRecv   int64         `json:"bytes_received"`
}

// TunnelMetrics 隧道指标
type TunnelMetrics struct {
	TotalConnections    int64         `json:"total_connections"`
	ActiveConnections   int           `json:"active_connections"`
	FailedConnections   int64         `json:"failed_connections"`
	BytesSent           int64         `json:"bytes_sent"`
	BytesReceived       int64         `json:"bytes_received"`
	PacketsSent         int64         `json:"packets_sent"`
	PacketsReceived     int64         `json:"packets_received"`
	AverageLatency      time.Duration `json:"average_latency"`
	ConnectionErrors    int64         `json:"connection_errors"`
	LastConnectionTime  time.Time     `json:"last_connection_time"`
}

// RoutingDecision 路由决策
type RoutingDecision struct {
	Action      string `json:"action"`      // tunnel, direct, block
	TunnelName  string `json:"tunnel_name,omitempty"`
	Rule        string `json:"rule"`
	Destination string `json:"destination"`
}

// RoutingMetrics 路由指标
type RoutingMetrics struct {
	TotalRequests    int64            `json:"total_requests"`
	TunnelRequests   int64            `json:"tunnel_requests"`
	DirectRequests   int64            `json:"direct_requests"`
	BlockedRequests  int64            `json:"blocked_requests"`
	RuleHits         map[string]int64 `json:"rule_hits"`
	LastRequestTime  time.Time        `json:"last_request_time"`
}

// ForwardingMetrics 转发指标
type ForwardingMetrics struct {
	PacketsForwarded int64         `json:"packets_forwarded"`
	PacketsDropped   int64         `json:"packets_dropped"`
	BytesForwarded   int64         `json:"bytes_forwarded"`
	ForwardingErrors int64         `json:"forwarding_errors"`
	AverageLatency   time.Duration `json:"average_latency"`
	LastForwardTime  time.Time     `json:"last_forward_time"`
}
