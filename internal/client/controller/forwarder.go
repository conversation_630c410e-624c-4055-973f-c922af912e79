package controller

import (
	"context"
	"fmt"
	"net"
	"sync"
	"time"

	"cyber-bastion/pkg/config"

	"go.uber.org/zap"
)

// DefaultForwarder 默认转发器实现
type DefaultForwarder struct {
	config        *config.ClientConfig
	tunnelManager TunnelManager
	router        Router
	logger        *zap.Logger
	ctx           context.Context
	cancel        context.CancelFunc
	mu            sync.RWMutex
	
	metrics *ForwardingMetrics
}

// NewForwarder 创建新的转发器
func NewForwarder(cfg *config.ClientConfig, tunnelManager TunnelManager, router Router, logger *zap.Logger) (Forwarder, error) {
	ctx, cancel := context.WithCancel(context.Background())
	
	forwarder := &DefaultForwarder{
		config:        cfg,
		tunnelManager: tunnelManager,
		router:        router,
		logger:        logger,
		ctx:           ctx,
		cancel:        cancel,
		metrics:       &ForwardingMetrics{},
	}
	
	return forwarder, nil
}

// Start 启动转发器
func (f *DefaultForwarder) Start(ctx context.Context) error {
	f.mu.Lock()
	defer f.mu.Unlock()
	
	f.logger.Info("Starting forwarder")
	
	f.logger.Info("Forwarder started successfully")
	return nil
}

// Stop 停止转发器
func (f *DefaultForwarder) Stop() error {
	f.mu.Lock()
	defer f.mu.Unlock()
	
	f.logger.Info("Stopping forwarder")
	f.cancel()
	
	f.logger.Info("Forwarder stopped")
	return nil
}

// Forward 转发数据包
func (f *DefaultForwarder) Forward(packet []byte, destination string) error {
	startTime := time.Now()
	
	// 解析数据包获取目标地址
	if destination == "" {
		var err error
		destination, err = f.extractDestination(packet)
		if err != nil {
			f.updateMetrics(false, len(packet), time.Since(startTime))
			return fmt.Errorf("failed to extract destination: %w", err)
		}
	}
	
	// 路由决策
	decision, err := f.router.Route(destination)
	if err != nil {
		f.updateMetrics(false, len(packet), time.Since(startTime))
		return fmt.Errorf("routing failed: %w", err)
	}
	
	// 根据决策处理数据包
	switch decision.Action {
	case "tunnel":
		err = f.forwardThroughTunnel(packet, decision)
	case "direct":
		err = f.forwardDirect(packet, destination)
	case "block":
		f.logger.Debug("Packet blocked by routing rule",
			zap.String("destination", destination),
			zap.String("rule", decision.Rule))
		return nil
	default:
		err = fmt.Errorf("unknown action: %s", decision.Action)
	}
	
	if err != nil {
		f.updateMetrics(false, len(packet), time.Since(startTime))
		return err
	}
	
	f.updateMetrics(true, len(packet), time.Since(startTime))
	return nil
}

// GetMetrics 获取转发指标
func (f *DefaultForwarder) GetMetrics() *ForwardingMetrics {
	f.mu.RLock()
	defer f.mu.RUnlock()
	
	// 复制指标
	metrics := *f.metrics
	return &metrics
}

// extractDestination 从数据包中提取目标地址
func (f *DefaultForwarder) extractDestination(packet []byte) (string, error) {
	if len(packet) < 20 {
		return "", fmt.Errorf("packet too short")
	}
	
	// 解析IP头部
	version := packet[0] >> 4
	switch version {
	case 4:
		if len(packet) < 20 {
			return "", fmt.Errorf("IPv4 packet too short")
		}
		dstIP := net.IP(packet[16:20])
		return dstIP.String(), nil
		
	case 6:
		if len(packet) < 40 {
			return "", fmt.Errorf("IPv6 packet too short")
		}
		dstIP := net.IP(packet[24:40])
		return dstIP.String(), nil
		
	default:
		return "", fmt.Errorf("unsupported IP version: %d", version)
	}
}

// forwardThroughTunnel 通过隧道转发
func (f *DefaultForwarder) forwardThroughTunnel(packet []byte, decision *RoutingDecision) error {
	var tunnel Tunnel
	var err error
	
	if decision.TunnelName != "" {
		// 使用指定的隧道
		tunnel, exists := f.tunnelManager.GetTunnel(decision.TunnelName)
		if !exists {
			return fmt.Errorf("tunnel not found: %s", decision.TunnelName)
		}
		if !tunnel.IsConnected() {
			return fmt.Errorf("tunnel not connected: %s", decision.TunnelName)
		}
	} else {
		// 让隧道管理器选择隧道
		tunnel, err = f.tunnelManager.SelectTunnel(decision.Destination)
		if err != nil {
			return fmt.Errorf("failed to select tunnel: %w", err)
		}
	}
	
	// 发送数据包
	if err := tunnel.Send(packet); err != nil {
		return fmt.Errorf("failed to send through tunnel %s: %w", tunnel.GetName(), err)
	}
	
	f.logger.Debug("Packet forwarded through tunnel",
		zap.String("tunnel", tunnel.GetName()),
		zap.String("destination", decision.Destination),
		zap.Int("size", len(packet)))
	
	return nil
}

// forwardDirect 直接转发
func (f *DefaultForwarder) forwardDirect(packet []byte, destination string) error {
	// TODO: 实现直接转发逻辑
	// 这里需要实现绕过隧道的直接网络访问
	
	f.logger.Debug("Packet forwarded directly",
		zap.String("destination", destination),
		zap.Int("size", len(packet)))
	
	return fmt.Errorf("direct forwarding not implemented")
}

// updateMetrics 更新指标
func (f *DefaultForwarder) updateMetrics(success bool, bytes int, latency time.Duration) {
	f.mu.Lock()
	defer f.mu.Unlock()
	
	if success {
		f.metrics.PacketsForwarded++
		f.metrics.BytesForwarded += int64(bytes)
	} else {
		f.metrics.PacketsDropped++
		f.metrics.ForwardingErrors++
	}
	
	// 更新平均延迟
	if f.metrics.PacketsForwarded > 0 {
		f.metrics.AverageLatency = (f.metrics.AverageLatency + latency) / 2
	} else {
		f.metrics.AverageLatency = latency
	}
	
	f.metrics.LastForwardTime = time.Now()
}
