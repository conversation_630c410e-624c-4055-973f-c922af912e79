package controller

import (
	"context"
	"fmt"
	"net"
	"sort"
	"sync"
	"time"

	"cyber-bastion/pkg/config"

	"go.uber.org/zap"
)

// DefaultRouter 默认路由器实现
type DefaultRouter struct {
	config  *config.RoutingConfig
	logger  *zap.Logger
	ctx     context.Context
	cancel  context.CancelFunc
	mu      sync.RWMutex
	
	rules   []*config.RoutingRule
	metrics *RoutingMetrics
}

// NewRouter 创建新的路由器
func NewRouter(cfg *config.RoutingConfig, logger *zap.Logger) (Router, error) {
	ctx, cancel := context.WithCancel(context.Background())
	
	if cfg == nil {
		cfg = config.DefaultRoutingConfig()
	}
	
	router := &DefaultRouter{
		config:  cfg,
		logger:  logger,
		ctx:     ctx,
		cancel:  cancel,
		rules:   make([]*config.RoutingRule, len(cfg.Rules)),
		metrics: &RoutingMetrics{
			RuleHits: make(map[string]int64),
		},
	}
	
	// 复制并排序规则
	copy(router.rules, cfg.Rules)
	router.sortRules()
	
	return router, nil
}

// Start 启动路由器
func (r *DefaultRouter) Start(ctx context.Context) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	r.logger.Info("Starting router", zap.Int("rules", len(r.rules)))
	
	// 验证规则
	if err := r.validateRules(); err != nil {
		return fmt.Errorf("invalid routing rules: %w", err)
	}
	
	r.logger.Info("Router started successfully")
	return nil
}

// Stop 停止路由器
func (r *DefaultRouter) Stop() error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	r.logger.Info("Stopping router")
	r.cancel()
	
	r.logger.Info("Router stopped")
	return nil
}

// Route 路由决策
func (r *DefaultRouter) Route(destination string) (*RoutingDecision, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	// 解析目标地址
	destIP := net.ParseIP(destination)
	if destIP == nil {
		// 尝试解析为主机名
		ips, err := net.LookupIP(destination)
		if err != nil || len(ips) == 0 {
			return nil, fmt.Errorf("invalid destination: %s", destination)
		}
		destIP = ips[0]
	}
	
	// 匹配路由规则
	for _, rule := range r.rules {
		if r.matchRule(rule, destIP) {
			decision := &RoutingDecision{
				Action:      rule.Action,
				TunnelName:  rule.Tunnel,
				Rule:        rule.Name,
				Destination: destination,
			}
			
			// 更新指标
			r.updateMetrics(decision)
			
			r.logger.Debug("Route decision made",
				zap.String("destination", destination),
				zap.String("action", decision.Action),
				zap.String("rule", decision.Rule),
				zap.String("tunnel", decision.TunnelName))
			
			return decision, nil
		}
	}
	
	// 默认规则：通过隧道
	decision := &RoutingDecision{
		Action:      "tunnel",
		Rule:        "default",
		Destination: destination,
	}
	
	r.updateMetrics(decision)
	
	return decision, nil
}

// UpdateRules 更新路由规则
func (r *DefaultRouter) UpdateRules(rules []*config.RoutingRule) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	// 验证新规则
	tempRouter := &DefaultRouter{rules: rules}
	if err := tempRouter.validateRules(); err != nil {
		return fmt.Errorf("invalid rules: %w", err)
	}
	
	// 更新规则
	r.rules = make([]*config.RoutingRule, len(rules))
	copy(r.rules, rules)
	r.sortRules()
	
	r.logger.Info("Routing rules updated", zap.Int("count", len(r.rules)))
	return nil
}

// GetRules 获取路由规则
func (r *DefaultRouter) GetRules() []*config.RoutingRule {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	rules := make([]*config.RoutingRule, len(r.rules))
	copy(rules, r.rules)
	return rules
}

// GetMetrics 获取路由指标
func (r *DefaultRouter) GetMetrics() *RoutingMetrics {
	r.mu.RLock()
	defer r.mu.RUnlock()
	
	// 复制指标
	metrics := &RoutingMetrics{
		TotalRequests:   r.metrics.TotalRequests,
		TunnelRequests:  r.metrics.TunnelRequests,
		DirectRequests:  r.metrics.DirectRequests,
		BlockedRequests: r.metrics.BlockedRequests,
		LastRequestTime: r.metrics.LastRequestTime,
		RuleHits:        make(map[string]int64),
	}
	
	for rule, hits := range r.metrics.RuleHits {
		metrics.RuleHits[rule] = hits
	}
	
	return metrics
}

// matchRule 匹配路由规则
func (r *DefaultRouter) matchRule(rule *config.RoutingRule, destIP net.IP) bool {
	// 解析目标网络
	_, network, err := net.ParseCIDR(rule.Destination)
	if err != nil {
		// 尝试解析为单个IP
		ruleIP := net.ParseIP(rule.Destination)
		if ruleIP == nil {
			r.logger.Warn("Invalid rule destination", 
				zap.String("rule", rule.Name),
				zap.String("destination", rule.Destination))
			return false
		}
		return destIP.Equal(ruleIP)
	}
	
	return network.Contains(destIP)
}

// updateMetrics 更新指标
func (r *DefaultRouter) updateMetrics(decision *RoutingDecision) {
	r.metrics.TotalRequests++
	r.metrics.LastRequestTime = time.Now()
	
	switch decision.Action {
	case "tunnel":
		r.metrics.TunnelRequests++
	case "direct":
		r.metrics.DirectRequests++
	case "block":
		r.metrics.BlockedRequests++
	}
	
	r.metrics.RuleHits[decision.Rule]++
}

// sortRules 按优先级排序规则
func (r *DefaultRouter) sortRules() {
	sort.Slice(r.rules, func(i, j int) bool {
		return r.rules[i].Priority < r.rules[j].Priority
	})
}

// validateRules 验证路由规则
func (r *DefaultRouter) validateRules() error {
	for _, rule := range r.rules {
		// 验证规则名称
		if rule.Name == "" {
			return fmt.Errorf("rule name cannot be empty")
		}
		
		// 验证目标地址
		if rule.Destination == "" {
			return fmt.Errorf("rule destination cannot be empty")
		}
		
		// 验证CIDR格式
		if _, _, err := net.ParseCIDR(rule.Destination); err != nil {
			// 尝试解析为IP地址
			if net.ParseIP(rule.Destination) == nil {
				return fmt.Errorf("invalid destination format in rule %s: %s", 
					rule.Name, rule.Destination)
			}
		}
		
		// 验证动作
		switch rule.Action {
		case "tunnel", "direct", "block":
			// 有效动作
		default:
			return fmt.Errorf("invalid action in rule %s: %s", 
				rule.Name, rule.Action)
		}
		
		// 如果动作是tunnel，验证隧道名称
		if rule.Action == "tunnel" && rule.Tunnel == "" {
			// 允许空隧道名称，将由隧道管理器选择
		}
	}
	
	return nil
}
