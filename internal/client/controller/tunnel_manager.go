package controller

import (
	"context"
	"fmt"
	"sort"
	"sync"
	"time"

	"cyber-bastion/pkg/config"

	"go.uber.org/zap"
)

// DefaultTunnelManager 默认隧道管理器实现
type DefaultTunnelManager struct {
	config   *config.ClientConfig
	logger   *zap.Logger
	ctx      context.Context
	cancel   context.CancelFunc
	wg       sync.WaitGroup
	mu       sync.RWMutex
	
	tunnels  map[string]Tunnel
	strategy string
	metrics  *TunnelMetrics
	
	// 负载均衡状态
	roundRobinIndex int
}

// NewTunnelManager 创建新的隧道管理器
func NewTunnelManager(cfg *config.ClientConfig, logger *zap.Logger) (TunnelManager, error) {
	ctx, cancel := context.WithCancel(context.Background())
	
	manager := &DefaultTunnelManager{
		config:  cfg,
		logger:  logger,
		ctx:     ctx,
		cancel:  cancel,
		tunnels: make(map[string]Tunnel),
		metrics: &TunnelMetrics{},
	}
	
	// 设置策略
	if cfg.Routing != nil {
		manager.strategy = cfg.Routing.Strategy
	} else {
		manager.strategy = "priority"
	}
	
	// 创建隧道
	if err := manager.createTunnels(); err != nil {
		cancel()
		return nil, fmt.Errorf("failed to create tunnels: %w", err)
	}
	
	return manager, nil
}

// Start 启动隧道管理器
func (tm *DefaultTunnelManager) Start(ctx context.Context) error {
	tm.mu.Lock()
	defer tm.mu.Unlock()
	
	tm.logger.Info("Starting tunnel manager")
	
	// 启动所有隧道
	for name, tunnel := range tm.tunnels {
		if err := tunnel.Connect(); err != nil {
			tm.logger.Error("Failed to connect tunnel", 
				zap.String("tunnel", name), 
				zap.Error(err))
			continue
		}
		tm.logger.Info("Tunnel connected", zap.String("tunnel", name))
	}
	
	// 启动监控协程
	tm.wg.Add(1)
	go tm.monitorLoop()
	
	tm.logger.Info("Tunnel manager started")
	return nil
}

// Stop 停止隧道管理器
func (tm *DefaultTunnelManager) Stop() error {
	tm.mu.Lock()
	defer tm.mu.Unlock()
	
	tm.logger.Info("Stopping tunnel manager")
	
	// 取消上下文
	tm.cancel()
	
	// 断开所有隧道
	for name, tunnel := range tm.tunnels {
		if err := tunnel.Disconnect(); err != nil {
			tm.logger.Error("Failed to disconnect tunnel", 
				zap.String("tunnel", name), 
				zap.Error(err))
		}
	}
	
	// 等待监控协程完成
	tm.wg.Wait()
	
	tm.logger.Info("Tunnel manager stopped")
	return nil
}

// GetTunnels 获取所有隧道
func (tm *DefaultTunnelManager) GetTunnels() map[string]Tunnel {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	
	result := make(map[string]Tunnel)
	for name, tunnel := range tm.tunnels {
		result[name] = tunnel
	}
	return result
}

// GetActiveTunnels 获取活跃隧道
func (tm *DefaultTunnelManager) GetActiveTunnels() []Tunnel {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	
	var active []Tunnel
	for _, tunnel := range tm.tunnels {
		if tunnel.IsConnected() {
			active = append(active, tunnel)
		}
	}
	
	// 按优先级排序
	sort.Slice(active, func(i, j int) bool {
		return active[i].GetConfig().Priority < active[j].GetConfig().Priority
	})
	
	return active
}

// GetTunnel 根据名称获取隧道
func (tm *DefaultTunnelManager) GetTunnel(name string) (Tunnel, bool) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	
	tunnel, exists := tm.tunnels[name]
	return tunnel, exists
}

// SelectTunnel 根据策略选择隧道
func (tm *DefaultTunnelManager) SelectTunnel(destination string) (Tunnel, error) {
	activeTunnels := tm.GetActiveTunnels()
	if len(activeTunnels) == 0 {
		return nil, fmt.Errorf("no active tunnels available")
	}
	
	switch tm.strategy {
	case "priority":
		return activeTunnels[0], nil // 已按优先级排序
		
	case "round_robin":
		tm.mu.Lock()
		tunnel := activeTunnels[tm.roundRobinIndex%len(activeTunnels)]
		tm.roundRobinIndex++
		tm.mu.Unlock()
		return tunnel, nil
		
	case "load_balance":
		// 简单的负载均衡：选择连接数最少的隧道
		var selected Tunnel
		minConnections := int64(-1)
		
		for _, tunnel := range activeTunnels {
			metrics := tunnel.GetMetrics()
			if minConnections == -1 || metrics.ActiveConnections < int(minConnections) {
				selected = tunnel
				minConnections = int64(metrics.ActiveConnections)
			}
		}
		
		if selected == nil {
			return activeTunnels[0], nil
		}
		return selected, nil
		
	case "failover":
		// 故障转移：使用第一个可用的隧道
		return activeTunnels[0], nil
		
	default:
		return activeTunnels[0], nil
	}
}

// GetMetrics 获取隧道指标
func (tm *DefaultTunnelManager) GetMetrics() *TunnelMetrics {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	
	// 聚合所有隧道的指标
	aggregated := &TunnelMetrics{}
	
	for _, tunnel := range tm.tunnels {
		metrics := tunnel.GetMetrics()
		aggregated.TotalConnections += metrics.TotalConnections
		aggregated.ActiveConnections += metrics.ActiveConnections
		aggregated.FailedConnections += metrics.FailedConnections
		aggregated.BytesSent += metrics.BytesSent
		aggregated.BytesReceived += metrics.BytesReceived
		aggregated.PacketsSent += metrics.PacketsSent
		aggregated.PacketsReceived += metrics.PacketsReceived
		aggregated.ConnectionErrors += metrics.ConnectionErrors
		
		// 更新最后连接时间
		if metrics.LastConnectionTime.After(aggregated.LastConnectionTime) {
			aggregated.LastConnectionTime = metrics.LastConnectionTime
		}
	}
	
	// 计算平均延迟
	if len(tm.tunnels) > 0 {
		var totalLatency time.Duration
		count := 0
		
		for _, tunnel := range tm.tunnels {
			if tunnel.IsConnected() {
				metrics := tunnel.GetMetrics()
				totalLatency += metrics.AverageLatency
				count++
			}
		}
		
		if count > 0 {
			aggregated.AverageLatency = totalLatency / time.Duration(count)
		}
	}
	
	return aggregated
}

// createTunnels 创建隧道
func (tm *DefaultTunnelManager) createTunnels() error {
	if tm.config.IsMultiTunnelMode() {
		// 多隧道模式
		for _, tunnelConfig := range tm.config.Tunnels {
			if !tunnelConfig.Enabled {
				continue
			}
			
			tunnel, err := NewTunnel(tunnelConfig, tm.logger)
			if err != nil {
				return fmt.Errorf("failed to create tunnel %s: %w", tunnelConfig.Name, err)
			}
			
			tm.tunnels[tunnelConfig.Name] = tunnel
		}
	} else {
		// 兼容模式：创建单个隧道
		tunnelConfig := tm.config.GetPrimaryTunnel()
		tunnel, err := NewTunnel(tunnelConfig, tm.logger)
		if err != nil {
			return fmt.Errorf("failed to create primary tunnel: %w", err)
		}
		
		tm.tunnels[tunnelConfig.Name] = tunnel
	}
	
	if len(tm.tunnels) == 0 {
		return fmt.Errorf("no tunnels configured")
	}
	
	tm.logger.Info("Created tunnels", zap.Int("count", len(tm.tunnels)))
	return nil
}

// monitorLoop 监控循环
func (tm *DefaultTunnelManager) monitorLoop() {
	defer tm.wg.Done()
	
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-tm.ctx.Done():
			return
		case <-ticker.C:
			tm.performHealthCheck()
		}
	}
}

// performHealthCheck 执行健康检查
func (tm *DefaultTunnelManager) performHealthCheck() {
	tm.mu.RLock()
	tunnels := make([]Tunnel, 0, len(tm.tunnels))
	for _, tunnel := range tm.tunnels {
		tunnels = append(tunnels, tunnel)
	}
	tm.mu.RUnlock()
	
	for _, tunnel := range tunnels {
		if !tunnel.IsConnected() {
			tm.logger.Warn("Tunnel disconnected, attempting reconnection",
				zap.String("tunnel", tunnel.GetName()))
			
			if err := tunnel.Connect(); err != nil {
				tm.logger.Error("Failed to reconnect tunnel",
					zap.String("tunnel", tunnel.GetName()),
					zap.Error(err))
			} else {
				tm.logger.Info("Tunnel reconnected successfully",
					zap.String("tunnel", tunnel.GetName()))
			}
		}
	}
}
