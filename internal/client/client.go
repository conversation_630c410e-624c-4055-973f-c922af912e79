package client

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"fmt"
	"net"
	"os"
	"sync"
	"time"

	"cyber-bastion/pkg/config"
	"cyber-bastion/pkg/protocol"
	"cyber-bastion/pkg/tun"

	"go.uber.org/zap"
)

// ConnectionStats 连接统计信息
type ConnectionStats struct {
	LastHeartbeatTime   time.Time
	HeartbeatFailures   int
	ConsecutiveFailures int
	MessagesSent        int64
	MessagesReceived    int64
	LastMessageTime     time.Time
	ConnectedAt         time.Time
	ReconnectCount      int
}

// Client TCP客户端
type Client struct {
	config            *config.ClientConfig
	logger            *zap.Logger
	conn              net.Conn
	ctx               context.Context
	cancel            context.CancelFunc
	wg                sync.WaitGroup
	mu                sync.RWMutex
	connected         bool
	authorized        bool
	reconnecting      bool
	reconnectMu       sync.Mutex
	pendingHeartbeats map[string]chan bool
	heartbeatMu       sync.Mutex
	stats             ConnectionStats
	statsMu           sync.RWMutex
	// TUN接口相关
	tunInterface *tun.TunInterface
	tunEnabled   bool
	// 数据包缓存相关
	packetBuffer   [][]byte
	packetBufferMu sync.Mutex
	maxBufferSize  int
	bufferEnabled  bool
}

// NewClient 创建新客户端
func NewClient(cfg *config.ClientConfig, logger *zap.Logger) *Client {
	ctx, cancel := context.WithCancel(context.Background())
	return &Client{
		config:            cfg,
		logger:            logger,
		ctx:               ctx,
		cancel:            cancel,
		pendingHeartbeats: make(map[string]chan bool),
		packetBuffer:      make([][]byte, 0),
		maxBufferSize:     100, // 最多缓存100个数据包
		bufferEnabled:     true,
	}
}

// Start 启动客户端
func (c *Client) Start() error {
	c.logger.Info("Starting client",
		zap.String("server", fmt.Sprintf("%s:%d", c.config.ServerHost, c.config.ServerPort)))

	// 初始化TUN接口（如果启用）
	if c.config.Tun != nil && c.config.Tun.Enabled {
		if err := c.initTunInterface(); err != nil {
			return fmt.Errorf("failed to initialize TUN interface: %w", err)
		}
	}

	// 连接到服务器
	if err := c.connect(); err != nil {
		return fmt.Errorf("failed to connect: %w", err)
	}

	// 认证
	if err := c.authenticate(); err != nil {
		return fmt.Errorf("failed to authenticate: %w", err)
	}

	// 启动心跳
	c.wg.Add(1)
	go c.heartbeatLoop()

	// 启动消息接收
	c.wg.Add(1)
	go c.receiveLoop()

	// 启动TUN数据处理（如果启用）
	if c.tunEnabled {
		c.wg.Add(1)
		go c.tunDataLoop()
	}

	c.logger.Info("Client started successfully")
	return nil
}

// Stop 停止客户端
func (c *Client) Stop() error {
	c.logger.Info("Stopping client...")
	c.cancel()

	c.mu.Lock()
	if c.conn != nil {
		c.conn.Close()
		c.conn = nil
	}
	c.connected = false
	c.authorized = false
	c.mu.Unlock()

	// 停止TUN接口
	if c.tunInterface != nil {
		if err := c.tunInterface.Stop(); err != nil {
			c.logger.Error("Failed to stop TUN interface", zap.Error(err))
		}
		c.tunInterface = nil
		c.tunEnabled = false
	}

	// 清理数据包缓存
	c.clearPacketBuffer()

	c.wg.Wait()
	c.logger.Info("Client stopped")
	return nil
}

// SendData 发送数据消息
func (c *Client) SendData(data []byte) error {
	c.mu.RLock()
	if !c.connected || !c.authorized {
		c.mu.RUnlock()
		return fmt.Errorf("client not connected or not authorized")
	}
	conn := c.conn
	c.mu.RUnlock()

	msg := protocol.NewDataMessage(fmt.Sprintf("data-%d", time.Now().UnixNano()), data)
	if err := protocol.SendMessage(conn, msg); err != nil {
		c.logger.Error("Failed to send data message", zap.Error(err))
		return err
	}

	// 更新发送统计
	c.updateMessageSent()

	c.logger.Debug("Data message sent", zap.String("message_id", msg.ID), zap.Int("data_length", len(data)))
	return nil
}

// SendTunData 发送TUN数据包消息（支持缓存和重试）
func (c *Client) SendTunData(packet []byte) error {
	c.mu.RLock()
	connected := c.connected
	authorized := c.authorized
	conn := c.conn
	c.mu.RUnlock()

	// 如果连接正常，直接发送
	if connected && authorized && conn != nil {
		return c.sendTunDataDirect(packet, conn)
	}

	// 如果未连接但启用了缓存，将数据包加入缓存
	if c.bufferEnabled {
		c.bufferPacket(packet)
		c.logger.Debug("Packet buffered due to disconnection",
			zap.Int("packet_length", len(packet)),
			zap.Int("buffer_size", c.getBufferSize()))
		return nil
	}

	return fmt.Errorf("client not connected or not authorized")
}

// sendTunDataDirect 直接发送TUN数据包
func (c *Client) sendTunDataDirect(packet []byte, conn net.Conn) error {
	msg := protocol.NewTunDataMessage(fmt.Sprintf("tun-%d", time.Now().UnixNano()), packet)
	if err := protocol.SendMessage(conn, msg); err != nil {
		c.logger.Error("Failed to send TUN data message", zap.Error(err))
		return err
	}

	// 更新发送统计
	c.updateMessageSent()

	c.logger.Debug("TUN data message sent", zap.String("message_id", msg.ID), zap.Int("packet_length", len(packet)))
	return nil
}

// bufferPacket 缓存数据包
func (c *Client) bufferPacket(packet []byte) {
	c.packetBufferMu.Lock()
	defer c.packetBufferMu.Unlock()

	// 如果缓存已满，移除最旧的数据包
	if len(c.packetBuffer) >= c.maxBufferSize {
		c.packetBuffer = c.packetBuffer[1:]
		c.logger.Debug("Packet buffer full, dropping oldest packet")
	}

	// 复制数据包并加入缓存
	bufferedPacket := make([]byte, len(packet))
	copy(bufferedPacket, packet)
	c.packetBuffer = append(c.packetBuffer, bufferedPacket)
}

// getBufferSize 获取当前缓存大小
func (c *Client) getBufferSize() int {
	c.packetBufferMu.Lock()
	defer c.packetBufferMu.Unlock()
	return len(c.packetBuffer)
}

// flushPacketBuffer 发送缓存的数据包
func (c *Client) flushPacketBuffer() {
	c.packetBufferMu.Lock()
	bufferedPackets := make([][]byte, len(c.packetBuffer))
	copy(bufferedPackets, c.packetBuffer)
	c.packetBuffer = c.packetBuffer[:0] // 清空缓存
	c.packetBufferMu.Unlock()

	if len(bufferedPackets) == 0 {
		return
	}

	c.logger.Info("Flushing buffered packets after reconnection",
		zap.Int("packet_count", len(bufferedPackets)))

	c.mu.RLock()
	conn := c.conn
	c.mu.RUnlock()

	if conn == nil {
		c.logger.Warn("Cannot flush packets: no connection available")
		return
	}

	// 发送所有缓存的数据包
	successCount := 0
	for i, packet := range bufferedPackets {
		if err := c.sendTunDataDirect(packet, conn); err != nil {
			c.logger.Error("Failed to send buffered packet",
				zap.Int("packet_index", i),
				zap.Error(err))
			// 如果发送失败，将剩余的包重新加入缓存
			c.packetBufferMu.Lock()
			c.packetBuffer = append(bufferedPackets[i:], c.packetBuffer...)
			c.packetBufferMu.Unlock()
			break
		}
		successCount++
	}

	c.logger.Info("Buffered packets flushed",
		zap.Int("success_count", successCount),
		zap.Int("total_count", len(bufferedPackets)))
}

// clearPacketBuffer 清空数据包缓存
func (c *Client) clearPacketBuffer() {
	c.packetBufferMu.Lock()
	defer c.packetBufferMu.Unlock()

	bufferSize := len(c.packetBuffer)
	c.packetBuffer = c.packetBuffer[:0]

	if bufferSize > 0 {
		c.logger.Info("Packet buffer cleared",
			zap.Int("dropped_packets", bufferSize))
	}
}

// getBufferStats 获取缓存统计信息
func (c *Client) getBufferStats() (int, int) {
	c.packetBufferMu.Lock()
	defer c.packetBufferMu.Unlock()
	return len(c.packetBuffer), c.maxBufferSize
}

// IsConnected 检查是否已连接
func (c *Client) IsConnected() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.connected
}

// IsAuthorized 检查是否已认证
func (c *Client) IsAuthorized() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.authorized
}

// GetConnectionStats 获取连接统计信息
func (c *Client) GetConnectionStats() ConnectionStats {
	c.statsMu.RLock()
	defer c.statsMu.RUnlock()
	return c.stats
}

// updateMessageSent 更新发送消息统计
func (c *Client) updateMessageSent() {
	c.statsMu.Lock()
	defer c.statsMu.Unlock()
	c.stats.MessagesSent++
	c.stats.LastMessageTime = time.Now()
}

// updateMessageReceived 更新接收消息统计
func (c *Client) updateMessageReceived() {
	c.statsMu.Lock()
	defer c.statsMu.Unlock()
	c.stats.MessagesReceived++
	c.stats.LastMessageTime = time.Now()
}

// updateHeartbeatSuccess 更新心跳成功统计
func (c *Client) updateHeartbeatSuccess() {
	c.statsMu.Lock()
	defer c.statsMu.Unlock()
	c.stats.LastHeartbeatTime = time.Now()
	c.stats.ConsecutiveFailures = 0
}

// updateHeartbeatFailure 更新心跳失败统计
func (c *Client) updateHeartbeatFailure() {
	c.statsMu.Lock()
	defer c.statsMu.Unlock()
	c.stats.HeartbeatFailures++
	c.stats.ConsecutiveFailures++
}

// resetConnectionStats 重置连接统计
func (c *Client) resetConnectionStats() {
	c.statsMu.Lock()
	defer c.statsMu.Unlock()
	now := time.Now()
	c.stats = ConnectionStats{
		ConnectedAt:    now,
		ReconnectCount: c.stats.ReconnectCount + 1,
	}
}

// connect 连接到服务器
func (c *Client) connect() error {
	addr := fmt.Sprintf("%s:%d", c.config.ServerHost, c.config.ServerPort)

	timeout := time.Duration(c.config.ConnectTimeout) * time.Second

	var conn net.Conn
	var err error

	if c.config.EnableTLS {
		// 使用TLS连接
		tlsConfig := &tls.Config{
			ServerName:         c.config.ServerHost,
			InsecureSkipVerify: c.config.TLSSkipVerify,
		}

		// 加载CA证书
		if c.config.TLSCAFile != "" {
			caCert, err := os.ReadFile(c.config.TLSCAFile)
			if err != nil {
				return fmt.Errorf("failed to read CA certificate: %w", err)
			}

			caCertPool := x509.NewCertPool()
			if !caCertPool.AppendCertsFromPEM(caCert) {
				return fmt.Errorf("failed to parse CA certificate")
			}
			tlsConfig.RootCAs = caCertPool
			c.logger.Info("TLS CA certificate loaded", zap.String("ca_file", c.config.TLSCAFile))
		}

		// 如果指定了客户端证书
		if c.config.TLSCertFile != "" && c.config.TLSKeyFile != "" {
			cert, err := tls.LoadX509KeyPair(c.config.TLSCertFile, c.config.TLSKeyFile)
			if err != nil {
				return fmt.Errorf("failed to load client certificate: %w", err)
			}
			tlsConfig.Certificates = []tls.Certificate{cert}
			c.logger.Info("TLS client certificate loaded",
				zap.String("cert_file", c.config.TLSCertFile))
		}

		dialer := &net.Dialer{Timeout: timeout}
		conn, err = tls.DialWithDialer(dialer, "tcp", addr, tlsConfig)
		if err != nil {
			return fmt.Errorf("failed to connect to %s with TLS: %w", addr, err)
		}
		c.logger.Info("Connected to server with TLS", zap.String("server", addr))
	} else {
		// 普通TCP连接
		conn, err = net.DialTimeout("tcp", addr, timeout)
		if err != nil {
			return fmt.Errorf("failed to connect to %s: %w", addr, err)
		}
		c.logger.Info("Connected to server", zap.String("server", addr))
	}

	c.mu.Lock()
	c.conn = conn
	c.connected = true
	c.mu.Unlock()

	// 重置连接统计
	c.resetConnectionStats()

	return nil
}

// authenticate 认证
func (c *Client) authenticate() error {
	c.mu.RLock()
	conn := c.conn
	c.mu.RUnlock()

	if conn == nil {
		return fmt.Errorf("not connected")
	}

	// 发送认证消息
	authMsg := protocol.NewAuthMessage(c.config.AuthToken)
	if err := protocol.SendMessage(conn, authMsg); err != nil {
		return fmt.Errorf("failed to send auth message: %w", err)
	}

	// 设置读取超时
	authTimeout := 10 * time.Second
	conn.SetReadDeadline(time.Now().Add(authTimeout))

	// 等待响应
	response, err := protocol.ReceiveMessage(conn)

	// 重置读取超时 - 清除之前设置的超时
	conn.SetReadDeadline(time.Time{})

	if err != nil {
		return fmt.Errorf("failed to receive auth response: %w", err)
	}

	switch response.Type {
	case protocol.MessageTypeResponse:
		c.mu.Lock()
		c.authorized = true
		c.mu.Unlock()
		c.logger.Info("Authentication successful")
		return nil
	case protocol.MessageTypeError:
		return fmt.Errorf("authentication failed: %s", string(response.Data))
	default:
		return fmt.Errorf("unexpected response type: %d", response.Type)
	}
}

// heartbeatLoop 心跳循环
func (c *Client) heartbeatLoop() {
	defer c.wg.Done()

	interval := time.Duration(c.config.HeartbeatInterval) * time.Second
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			if err := c.sendHeartbeat(); err != nil {
				stats := c.GetConnectionStats()
				if c.isExpectedCloseError(err) {
					c.logger.Debug("Heartbeat failed due to connection close",
						zap.Error(err),
						zap.Int("consecutive_failures", stats.ConsecutiveFailures),
						zap.Int("total_failures", stats.HeartbeatFailures))
				} else {
					c.logger.Error("Failed to send heartbeat",
						zap.Error(err),
						zap.Int("consecutive_failures", stats.ConsecutiveFailures),
						zap.Int("total_failures", stats.HeartbeatFailures),
						zap.Duration("connected_duration", time.Since(stats.ConnectedAt)))
				}
				c.handleDisconnection()
				return
			}
		}
	}
}

// sendHeartbeat 发送心跳
func (c *Client) sendHeartbeat() error {
	c.mu.RLock()
	if !c.connected {
		c.mu.RUnlock()
		return fmt.Errorf("not connected")
	}
	conn := c.conn
	c.mu.RUnlock()

	heartbeat := protocol.NewHeartbeat()

	// 创建响应通道
	responseChan := make(chan bool, 1)
	c.heartbeatMu.Lock()
	c.pendingHeartbeats[heartbeat.ID] = responseChan
	c.heartbeatMu.Unlock()

	// 清理函数
	defer func() {
		c.heartbeatMu.Lock()
		delete(c.pendingHeartbeats, heartbeat.ID)
		c.heartbeatMu.Unlock()
		close(responseChan)
	}()

	if err := protocol.SendMessage(conn, heartbeat); err != nil {
		return err
	}

	c.logger.Debug("Heartbeat sent", zap.String("heartbeat_id", heartbeat.ID))

	// 等待响应，设置5秒超时
	select {
	case success := <-responseChan:
		if success {
			c.updateHeartbeatSuccess()
			c.logger.Debug("Heartbeat response received", zap.String("heartbeat_id", heartbeat.ID))
			return nil
		}
		c.updateHeartbeatFailure()
		return fmt.Errorf("heartbeat failed")
	case <-time.After(5 * time.Second):
		c.updateHeartbeatFailure()
		c.logger.Warn("Heartbeat timeout", zap.String("heartbeat_id", heartbeat.ID))
		return fmt.Errorf("heartbeat timeout")
	case <-c.ctx.Done():
		return fmt.Errorf("client stopped")
	}
}

// receiveLoop 消息接收循环
func (c *Client) receiveLoop() {
	defer c.wg.Done()

	for {
		select {
		case <-c.ctx.Done():
			return
		default:
			c.mu.RLock()
			if !c.connected {
				c.mu.RUnlock()
				return
			}
			conn := c.conn
			c.mu.RUnlock()

			msg, err := protocol.ReceiveMessage(conn)
			if err != nil {
				stats := c.GetConnectionStats()
				// 检查是否是正常关闭连接
				if c.isExpectedCloseError(err) {
					c.logger.Debug("Connection closed normally",
						zap.Error(err),
						zap.Duration("connected_duration", time.Since(stats.ConnectedAt)),
						zap.Int64("messages_sent", stats.MessagesSent),
						zap.Int64("messages_received", stats.MessagesReceived))
				} else {
					c.logger.Error("Failed to receive message",
						zap.Error(err),
						zap.Duration("connected_duration", time.Since(stats.ConnectedAt)),
						zap.Int64("messages_sent", stats.MessagesSent),
						zap.Int64("messages_received", stats.MessagesReceived),
						zap.Int("heartbeat_failures", stats.HeartbeatFailures))
				}
				c.handleDisconnection()
				return
			}

			c.handleMessage(msg)
		}
	}
}

// handleMessage 处理接收到的消息
func (c *Client) handleMessage(msg *protocol.Message) {
	// 更新接收统计
	c.updateMessageReceived()

	c.logger.Debug("Received message", zap.String("message", msg.String()))

	switch msg.Type {
	case protocol.MessageTypeResponse:
		// 检查是否是心跳响应
		c.heartbeatMu.Lock()
		if responseChan, exists := c.pendingHeartbeats[msg.ID]; exists {
			// 这是心跳响应
			select {
			case responseChan <- true:
			default:
			}
			c.heartbeatMu.Unlock()
			c.logger.Debug("Heartbeat response processed", zap.String("message_id", msg.ID))
		} else {
			c.heartbeatMu.Unlock()
			// 普通响应消息
			c.logger.Info("Received response",
				zap.String("message_id", msg.ID),
				zap.String("data", string(msg.Data)))
		}
	case protocol.MessageTypeError:
		// 检查是否是心跳错误响应
		c.heartbeatMu.Lock()
		if responseChan, exists := c.pendingHeartbeats[msg.ID]; exists {
			// 这是心跳错误响应
			select {
			case responseChan <- false:
			default:
			}
			c.heartbeatMu.Unlock()
			c.logger.Warn("Heartbeat error response",
				zap.String("message_id", msg.ID),
				zap.String("error", string(msg.Data)))
		} else {
			c.heartbeatMu.Unlock()
			// 普通错误消息
			c.logger.Error("Received error message",
				zap.String("message_id", msg.ID),
				zap.String("error", string(msg.Data)))
		}
	case protocol.MessageTypeTunResponse:
		// 处理TUN响应数据包
		c.handleTunResponse(msg)
	default:
		c.logger.Warn("Received unknown message type", zap.Uint8("type", uint8(msg.Type)))
	}
}

// handleTunResponse 处理TUN响应数据包
func (c *Client) handleTunResponse(msg *protocol.Message) {
	if !c.tunEnabled || c.tunInterface == nil {
		c.logger.Warn("Received TUN response but TUN interface is not enabled",
			zap.String("message_id", msg.ID))
		return
	}

	// 解析数据包获取源IP、目标IP和协议类型用于日志记录
	var srcIP, dstIP, protocolStr string
	if len(msg.Data) >= 20 {
		// 使用tun包中的工具函数解析数据包
		srcIPAddr := tun.GetSourceIP(msg.Data)
		dstIPAddr := tun.GetDestinationIP(msg.Data)
		protocolStr = tun.GetProtocolString(msg.Data)

		if srcIPAddr != nil {
			srcIP = srcIPAddr.String()
		}
		if dstIPAddr != nil {
			dstIP = dstIPAddr.String()
		}
	}

	c.logger.Debug("Received TUN response packet",
		zap.String("message_id", msg.ID),
		zap.Int("packet_length", len(msg.Data)),
		zap.String("src_ip", srcIP),
		zap.String("dst_ip", dstIP),
		zap.String("protocol", protocolStr))

	// 将响应数据包写入TUN接口
	n, err := c.tunInterface.Write(msg.Data)
	if err != nil {
		c.logger.Error("Failed to write TUN response packet to interface",
			zap.String("message_id", msg.ID),
			zap.Error(err),
			zap.String("src_ip", srcIP),
			zap.String("dst_ip", dstIP),
			zap.String("protocol", protocolStr))
		return
	}

	c.logger.Debug("TUN response packet written to interface",
		zap.String("message_id", msg.ID),
		zap.Int("bytes_written", n),
		zap.String("src_ip", srcIP),
		zap.String("dst_ip", dstIP),
		zap.String("protocol", protocolStr))
}

// isExpectedCloseError 检查是否是预期的连接关闭错误
func (c *Client) isExpectedCloseError(err error) bool {
	if err == nil {
		return false
	}

	// 检查context是否已取消（正常关闭）
	if c.ctx.Err() != nil {
		return true
	}

	errStr := err.Error()
	// 检查常见的正常关闭错误
	return errStr == "EOF" ||
		errStr == "io: read/write on closed pipe" ||
		errStr == "broken pipe" ||
		// 检查包含这些关键词的错误
		containsAny(errStr, []string{
			"use of closed network connection",
			"connection reset by peer",
			"i/o timeout",
			"network is unreachable",
			"connection refused",
			"EOF",
		})
}

// isConnectionError 检查是否是连接相关的错误
func (c *Client) isConnectionError(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()
	connectionErrors := []string{
		"client not connected",
		"not authorized",
		"connection reset by peer",
		"broken pipe",
		"use of closed network connection",
		"EOF",
		"connection refused",
		"no route to host",
		"network is unreachable",
	}

	return containsAny(errStr, connectionErrors)
}

// handleDisconnection 处理断开连接
func (c *Client) handleDisconnection() {
	stats := c.GetConnectionStats()

	c.mu.Lock()
	if c.conn != nil {
		c.conn.Close()
		c.conn = nil
	}
	c.connected = false
	c.authorized = false
	c.mu.Unlock()

	c.logger.Warn("Disconnected from server",
		zap.Duration("connected_duration", time.Since(stats.ConnectedAt)),
		zap.Int64("messages_sent", stats.MessagesSent),
		zap.Int64("messages_received", stats.MessagesReceived),
		zap.Int("heartbeat_failures", stats.HeartbeatFailures),
		zap.Int("reconnect_count", stats.ReconnectCount))

	// 尝试重连 - 使用互斥锁防止多个重连协程
	c.reconnectMu.Lock()
	defer c.reconnectMu.Unlock()

	if c.ctx.Err() == nil && !c.reconnecting {
		c.reconnecting = true
		c.wg.Add(1)
		go c.reconnectLoop()
	}
}

// reconnectLoop 重连循环 - 使用指数退避策略
func (c *Client) reconnectLoop() {
	defer c.wg.Done()
	defer func() {
		c.reconnectMu.Lock()
		c.reconnecting = false
		c.reconnectMu.Unlock()
	}()

	initialDelay := time.Duration(c.config.ReconnectDelay) * time.Second
	maxDelay := time.Duration(c.config.MaxReconnectDelay) * time.Second
	maxTries := c.config.MaxReconnectTries

	currentDelay := initialDelay
	attempts := 0

	for attempts < maxTries {
		select {
		case <-c.ctx.Done():
			return
		case <-time.After(currentDelay):
			attempts++
			c.logger.Info("Attempting to reconnect...",
				zap.Int("attempt", attempts),
				zap.Int("max_attempts", maxTries),
				zap.Duration("delay", currentDelay))

			if err := c.connect(); err != nil {
				c.logger.Error("Reconnection failed",
					zap.Error(err),
					zap.Int("attempt", attempts))

				// 指数退避：延迟时间翻倍，但不超过最大值
				currentDelay = min(currentDelay*2, maxDelay)
				continue
			}

			if err := c.authenticate(); err != nil {
				c.logger.Error("Re-authentication failed",
					zap.Error(err),
					zap.Int("attempt", attempts))
				c.mu.Lock()
				if c.conn != nil {
					c.conn.Close()
					c.conn = nil
				}
				c.connected = false
				c.mu.Unlock()

				// 指数退避：延迟时间翻倍，但不超过最大值
				currentDelay = min(currentDelay*2, maxDelay)
				continue
			}

			c.logger.Info("Reconnected successfully",
				zap.Int("attempts_used", attempts))

			// 重新启动消息接收
			c.wg.Add(1)
			go c.receiveLoop()

			// 重新启动TUN数据处理（如果启用）
			if c.tunEnabled {
				c.wg.Add(1)
				go c.tunDataLoop()
			}

			// 发送缓存的数据包
			go c.flushPacketBuffer()

			return
		}
	}

	c.logger.Error("Max reconnection attempts reached, giving up",
		zap.Int("max_attempts", maxTries))
}

// min 返回两个 time.Duration 中的较小值
func min(a, b time.Duration) time.Duration {
	if a < b {
		return a
	}
	return b
}

// containsAny 检查字符串是否包含任何一个子字符串
func containsAny(s string, substrings []string) bool {
	for _, substr := range substrings {
		if len(s) >= len(substr) {
			for i := 0; i <= len(s)-len(substr); i++ {
				if s[i:i+len(substr)] == substr {
					return true
				}
			}
		}
	}
	return false
}

// initTunInterface 初始化TUN接口
func (c *Client) initTunInterface() error {
	// 创建TUN配置
	tunConfig := &tun.Config{
		Name:    c.config.Tun.Name,
		IP:      c.config.Tun.IP,
		Netmask: c.config.Tun.Netmask,
		MTU:     c.config.Tun.MTU,
		Enabled: c.config.Tun.Enabled,
	}

	// 创建TUN接口
	tunInterface, err := tun.NewTunInterface(tunConfig, c.logger)
	if err != nil {
		return fmt.Errorf("failed to create TUN interface: %w", err)
	}

	// 启动TUN接口
	if err := tunInterface.Start(); err != nil {
		return fmt.Errorf("failed to start TUN interface: %w", err)
	}

	c.tunInterface = tunInterface
	c.tunEnabled = true

	c.logger.Info("TUN interface initialized successfully",
		zap.String("name", tunInterface.Name()),
		zap.String("ip", c.config.Tun.IP),
		zap.Int("mtu", c.config.Tun.MTU))

	return nil
}

// tunDataLoop TUN数据处理循环
func (c *Client) tunDataLoop() {
	defer c.wg.Done()

	buffer := make([]byte, 2048) // 足够大的缓冲区来处理MTU大小的数据包

	for {
		select {
		case <-c.ctx.Done():
			return
		default:
			// 从TUN接口读取数据包
			n, err := c.tunInterface.Read(buffer)
			if err != nil {
				if c.ctx.Err() != nil {
					// 客户端正在关闭
					return
				}
				c.logger.Error("Failed to read from TUN interface", zap.Error(err))
				continue
			}

			if n > 0 {
				packet := make([]byte, n)
				copy(packet, buffer[:n])

				// 解析数据包获取源IP、目标IP和协议类型用于日志记录
				var srcIP, dstIP, protocolStr string
				if len(packet) >= 20 {
					// 使用tun包中的工具函数解析数据包
					srcIPAddr := tun.GetSourceIP(packet)
					dstIPAddr := tun.GetDestinationIP(packet)
					protocolStr = tun.GetProtocolString(packet)

					if srcIPAddr != nil {
						srcIP = srcIPAddr.String()
					}
					if dstIPAddr != nil {
						dstIP = dstIPAddr.String()
					}
				}

				// 发送数据包到服务器
				if err := c.SendTunData(packet); err != nil {
					// 检查是否是连接相关的错误
					if c.isConnectionError(err) {
						c.logger.Debug("TUN data send failed due to connection issue, will be buffered",
							zap.Error(err),
							zap.String("src_ip", srcIP),
							zap.String("dst_ip", dstIP),
							zap.String("protocol", protocolStr))
						// 触发重连，但不退出TUN循环
						if !c.isExpectedCloseError(err) {
							go c.handleDisconnection()
						}
					} else {
						c.logger.Error("Failed to send TUN data to server",
							zap.Error(err),
							zap.String("src_ip", srcIP),
							zap.String("dst_ip", dstIP),
							zap.String("protocol", protocolStr))
					}
				} else {
					c.logger.Debug("TUN packet forwarded to server",
						zap.Int("size", n),
						zap.String("src_ip", srcIP),
						zap.String("dst_ip", dstIP),
						zap.String("protocol", protocolStr))
				}
			}
		}
	}
}
