```mermaid
flowchart TD
    subgraph Client 端
        C0[配置加载<br/>config.yaml 或 json]
        C1[控制模块<br/>重连逻辑、状态监控]
        C2[路由策略模块<br/>决定哪些流量走 TUN]
        C3[TUN 虚拟网卡<br/>10.0.0.0/24]
        C4[VPN 客户端核心转发器<br/>Golang实现]
        C5[加密传输层<br/>WebSocket / TLS TCP / QUIC]
    end

    subgraph 长连接传输通道
        LINK1[隧道连接 #1<br/>vpn1.server.com]
        LINK2[隧道连接 #2<br/>vpn2.server.com]
    end

    subgraph Server 端
        S1[服务端监听器<br/>监听所有隧道连接]
        S2[转发逻辑<br/>根据虚拟源IP决定路由]
        S3[NAT模块 或 TUN发包<br/>本地发出真实包]
        S4[真实网络出口<br/>eth0]
        S5[目标服务器]
    end

    C0 --> C1
    C1 --> C4
    C2 --> C3
    C3 --> C4
    C4 --> C5

    C5 --> LINK1
    C5 --> LINK2

    LINK1 --> S1
    LINK2 --> S1

    S1 --> S2 --> S3 --> S4 --> S5
```
