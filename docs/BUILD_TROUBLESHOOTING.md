# Build Troubleshooting Guide

This document provides solutions for common build issues in the Cyber Bastion project.

## Git VCS Build Errors

### Problem

You may encounter the following error when building:

```
error obtaining VCS status: exit status 128
Use -buildvcs=false to disable VCS stamping.
make: *** [Makefile:102: build-all] Error 1
```

### Root Cause

This error occurs when Go tries to include Git version control information in the binary but encounters issues with the Git repository state. Common causes include:

1. **Incomplete Git repository** - The repository was not properly cloned
2. **Git permission issues** - Insufficient permissions to access Git metadata
3. **Corrupted Git repository** - The `.git` directory is damaged
4. **Uncommitted changes** - Working directory has uncommitted files
5. **Missing Git configuration** - Git user.name or user.email not set

### Solutions

#### Quick Fix (Recommended)

Use the default build commands which automatically disable VCS stamping:

```bash
# For local development
make build

# For multi-platform builds
make build-all
```

#### Diagnosis

Run the diagnostic script to get detailed information about your Git setup:

```bash
make diagnose-git
```

This will analyze your Git repository and provide specific recommendations.

#### Advanced Solutions

1. **For builds with version information** (requires clean Git repo):
   ```bash
   # Commit your changes first
   git add .
   git commit -m "Your commit message"
   
   # Then build with VCS info
   make build-with-vcs
   make build-all-with-vcs
   ```

2. **Fix Git configuration**:
   ```bash
   git config user.name "Your Name"
   git config user.email "<EMAIL>"
   ```

3. **Re-initialize Git repository** (if corrupted):
   ```bash
   rm -rf .git
   git init
   git remote add origin <your-repo-url>
   git fetch
   git checkout main
   ```

## Build Targets Reference

| Target | Description | VCS Info | Platforms | Use Case |
|--------|-------------|----------|-----------|----------|
| `make build` | Build for current platform | ❌ | Current | Development, CI/CD |
| `make build-all` | Build for all platforms | ❌ | All supported | Release builds, CI/CD |
| `make build-linux` | Build for Linux platforms | ❌ | Linux amd64, arm64 | Linux deployment |
| `make build-darwin` | Build for macOS platforms | ❌ | macOS amd64, arm64 | macOS deployment |
| `make build-windows` | Build for Windows platforms | ❌ | Windows amd64 | Windows deployment |
| `make build-with-vcs` | Build with Git info | ✅ | Current | Local builds with version info |
| `make build-all-with-vcs` | Build all platforms with Git info | ✅ | All supported | Release builds with version info |

## Supported Platforms

The project supports the following platforms and architectures:

| Platform | Architecture | Binary Suffix | Notes |
|----------|-------------|---------------|-------|
| Linux | amd64 | `-linux-amd64` | Standard x86_64 Linux |
| Linux | arm64 | `-linux-arm64` | ARM64 Linux (AWS Graviton, etc.) |
| macOS | amd64 | `-darwin-amd64` | Intel-based Macs |
| macOS | arm64 | `-darwin-arm64` | Apple Silicon Macs (M1, M2, etc.) |
| Windows | amd64 | `-windows-amd64.exe` | 64-bit Windows |

## Environment-Specific Solutions

### CI/CD Environments

For CI/CD pipelines, always use the default build targets:

```yaml
# GitHub Actions example
- name: Build
  run: make build-all
```

### Docker Builds

When building in Docker containers, use:

```dockerfile
RUN make build-all
```

### Development Environment

For local development with frequent changes:

```bash
# Quick builds without VCS info
make build

# With version info (commit changes first)
git add . && git commit -m "WIP: development changes"
make build-with-vcs
```

## Makefile Configuration

The project Makefile includes the following VCS-related configuration:

```makefile
# Disable VCS stamping to avoid Git status issues
BUILD_FLAGS=-buildvcs=false
```

This ensures that default build targets work reliably across different environments.

## Verification

After applying any fix, verify the build works:

```bash
# Clean and rebuild
make clean
make build

# Verify binaries were created
ls -la bin/
```

## Getting Help

If you continue to experience build issues:

1. Run `make diagnose-git` for detailed diagnosis
2. Check the project's GitHub issues
3. Ensure you have the latest version of Go (1.21+)
4. Verify your Git installation is working: `git --version`

## Related Documentation

- [README.md](../README.md) - General project documentation
- [TESTING.md](TESTING.md) - Testing guide
- [Go Build Documentation](https://pkg.go.dev/cmd/go#hdr-Build_constraints) - Official Go build documentation
