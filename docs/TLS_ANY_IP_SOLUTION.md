# TLS支持任意IP地址的解决方案

本文档说明如何配置Cyber Bastion以支持TLS加密连接到任意IP地址。

## 问题描述

当客户端尝试连接到服务器的特定IP地址时，可能会遇到以下错误：

```
tls: failed to verify certificate: x509: certificate is valid for 127.0.0.1, ::1, not *************
```

这是因为TLS证书中的Subject Alternative Names (SAN) 不包含目标IP地址。

## 解决方案

### 方案1：使用加密专用证书（推荐）

这种方案生成专门用于加密的证书，客户端跳过主机名验证，仅用于数据加密。

#### 快速配置

```bash
# 生成加密专用证书并自动配置
make certs-enable-encryption

# 构建项目
make build

# 现在可以连接到任意IP地址
./bin/client-linux-amd64 -c configs/client.yaml -s ************* -p 8080
```

#### 手动配置

1. **生成加密专用证书**：
```bash
make certs-encryption
```

2. **配置服务器** (`configs/server.yaml`)：
```yaml
security:
  enable_tls: true
  tls_cert_file: "certs/server.crt"
  tls_key_file: "certs/server.key"
  tls_ca_file: "certs/ca.crt"
  tls_skip_verify: false
```

3. **配置客户端** (`configs/client.yaml`)：
```yaml
enable_tls: true
tls_cert_file: "certs/client.crt"
tls_key_file: "certs/client.key"
tls_ca_file: "certs/ca.crt"
tls_skip_verify: true    # 关键设置：跳过证书验证
```

### 方案2：仅修改客户端配置

如果已有证书，只需修改客户端配置：

```yaml
# configs/client.yaml
tls_skip_verify: true     # 允许连接任意IP
```

### 方案3：生成包含特定IP的证书

为特定IP地址生成证书（适用于固定IP的生产环境）：

```bash
# 使用完整证书生成脚本，指定服务器IP
./scripts/generate-certs.sh --server-cn "*************" --clean
```

## 证书类型对比

| 证书类型 | 适用场景 | 安全级别 | IP支持 | 配置复杂度 |
|---------|---------|---------|--------|-----------|
| 标准证书 | 固定域名/IP | 高 | 限制 | 中等 |
| 加密专用证书 | 动态IP/私网 | 中等 | 任意 | 简单 |
| 跳过验证 | 测试/内网 | 低 | 任意 | 最简单 |

## 安全考虑

### 加密专用证书的安全性

✅ **提供的安全保护**：
- 数据传输加密（AES等）
- 防止数据被窃听
- 防止数据被篡改

⚠️ **不提供的安全保护**：
- 服务器身份验证
- 防止中间人攻击（如果攻击者有证书）

### 适用场景

**推荐使用加密专用证书的场景**：
- 私有网络内部通信
- 动态IP环境
- 开发和测试环境
- 对加密有要求但对身份验证要求不严格的场景

**不推荐使用的场景**：
- 公网环境
- 对安全要求极高的生产环境
- 需要严格身份验证的场景

## 使用示例

### 开发环境

```bash
# 快速设置加密专用TLS
make certs-enable-encryption
make build

# 服务器端
./bin/server --config configs/server.yaml

# 客户端连接任意IP
./bin/client --config configs/client.yaml --server *************
./bin/client --config configs/client.yaml --server *********
./bin/client --config configs/client.yaml --server *************
```

### 生产环境

```bash
# 为特定域名生成证书
./scripts/generate-certs.sh --server-cn "your-domain.com" --days 730

# 或者使用加密专用证书（如果IP经常变化）
make certs-enable-encryption
```

## 验证配置

验证TLS配置是否正确：

```bash
# 验证证书
make certs-verify

# 测试TLS连接
make test-tls

# 查看证书支持的IP/域名
openssl x509 -in certs/server.crt -text -noout | grep -A 10 "Subject Alternative Name"
```

## 故障排除

### 常见问题

1. **证书验证失败**
   - 确认 `tls_skip_verify: true` 在客户端配置中
   - 检查证书文件路径是否正确

2. **连接被拒绝**
   - 确认服务器已启用TLS
   - 检查防火墙设置

3. **证书过期**
   - 重新生成证书：`make certs-clean && make certs-encryption`

### 调试命令

```bash
# 查看证书详细信息
openssl x509 -in certs/server.crt -text -noout

# 测试TLS连接
openssl s_client -connect *************:8080 -CAfile certs/ca.crt

# 启用调试日志
./bin/client --config configs/client.yaml --log-level debug
```

## 总结

对于需要连接任意IP地址的场景，推荐使用加密专用证书方案：

1. **简单快速**：一条命令完成配置
2. **安全可靠**：提供数据加密保护
3. **灵活性高**：支持任意IP地址
4. **维护简单**：无需为每个IP生成证书

使用命令：
```bash
make certs-enable-encryption
```

这样配置后，客户端就可以安全地连接到任意IP地址的服务器，同时保持数据传输的加密保护。
