# TLS 证书问题排查指南

## 🚨 常见TLS错误

### 错误1: 证书IP地址不匹配
```
Error: failed to connect: tls: failed to verify certificate: 
x509: certificate is valid for 127.0.0.1, ::1, not *************
```

**原因**: 服务器证书的Subject Alternative Name (SAN) 中不包含客户端尝试连接的IP地址。

**解决方案**:
1. 重新生成包含正确IP地址的证书
2. 或者在客户端配置中设置 `tls_skip_verify: true`（不推荐用于生产环境）

### 错误2: 证书过期
```
Error: tls: certificate has expired or is not yet valid
```

**原因**: 证书已过期或尚未生效。

**解决方案**: 重新生成证书

### 错误3: 证书链验证失败
```
Error: tls: certificate signed by unknown authority
```

**原因**: 客户端无法验证服务器证书的签发机构。

**可能原因**:
1. 客户端配置中缺少 `tls_ca_file` 配置
2. CA证书文件路径错误或文件不存在
3. 客户端代码没有正确加载CA证书

**解决方案**:
1. 确保客户端配置了正确的CA证书文件
2. 检查客户端代码是否正确加载CA证书到 `RootCAs`

## 🔧 快速修复

### 方法1: 重新生成证书（推荐）
```bash
# 删除现有证书
rm -f certs/server.crt certs/server.key

# 重新运行配置脚本
./scripts/setup-single-server.sh

# 验证证书
./scripts/verify-certificates.sh
```

### 方法2: 手动生成正确的证书
```bash
# 1. 生成服务器私钥
openssl genrsa -out certs/server.key 4096

# 2. 生成证书请求
openssl req -new -key certs/server.key -out certs/server.csr \
  -subj "/C=CN/ST=Beijing/L=Beijing/O=CyberBastion/OU=Server/CN=*************"

# 3. 创建扩展文件
cat > certs/server.ext << EOF
[v3_req]
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature, nonRepudiation, keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth, clientAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = cyber-bastion
DNS.3 = *.localhost
IP.1 = 127.0.0.1
IP.2 = ::1
IP.3 = *************
EOF

# 4. 生成服务器证书
openssl x509 -req -in certs/server.csr -CA certs/ca.crt -CAkey certs/ca.key \
  -CAcreateserial -out certs/server.crt -days 365 -extensions v3_req -extfile certs/server.ext

# 5. 清理临时文件
rm certs/server.csr certs/server.ext

# 6. 设置权限
chmod 600 certs/server.key
```

### 方法3: 临时跳过证书验证（仅用于测试）
```yaml
# 在客户端配置中设置（不推荐用于生产环境）
tls_skip_verify: true
```

## 🔍 诊断工具

### 1. 证书验证脚本
```bash
./scripts/verify-certificates.sh
```

### 2. 手动检查证书内容
```bash
# 查看证书详细信息
openssl x509 -in certs/server.crt -text -noout

# 检查Subject Alternative Name
openssl x509 -in certs/server.crt -text -noout | grep -A 5 "Subject Alternative Name"

# 检查证书有效期
openssl x509 -in certs/server.crt -dates -noout

# 验证证书链
openssl verify -CAfile certs/ca.crt certs/server.crt
```

### 3. 测试TLS连接
```bash
# 测试服务器TLS连接
openssl s_client -connect *************:8080 -CAfile certs/ca.crt

# 测试本地TLS连接
openssl s_client -connect localhost:8080 -CAfile certs/ca.crt
```

## 📋 证书要求清单

### 服务器证书必须包含:
- ✅ **IP地址**: `*************` (服务器实际IP)
- ✅ **本地IP**: `127.0.0.1` (用于本地测试)
- ✅ **IPv6本地**: `::1` (IPv6本地地址)
- ✅ **域名**: `localhost` (本地域名)
- ✅ **通配符**: `*.localhost` (本地子域名)

### 证书配置要求:
- ✅ **密钥用途**: `digitalSignature, keyEncipherment`
- ✅ **扩展密钥用途**: `serverAuth, clientAuth`
- ✅ **基本约束**: `CA:FALSE`
- ✅ **有效期**: 至少1年

## 🛠️ 配置检查

### 客户端配置检查
```yaml
# configs/client.yaml
enable_tls: true
tls_cert_file: "certs/client.crt"    # 客户端证书
tls_key_file: "certs/client.key"     # 客户端私钥
tls_ca_file: "certs/ca.crt"          # CA证书
tls_skip_verify: false               # 严格验证（推荐）
```

### 服务端配置检查
```yaml
# configs/server.yaml
security:
  enable_tls: true
  tls_cert_file: "certs/server.crt"  # 服务器证书
  tls_key_file: "certs/server.key"   # 服务器私钥
  tls_ca_file: "certs/ca.crt"        # CA证书
```

## 🔄 证书更新流程

### 定期更新证书
```bash
# 1. 备份现有证书
cp -r certs certs.backup.$(date +%Y%m%d)

# 2. 重新生成证书
./scripts/setup-single-server.sh

# 3. 验证新证书
./scripts/verify-certificates.sh

# 4. 重启服务
./scripts/start-server.sh
./scripts/start-client.sh
```

## 🚨 紧急修复

### 如果服务无法启动
```bash
# 1. 检查证书文件权限
ls -la certs/

# 2. 修复权限
chmod 600 certs/*.key
chmod 644 certs/*.crt

# 3. 验证证书
./scripts/verify-certificates.sh

# 4. 如果仍有问题，重新生成所有证书
rm -rf certs/
./scripts/setup-single-server.sh
```

### 如果客户端无法连接
```bash
# 1. 检查服务器是否启动
netstat -an | grep 8080

# 2. 测试网络连通性
ping *************
telnet ************* 8080

# 3. 测试TLS连接
openssl s_client -connect *************:8080 -CAfile certs/ca.crt

# 4. 检查防火墙
sudo ufw status
```

## 📚 相关文档

- [加密配置指南](ENCRYPTION_SETUP.md)
- [单服务器设置指南](SINGLE_SERVER_SETUP.md)
- [配置文件说明](../configs/README.md)

---

**🔐 记住：正确的证书配置是TLS连接成功的关键！**
