# 数据包转发日志增强

## 概述

本次更新为client和server在转发数据包时添加了源IP和目标IP的日志记录功能，满足了用户的需求。

## 更改内容

### 1. 新增工具函数 (`pkg/tun/tun.go`)

添加了以下函数来解析数据包中的源IP地址：

- `GetIPv4SourceIP(packet []byte) net.IP` - 获取IPv4数据包的源IP地址
- `GetIPv6SourceIP(packet []byte) net.IP` - 获取IPv6数据包的源IP地址  
- `GetSourceIP(packet []byte) net.IP` - 自动检测IP版本并获取源IP地址

### 2. Server端转发日志增强 (`internal/server/server.go`)

#### IPv4转发函数更新：
- `forwardIPv4Packet()` - 添加了源IP和目标IP的日志记录
- `forwardTCP()` - 添加了源IP、源端口、目标IP、目标端口的日志记录
- `forwardUDP()` - 添加了源IP、源端口、目标IP、目标端口的日志记录
- `forwardRaw()` - 添加了源IP、目标IP、协议类型的日志记录

#### IPv6转发函数更新：
- `forwardIPv6Packet()` - 添加了源IP和目标IP的日志记录
- `forwardTCPv6()` - 添加了源IP、源端口、目标IP、目标端口的日志记录
- `forwardUDPv6()` - 添加了源IP、源端口、目标IP、目标端口的日志记录
- `forwardRawv6()` - 添加了源IP、目标IP、下一个头部类型的日志记录

### 3. Client端TUN数据处理增强 (`internal/client/client.go`)

在`tunDataLoop()`函数中：
- 添加了数据包解析逻辑，支持IPv4和IPv6
- 在发送和错误日志中都包含了源IP和目标IP信息
- 提供了更详细的数据包转发日志

### 4. 测试用例增强 (`pkg/tun/tun_test.go`)

添加了新的测试用例：
- `TestGetSourceIP()` - 测试源IP获取功能
- `TestGetIPv4SourceIP()` - 测试IPv4源IP获取
- `TestGetIPv6SourceIP()` - 测试IPv6源IP获取

## 日志示例

### Server端转发日志示例：

```json
{
  "level": "debug",
  "timestamp": "2025-06-24T23:10:41.289+0800",
  "caller": "server/server.go:1189",
  "msg": "Forwarding IPv4 packet",
  "client_id": "*************:12345",
  "src_ip": "*************",
  "dst_ip": "********",
  "packet_size": 64
}

{
  "level": "debug", 
  "timestamp": "2025-06-24T23:10:41.289+0800",
  "caller": "server/server.go:1330",
  "msg": "Forwarding TCP packet",
  "client_id": "*************:12345",
  "src_ip": "*************",
  "src_port": 54321,
  "dst_ip": "********", 
  "dst_port": 80,
  "packet_size": 64
}
```

### Client端转发日志示例：

```json
{
  "level": "debug",
  "timestamp": "2025-06-24T23:10:41.289+0800", 
  "caller": "client/client.go:757",
  "msg": "TUN packet forwarded to server",
  "size": 64,
  "src_ip": "*************",
  "dst_ip": "********"
}
```

## 技术细节

### IP地址解析

- **IPv4源地址位置**: 字节12-15
- **IPv4目标地址位置**: 字节16-19
- **IPv6源地址位置**: 字节8-23
- **IPv6目标地址位置**: 字节24-39

### 端口解析

- **TCP/UDP源端口**: 传输层头部字节0-1
- **TCP/UDP目标端口**: 传输层头部字节2-3

## 兼容性

- 所有更改都是向后兼容的
- 现有功能不受影响
- 新的日志字段为可选，不会破坏现有的日志解析

## 测试验证

- 所有现有测试用例通过
- 新增的测试用例验证了源IP解析功能
- 集成测试确认了端到端功能正常

## 使用方法

启动server和client后，在debug日志级别下可以看到详细的数据包转发信息，包括源IP和目标IP。这有助于：

1. 网络故障排查
2. 流量分析
3. 安全审计
4. 性能监控

## 注意事项

- 日志记录在debug级别，生产环境中可能需要调整日志级别
- 对于高流量场景，详细的数据包日志可能会产生大量日志输出
- 建议根据实际需求配置合适的日志级别和输出方式
