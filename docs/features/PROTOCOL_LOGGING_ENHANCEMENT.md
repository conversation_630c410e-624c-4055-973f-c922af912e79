# 协议类型日志增强

## 概述

本次更新为client和server在转发数据包时添加了协议类型的日志记录功能，满足了用户在数据转发时需要体现协议类型的需求。

## 更改内容

### 1. 新增协议解析工具函数 (`pkg/tun/tun.go`)

添加了以下函数来解析和识别数据包中的协议类型：

#### 协议提取函数
- `GetIPv4Protocol(packet []byte) uint8` - 获取IPv4数据包的协议类型
- `GetIPv6NextHeader(packet []byte) uint8` - 获取IPv6数据包的下一个头部类型
- `GetProtocol(packet []byte) uint8` - 自动检测IP版本并获取协议类型

#### 协议名称转换函数
- `ProtocolToString(protocol uint8, isIPv6 bool) string` - 将协议号转换为可读的协议名称
- `GetProtocolString(packet []byte) string` - 获取数据包的协议名称（自动检测IPv4/IPv6）

#### 支持的协议类型

**IPv4 协议:**
- ICMP (1)
- IGMP (2) 
- IPv4 (4)
- TCP (6)
- UDP (17)
- IPv6 (41)
- GRE (47)
- ESP (50)
- AH (51)
- OSPF (89)
- SCTP (132)
- 未知协议显示为 "Unknown-IPv4-{number}"

**IPv6 下一个头部类型:**
- IPv6-Hop-by-Hop (0)
- ICMP (1)
- IGMP (2)
- IPv4 (4)
- TCP (6)
- UDP (17)
- IPv6 (41)
- IPv6-Route (43)
- IPv6-Frag (44)
- ICMPv6 (58)
- IPv6-NoNxt (59)
- IPv6-Opts (60)
- 未知协议显示为 "Unknown-IPv6-{number}"

### 2. Client端TUN数据处理增强 (`internal/client/client.go`)

#### 发送数据包日志增强
在`tunDataLoop()`函数中：
- 使用新的工具函数解析数据包
- 在发送和错误日志中都包含了协议类型信息
- 提供了更详细的数据包转发日志

#### 接收响应数据包日志增强
在`handleTunResponse()`函数中：
- 解析响应数据包的协议类型
- 在接收、错误和成功日志中都包含协议信息

### 3. Server端转发日志增强 (`internal/server/server.go`)

#### 主要TUN数据处理增强
在`handleTunData()`函数中：
- 添加了数据包解析逻辑，获取源IP、目标IP和协议类型
- 在接收日志中包含完整的数据包信息

#### IPv4转发函数更新
- `forwardIPv4Packet()` - 添加了协议类型的日志记录
- `forwardIPv4PacketWithResponse()` - 添加了协议类型的日志记录

#### IPv6转发函数更新
- `forwardIPv6Packet()` - 添加了下一个头部类型的日志记录
- `forwardIPv6PacketWithResponse()` - 添加了下一个头部类型的日志记录

### 4. 测试用例增强 (`pkg/tun/tun_test.go`)

添加了新的测试用例：
- `TestGetIPv4Protocol()` - 测试IPv4协议类型获取
- `TestGetIPv6NextHeader()` - 测试IPv6下一个头部类型获取
- `TestGetProtocol()` - 测试自动协议类型检测
- `TestProtocolToString()` - 测试协议号到字符串转换
- `TestGetProtocolString()` - 测试数据包协议字符串获取

## 日志示例

### Client端转发日志示例

**发送数据包到服务器:**
```json
{
  "level": "debug",
  "timestamp": "2025-06-25T06:45:10.413+0800",
  "caller": "client/client.go:811",
  "msg": "TUN packet forwarded to server",
  "size": 84,
  "src_ip": "********",
  "dst_ip": "*************",
  "protocol": "TCP"
}
```

**接收响应数据包:**
```json
{
  "level": "debug",
  "timestamp": "2025-06-25T06:45:10.477+0800",
  "caller": "client/client.go:548",
  "msg": "Received TUN response packet",
  "message_id": "tun-1750805110413684894",
  "packet_length": 104,
  "src_ip": "*************",
  "dst_ip": "********",
  "protocol": "TCP"
}
```

### Server端转发日志示例

**接收TUN数据包:**
```json
{
  "level": "debug",
  "timestamp": "2025-06-25T06:45:10.413+0800",
  "caller": "server/server.go:1165",
  "msg": "Received TUN data packet",
  "client_id": "*************:12345",
  "message_id": "tun-1750805110413684894",
  "packet_length": 84,
  "src_ip": "********",
  "dst_ip": "*************",
  "protocol": "TCP"
}
```

**转发IPv4数据包:**
```json
{
  "level": "debug",
  "timestamp": "2025-06-25T06:45:10.413+0800",
  "caller": "server/server.go:1254",
  "msg": "Forwarding IPv4 packet",
  "client_id": "*************:12345",
  "src_ip": "********",
  "dst_ip": "*************",
  "packet_size": 84,
  "protocol": "TCP"
}
```

**转发IPv6数据包:**
```json
{
  "level": "debug",
  "timestamp": "2025-06-25T06:45:10.413+0800",
  "caller": "server/server.go:1294",
  "msg": "Forwarding IPv6 packet",
  "client_id": "*************:12345",
  "src_ip": "2001:db8::1",
  "dst_ip": "2001:db8::2",
  "packet_size": 84,
  "protocol": "ICMPv6"
}
```

## 兼容性

- 所有现有功能保持不变
- 新增的协议解析功能不影响现有的数据包转发逻辑
- 日志格式向后兼容，只是增加了新的字段
- 所有测试用例通过，确保功能稳定性

## 使用方法

协议类型信息会自动包含在相关的调试日志中，无需额外配置。用户可以通过查看日志来了解：

1. 数据包的源IP和目标IP地址
2. 数据包的协议类型（TCP、UDP、ICMP、ICMPv6等）
3. 数据包的大小
4. 转发的方向（客户端到服务器，服务器响应到客户端）

这些信息有助于网络调试、性能分析和安全监控。
