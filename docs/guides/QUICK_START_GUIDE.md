# 优化转发处理器快速启动指南

## 🚀 快速开始

### 1. 编译程序
```bash
# 编译server和client
go build -o bin/server ./cmd/server
go build -o bin/client ./cmd/client
```

### 2. 启动服务
```bash
# 启动server (需要sudo权限用于原始套接字)
sudo ./bin/server --config configs/server.yaml --log-level debug

# 在另一个终端启动client
sudo ./bin/client --config configs/client.yaml --log-level debug
```

### 3. 验证功能
```bash
# 测试ICMP转发
ping -c 3 *******

# 测试HTTP连接
curl -v http://httpbin.org/ip

# 测试HTTPS连接
curl -v https://httpbin.org/ip
```

### 4. 监控性能
```bash
# 查看转发指标
curl http://localhost:8080/metrics/forwarding

# 查看详细状态
curl http://localhost:8080/status/forwarding
```

## 🔧 测试脚本

### 基本功能测试
```bash
# 运行编译和基本功能测试（不需要sudo）
./test_compilation_only.sh

# 运行完整系统测试（需要sudo）
sudo ./test_integrated_system.sh
```

### 转发功能测试
```bash
# 测试转发功能（需要sudo）
sudo ./test_forwarding_functionality.sh
```

## 📊 性能监控

### 关键指标
- **吞吐量**: 每秒处理的数据包数量
- **延迟**: 数据包处理的平均延迟
- **错误率**: 转发失败的百分比
- **队列利用率**: 处理队列的使用情况
- **连接池利用率**: 连接池的使用效率

### 监控端点
```bash
# 实时指标
curl http://localhost:8080/metrics/forwarding | jq

# 详细状态
curl http://localhost:8080/status/forwarding | jq
```

## ⚙️ 配置调优

### 性能参数
```yaml
# configs/server.yaml 中添加
forwarding:
  workers: 10                    # 工作协程数 (建议: CPU核心数 × 2)
  queue_size: 1000              # 队列大小
  max_idle_connections: 50      # 最大空闲连接数
  max_active_connections: 200   # 最大活跃连接数
```

### 调优建议
- **高并发场景**: 增加workers和queue_size
- **内存受限**: 减少连接池大小
- **低延迟要求**: 减少队列大小，增加workers
- **高吞吐量**: 增加连接池大小和workers

## 🐛 故障排查

### 常见问题

#### 1. 权限错误
```
Error: socket: operation not permitted
```
**解决方案**: 使用sudo运行程序

#### 2. 连接失败
```
Error: failed to create raw socket
```
**解决方案**: 检查网络连通性和防火墙设置

#### 3. 性能问题
```
Warning: queue utilization > 80%
```
**解决方案**: 增加workers数量或queue_size

### 调试命令
```bash
# 查看详细日志
sudo ./bin/server --config configs/server.yaml --log-level debug

# 监控网络流量
sudo tcpdump -i cyber-tun -n

# 检查进程状态
ps aux | grep -E "(server|client)"

# 检查TUN接口
ip addr show cyber-tun
```

## 📈 性能基准

### 预期性能
- **ICMP转发**: > 1000 pps
- **TCP连接**: > 500 连接/秒
- **HTTP请求**: > 2000 req/秒
- **平均延迟**: < 50ms
- **错误率**: < 1%

### 基准测试
```bash
# 运行性能基准测试
./test_performance_benchmark.sh

# 并发连接测试
for i in {1..100}; do curl -s http://httpbin.org/ip & done; wait
```

## 🔄 升级和回滚

### 升级到优化版本
1. 停止现有服务
2. 备份配置文件
3. 部署新版本
4. 更新配置（添加forwarding部分）
5. 启动服务并验证

### 回滚到传统版本
1. 在配置中设置 `use_legacy_forwarding: true`
2. 重启服务
3. 验证功能正常

## 📞 支持和帮助

### 日志位置
- Server日志: `server.log` 或 systemd journal
- Client日志: `client.log` 或 systemd journal
- 测试日志: `*_test.log` 文件

### 重要文件
- 配置文件: `configs/server.yaml`, `configs/client.yaml`
- 二进制文件: `bin/server`, `bin/client`
- 测试脚本: `test_*.sh`
- 文档: `*.md` 文件

### 获取帮助
```bash
# 查看帮助信息
./bin/server --help
./bin/client --help

# 查看版本信息
./bin/server --version
./bin/client --version
```

## 🎯 最佳实践

### 生产环境部署
1. **资源规划**: 根据预期负载配置合适的参数
2. **监控设置**: 配置监控和告警
3. **日志管理**: 设置日志轮转和归档
4. **备份策略**: 定期备份配置和证书
5. **更新策略**: 制定升级和回滚计划

### 安全考虑
1. **权限控制**: 最小权限原则
2. **网络隔离**: 适当的网络分段
3. **证书管理**: 定期更新TLS证书
4. **日志审计**: 记录关键操作

### 性能优化
1. **定期监控**: 关注关键性能指标
2. **容量规划**: 根据增长趋势调整资源
3. **配置调优**: 根据实际负载优化参数
4. **版本更新**: 及时更新到最新版本

这个快速启动指南提供了从安装到运维的完整流程，帮助用户快速上手并有效使用优化转发处理器。
