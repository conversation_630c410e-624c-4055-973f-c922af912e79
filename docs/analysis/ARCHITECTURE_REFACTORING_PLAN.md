# 架构重构规划

## 新架构与现有代码对应关系分析

### 客户端模块映射

| 新架构组件 | 现有代码模块 | 重构需求 |
|-----------|-------------|----------|
| **C0: 配置加载** | `pkg/config/config.go` | ✅ 基本完整，需扩展多隧道配置 |
| **C1: 控制模块** | `internal/client/client.go` | 🔄 需重构为独立控制模块 |
| **C2: 路由策略模块** | 🆕 新增 | ❌ 需要全新实现 |
| **C3: TUN虚拟网卡** | `pkg/tun/` | ✅ 基本完整，需优化 |
| **C4: 核心转发器** | `internal/client/client.go` (部分) | 🔄 需重构为独立转发器 |
| **C5: 加密传输层** | `pkg/protocol/` | 🔄 需扩展支持多协议 |

### 服务端模块映射

| 新架构组件 | 现有代码模块 | 重构需求 |
|-----------|-------------|----------|
| **S1: 服务端监听器** | `internal/server/server.go` | ✅ 基本完整，需扩展多协议支持 |
| **S2: 转发逻辑** | `internal/forwarding/` + 优化转发系统 | ✅ 已完成，需集成 |
| **S3: NAT模块/TUN发包** | `internal/server/server.go` (部分) | 🔄 需重构为独立模块 |
| **S4: 网络出口** | 🆕 新增 | ❌ 需要全新实现 |
| **S5: 目标服务器** | 外部系统 | ✅ 无需修改 |

### 传输通道

| 新架构组件 | 现有代码模块 | 重构需求 |
|-----------|-------------|----------|
| **多隧道连接** | 🆕 新增 | ❌ 需要全新实现 |
| **WebSocket支持** | 🆕 新增 | ❌ 需要全新实现 |
| **QUIC支持** | 🆕 新增 | ❌ 需要全新实现 |

## 详细重构计划

### 阶段1: 客户端模块重构

#### 1.1 配置系统扩展
- **目标**: 支持多隧道配置、路由策略配置
- **文件**: `pkg/config/config.go`
- **新增配置项**:
  ```yaml
  tunnels:
    - name: "tunnel1"
      server: "vpn1.server.com"
      protocol: "websocket"
      priority: 1
    - name: "tunnel2" 
      server: "vpn2.server.com"
      protocol: "tcp-tls"
      priority: 2
  
  routing:
    strategy: "load_balance" # round_robin, failover, load_balance
    rules:
      - destination: "10.0.0.0/8"
        action: "tunnel"
      - destination: "192.168.0.0/16"
        action: "direct"
  ```

#### 1.2 控制模块重构
- **目标**: 独立的连接管理、状态监控、重连逻辑
- **新文件**: `internal/client/controller/`
- **功能**:
  - 多隧道连接管理
  - 健康检查和故障转移
  - 连接状态监控
  - 智能重连策略

#### 1.3 路由策略模块
- **目标**: 决定哪些流量走TUN，使用哪个隧道
- **新文件**: `internal/client/routing/`
- **功能**:
  - 路由规则解析
  - 流量分类
  - 隧道选择策略
  - 负载均衡

#### 1.4 核心转发器重构
- **目标**: 独立的数据包转发逻辑
- **新文件**: `internal/client/forwarder/`
- **功能**:
  - TUN数据包处理
  - 协议识别
  - 隧道选择
  - 数据包封装

#### 1.5 加密传输层扩展
- **目标**: 支持多种传输协议
- **文件**: `pkg/transport/`
- **新增协议**:
  - WebSocket传输
  - QUIC传输
  - 改进的TCP-TLS传输

### 阶段2: 传输通道优化

#### 2.1 传输协议抽象
- **新文件**: `pkg/transport/interface.go`
- **接口设计**:
  ```go
  type Transport interface {
      Connect(addr string) error
      Send(data []byte) error
      Receive() ([]byte, error)
      Close() error
      IsConnected() bool
  }
  ```

#### 2.2 WebSocket传输实现
- **新文件**: `pkg/transport/websocket.go`
- **功能**: WebSocket客户端/服务端实现

#### 2.3 QUIC传输实现
- **新文件**: `pkg/transport/quic.go`
- **功能**: QUIC客户端/服务端实现

#### 2.4 多隧道管理器
- **新文件**: `internal/client/tunnel/manager.go`
- **功能**:
  - 隧道池管理
  - 负载均衡
  - 故障转移
  - 性能监控

### 阶段3: 服务端模块重构

#### 3.1 监听器扩展
- **文件**: `internal/server/listener/`
- **功能**:
  - 多协议监听
  - 连接分发
  - 协议识别

#### 3.2 NAT模块独立
- **新文件**: `internal/server/nat/`
- **功能**:
  - IP地址转换
  - 端口映射
  - 连接跟踪

#### 3.3 网络出口管理
- **新文件**: `internal/server/egress/`
- **功能**:
  - 出口接口选择
  - 路由表管理
  - 流量统计

### 阶段4: 优化转发系统集成

#### 4.1 转发引擎集成
- **目标**: 将已完成的优化转发系统集成到新架构
- **文件**: `internal/forwarding/` (已完成)
- **集成点**:
  - 服务端转发逻辑 (S2)
  - 地址映射缓存
  - 高性能数据包处理

#### 4.2 性能监控集成
- **目标**: 统一的性能监控和指标收集
- **新文件**: `internal/monitoring/`
- **功能**:
  - 转发性能指标
  - 隧道性能指标
  - 系统资源监控

### 阶段5: 配置系统升级

#### 5.1 配置结构扩展
- **文件**: `pkg/config/`
- **新增配置**:
  - 多隧道配置
  - 路由策略配置
  - 传输协议配置
  - 性能调优配置

#### 5.2 配置验证
- **功能**:
  - 配置语法验证
  - 配置兼容性检查
  - 配置热重载

### 阶段6: 测试和验证

#### 6.1 单元测试
- **覆盖范围**:
  - 所有新增模块
  - 重构模块
  - 集成接口

#### 6.2 集成测试
- **测试场景**:
  - 多隧道连接
  - 故障转移
  - 负载均衡
  - 性能测试

#### 6.3 端到端测试
- **测试环境**:
  - 模拟真实网络环境
  - 多种传输协议
  - 各种故障场景

## 实施优先级

### 高优先级 (P0)
1. **配置系统扩展** - 基础设施
2. **控制模块重构** - 核心功能
3. **优化转发系统集成** - 性能保证

### 中优先级 (P1)
4. **路由策略模块** - 新功能
5. **传输协议扩展** - 功能增强
6. **NAT模块独立** - 架构优化

### 低优先级 (P2)
7. **QUIC支持** - 高级功能
8. **网络出口管理** - 高级功能
9. **性能监控集成** - 运维功能

## 风险评估

### 高风险
- **配置兼容性**: 新配置格式可能破坏现有部署
- **性能回归**: 重构可能影响现有性能

### 中风险
- **功能完整性**: 重构过程中可能遗漏功能
- **测试覆盖**: 新功能测试可能不充分

### 低风险
- **代码复杂性**: 新架构可能增加维护复杂性

## 迁移策略

### 渐进式迁移
1. **保持向后兼容**: 新版本支持旧配置格式
2. **功能开关**: 通过配置开关启用新功能
3. **灰度发布**: 逐步启用新功能模块

### 回滚计划
1. **配置回滚**: 支持快速回滚到旧配置
2. **功能降级**: 可以禁用新功能回到旧逻辑
3. **数据备份**: 重要状态数据的备份和恢复

这个重构计划将确保系统按照新架构图进行重构，同时保留已完成的优化转发系统，并提供平滑的迁移路径。
