# Transparent Forwarding

Cyber Bastion supports transparent packet forwarding using TUN interfaces, enabling it to intercept and forward network traffic of all protocols through encrypted channels.

## Overview

The transparent forwarding feature allows Cyber Bastion to:

1. **Client Side**: Create a TUN interface to capture network packets
2. **Encrypted Transport**: Forward captured packets through the existing encrypted channel
3. **Server Side**: Receive and forward packets to their original destinations
4. **Protocol Support**: Handle all IP protocols (TCP, UDP, ICMP, etc.) for both IPv4 and IPv6

## Architecture

```
[Application] → [TUN Interface] → [Client] → [Encrypted Channel] → [Server] → [Target Destination]
```

### Client Side Flow

1. Application sends packets to TUN interface
2. Client reads packets from TUN interface
3. <PERSON>lient encapsulates packets in TUN data messages
4. <PERSON>lient sends encrypted messages to server

### Server Side Flow

1. Server receives TUN data messages from client
2. Server decrypts and extracts original packets
3. Server forwards packets to destination using raw sockets
4. Server applies filtering rules based on configuration

## Configuration

### Client Configuration

```yaml
# configs/client.yaml
tun:
  enabled: true              # Enable transparent forwarding
  name: "cyber-tun"          # TUN interface name
  ip: "********"            # TUN interface IP address
  netmask: "*************"   # TUN interface netmask
  mtu: 1500                  # TUN interface MTU size
```

### Server Configuration

```yaml
# configs/server.yaml
forwarding:
  enabled: true              # Enable transparent packet forwarding
  bind_interface: false      # Bind forwarding to specific network interface
  interface_name: ""         # Network interface name (empty = use default routing)
  allowed_networks: []       # Allowed destination networks (empty = allow all)
    # Examples:
    # - "***********/16"     # Allow private networks
    # - "10.0.0.0/8"         # Allow private networks
    # - "0.0.0.0/0"          # Allow all (not recommended)
  blocked_networks:          # Blocked destination networks
    - "*********/8"         # Block localhost
    - "***********/16"      # Block link-local addresses
    # Additional examples:
    # - "*********/4"        # Block multicast
    # - "240.0.0.0/4"        # Block reserved
```

## Usage

### Prerequisites

- **Root Privileges**: TUN interface creation requires root/administrator privileges
- **Network Configuration**: Proper routing configuration to direct traffic through TUN interface
- **Firewall Rules**: Appropriate firewall configuration on both client and server

### Basic Setup

1. **Configure the server**:
```bash
# Edit server configuration
vim configs/server.yaml

# Enable forwarding
forwarding:
  enabled: true
```

2. **Configure the client**:
```bash
# Edit client configuration
vim configs/client.yaml

# Enable TUN interface
tun:
  enabled: true
  ip: "********"
```

3. **Start the server**:
```bash
./bin/server --config configs/server.yaml
```

4. **Start the client** (requires root):
```bash
sudo ./bin/client --config configs/client.yaml
```

### Routing Configuration

#### Linux

```bash
# IPv4 routes
# Add route for specific network
sudo ip route add *************/24 dev cyber-tun

# Add default route (careful!)
sudo ip route add default dev cyber-tun table 100
sudo ip rule add from ******** table 100

# IPv6 routes
# Add IPv6 route for specific network
sudo ip -6 route add 2001:db8::/32 dev cyber-tun

# Add IPv6 default route (careful!)
sudo ip -6 route add default dev cyber-tun table 100
sudo ip -6 rule add from 2001:db8::1 table 100

# Set TUN interface as default gateway for specific applications
sudo ip netns add tunnel
sudo ip link set cyber-tun netns tunnel
sudo ip netns exec tunnel ip addr add ********/24 dev cyber-tun
sudo ip netns exec tunnel ip -6 addr add 2001:db8::1/64 dev cyber-tun
sudo ip netns exec tunnel ip link set cyber-tun up
sudo ip netns exec tunnel ip route add default dev cyber-tun
sudo ip netns exec tunnel ip -6 route add default dev cyber-tun
```

#### macOS

```bash
# Add route for specific network
sudo route add -net *************/24 -interface cyber-tun

# Add default route (careful!)
sudo route add default -interface cyber-tun
```

#### Windows

```cmd
# Add route for specific network
route add ************* mask ************* ******** if [interface_index]

# Add default route (careful!)
route add 0.0.0.0 mask 0.0.0.0 ******** if [interface_index]
```

## Protocol Support

### Supported Protocols

#### IPv4 Protocols
- **ICMP**: Internet Control Message Protocol (ping, traceroute)
- **TCP**: Transmission Control Protocol (HTTP, HTTPS, SSH, etc.)
- **UDP**: User Datagram Protocol (DNS, DHCP, etc.)
- **Raw IP**: Other IPv4 protocols

#### IPv6 Protocols
- **ICMPv6**: Internet Control Message Protocol version 6 (ping6, traceroute6)
- **TCP over IPv6**: Transmission Control Protocol over IPv6
- **UDP over IPv6**: User Datagram Protocol over IPv6
- **Raw IPv6**: Other IPv6 protocols

### Protocol-Specific Handling

#### ICMP
- Uses raw sockets for ICMP packet forwarding
- Supports ping, traceroute, and other ICMP utilities

#### TCP
- Establishes connections to destination servers
- Forwards TCP payload data
- Handles connection state management

#### UDP
- Uses UDP sockets for packet forwarding
- Supports connectionless UDP applications

## Security Considerations

### Network Filtering

Configure `allowed_networks` and `blocked_networks` to control forwarding:

```yaml
forwarding:
  # Allow only specific networks
  allowed_networks:
    - "***********/16"      # Private networks
    - "10.0.0.0/8"          # Private networks
    - "*******/32"          # Specific DNS server
  
  # Block dangerous networks
  blocked_networks:
    - "*********/8"         # Localhost
    - "***********/16"      # Link-local
    - "*********/4"         # Multicast
    - "240.0.0.0/4"         # Reserved
```

### Firewall Configuration

#### Server Side (iptables example)

```bash
# Allow forwarded traffic
iptables -A FORWARD -i eth0 -o eth1 -j ACCEPT
iptables -A FORWARD -i eth1 -o eth0 -m state --state RELATED,ESTABLISHED -j ACCEPT

# Enable NAT if needed
iptables -t nat -A POSTROUTING -o eth0 -j MASQUERADE
```

### Privilege Management

- **Client**: Requires root privileges for TUN interface creation
- **Server**: May require privileges for raw socket creation
- **Recommendation**: Use capability-based permissions where possible

```bash
# Grant specific capabilities instead of full root
sudo setcap cap_net_admin+ep ./bin/client
sudo setcap cap_net_raw+ep ./bin/server
```

## Troubleshooting

### Common Issues

1. **Permission Denied**: Ensure root privileges for TUN interface creation
2. **Interface Creation Failed**: Check if TUN/TAP driver is available
3. **Packet Not Forwarded**: Verify routing configuration and firewall rules
4. **Connection Refused**: Check server forwarding configuration

### Debug Mode

Enable debug logging for detailed packet forwarding information:

```bash
# Server debug mode
./bin/server --config configs/server.yaml --log-level debug

# Client debug mode
sudo ./bin/client --config configs/client.yaml --log-level debug
```

### Monitoring

Monitor TUN interface traffic:

```bash
# Linux
sudo tcpdump -i cyber-tun

# Monitor forwarded packets on server
sudo tcpdump -i any host [destination_ip]
```

## Performance Considerations

- **MTU Size**: Configure appropriate MTU to avoid fragmentation
- **Buffer Sizes**: Adjust buffer sizes for high-throughput scenarios
- **CPU Usage**: Monitor CPU usage during high packet rates
- **Memory Usage**: Monitor memory usage for packet buffering

## Limitations

- **Raw Sockets**: Server requires raw socket permissions for some protocols
- **Platform Differences**: TUN interface behavior varies across platforms
- **Performance**: Additional overhead due to userspace packet processing
