# 单服务器环境配置指南

## 📋 服务器信息
- **服务器IP**: `*************`
- **主要端口**: `8080` (TCP)
- **WebSocket端口**: `8081` (WebSocket over TLS)
- **备用端口**: `443` (TCP over TLS)

## 🚀 快速开始

### 1. 自动配置（推荐）
```bash
# 运行自动配置脚本
./scripts/setup-single-server.sh
```

这个脚本会自动：
- ✅ 创建必要的目录结构
- ✅ 复制适合单服务器的配置文件
- ✅ 生成测试用的自签名证书
- ✅ 编译服务端和客户端程序
- ✅ 生成启动脚本

### 2. 验证配置
```bash
# 验证配置是否正确
./scripts/verify-config.sh
```

### 3. 启动服务
```bash
# 在服务器上启动服务端
./scripts/start-server.sh

# 在客户端上启动客户端
./scripts/start-client.sh
```

## 📁 配置文件说明

### 已调整的配置文件

#### 1. `configs/client-single-server.yaml`
- **服务器地址**: 已设置为 `*************`
- **隧道配置**: 
  - 主隧道: TCP 8080 (优先级1)
  - 备用隧道: WebSocket 8081 (优先级2)
- **路由策略**: 优先级策略，内网流量走隧道

#### 2. `configs/server-single.yaml`
- **监听地址**: `0.0.0.0` (监听所有接口)
- **端口配置**: 8080 (TCP), 8081 (WebSocket)
- **NAT配置**: 服务器IP设置为 `*************`
- **性能优化**: 适合单服务器环境的配置

#### 3. `configs/client-v2.yaml` 和 `configs/server-v2.yaml`
- **已更新**: 所有服务器地址都指向 `*************`
- **端口分配**: 合理分配不同协议的端口

## 🔧 配置调整详情

### 客户端配置调整
```yaml
# 基本连接配置
server_host: "*************"  # ✅ 已更新
server_port: 8080

# 多隧道配置
tunnels:
  - name: "primary"
    server: "*************"    # ✅ 已更新
    port: 8080
    protocol: "tcp"
    priority: 1
    enabled: true

  - name: "websocket"
    server: "*************"    # ✅ 已更新
    port: 8081                 # ✅ 使用不同端口
    protocol: "websocket"
    priority: 2
    enabled: true
```

### 服务端配置调整
```yaml
# NAT配置
nat:
  server_ip: "*************"  # ✅ 已更新为实际服务器IP

# 监听器配置
listeners:
  - protocol: "tcp"
    address: "0.0.0.0"        # ✅ 监听所有接口
    port: 8080

  - protocol: "websocket"
    address: "0.0.0.0"        # ✅ 监听所有接口
    port: 8081                # ✅ WebSocket专用端口
```

### 路由策略调整
```yaml
routing:
  strategy: "priority"        # ✅ 适合单服务器的策略
  rules:
    - name: "internal_networks"
      destination: "10.0.0.0/8"
      action: "tunnel"
      tunnel: "primary"       # ✅ 使用主隧道
      priority: 10
```

## 🔐 安全配置

### 证书配置
自动生成的证书包含：
- **服务器证书**: 支持IP地址 `*************`
- **客户端证书**: 用于双向认证
- **CA证书**: 用于证书验证

### 生产环境建议
1. **替换测试证书**: 使用正式的SSL证书
2. **修改认证令牌**: 更改 `auth_token` 和 `encryption_key`
3. **配置防火墙**: 只开放必要的端口 (8080, 8081)
4. **启用日志**: 监控连接和异常情况

## 🌐 网络配置

### 端口规划
| 端口 | 协议 | 用途 | 状态 |
|------|------|------|------|
| 8080 | TCP | 主要数据传输 | ✅ 启用 |
| 8081 | WebSocket | 备用通道/穿越防火墙 | ✅ 启用 |
| 443 | TCP | 备用端口 | ⚠️ 可选 |

### 防火墙配置
```bash
# 开放必要端口
sudo ufw allow 8080/tcp
sudo ufw allow 8081/tcp

# 可选：开放443端口
sudo ufw allow 443/tcp
```

## 📊 性能优化

### 单服务器优化配置
```yaml
# 优化转发处理器
optimized_forwarding:
  worker_count: 4           # 根据CPU核心数调整
  queue_size: 1000
  max_active_conns: 100

# 性能调优
performance:
  max_open_files: 65536
  tcp_keepalive: true
  tcp_nodelay: true
```

## 🔍 故障排除

### 常见问题

#### 1. 连接失败
```bash
# 检查服务器是否可达
ping *************

# 检查端口是否开放
nc -z ************* 8080
nc -z ************* 8081
```

#### 2. 证书错误
```bash
# 重新生成证书
rm -rf certs/
./scripts/setup-single-server.sh
```

#### 3. 配置验证
```bash
# 运行配置验证脚本
./scripts/verify-config.sh
```

### 日志查看
```bash
# 查看服务端日志
./bin/cyber-bastion-server -config configs/server.yaml -log-level debug

# 查看客户端日志
./bin/cyber-bastion-client -config configs/client.yaml -log-level debug
```

## 📈 监控和维护

### 健康检查
```bash
# 检查服务状态
curl -k https://*************:8080/health

# 检查WebSocket连接
curl -k -H "Upgrade: websocket" https://*************:8081/ws
```

### 性能监控
- **连接数**: 监控活跃连接数量
- **延迟**: 监控网络延迟
- **吞吐量**: 监控数据传输速率
- **错误率**: 监控连接失败率

## 🎯 下一步

1. **部署到生产环境**:
   - 使用正式SSL证书
   - 配置系统服务
   - 设置自动启动

2. **扩展功能**:
   - 添加更多隧道
   - 配置负载均衡
   - 实现高可用

3. **安全加固**:
   - 定期更新证书
   - 监控异常访问
   - 配置入侵检测

## 📚 相关文档

- [配置文件详细说明](configs/README.md)
- [架构重构总结](ARCHITECTURE_REFACTORING_SUMMARY.md)
- [优化转发集成计划](analysis/OPTIMIZED_FORWARDING_INTEGRATION_PLAN.md)

---

**✅ 配置完成！您的单服务器环境已准备就绪。**
