# 加密状态日志功能

## 概述

Cyber Bastion 在启动时会在日志中清楚地显示加密配置状态，帮助管理员确认安全设置是否正确配置。

## 功能特性

### 1. 启动日志增强
- 显示TLS状态（`tls_enabled`）
- 显示消息级加密状态（`encryption_enabled`）
- 显示加密算法类型（`encryption_type`）
- 提供密钥安全提示

### 2. 密钥安全检查
- 检测默认密钥使用情况
- 对默认密钥发出安全警告
- 对自定义密钥显示确认信息
- 显示密钥提示（前几个字符）

## 配置参数

### 服务器配置 (server.yaml)
```yaml
security:
  # TLS传输层加密
  enable_tls: true
  tls_cert_file: "certs/server.crt"
  tls_key_file: "certs/server.key"
  tls_ca_file: "certs/ca.crt"
  
  # 消息级加密
  enable_encryption: true
  encryption_key: "change-this-encryption-key-in-production-32-bytes"
```

### 客户端配置 (client.yaml)
```yaml
# TLS传输层加密
enable_tls: true
tls_cert_file: "certs/client.crt"
tls_key_file: "certs/client.key"
tls_ca_file: "certs/ca.crt"

# 消息级加密
enable_encryption: true
encryption_key: "change-this-encryption-key-in-production-32-bytes"
```

## 日志示例

### 场景1：加密启用，默认密钥

**服务器启动日志**：
```json
{
  "level": "warn",
  "timestamp": "2025-06-24T21:08:12.838+0800",
  "caller": "server/main.go:94",
  "msg": "Using default encryption key - CHANGE THIS IN PRODUCTION!",
  "key_hint": "change-this-encr..."
}
{
  "level": "info",
  "timestamp": "2025-06-24T21:08:12.839+0800",
  "caller": "server/main.go:104",
  "msg": "Starting Cyber Bastion Server",
  "version": "1.0.0",
  "host": "0.0.0.0",
  "port": 8080,
  "log_level": "info",
  "tls_enabled": true,
  "encryption_enabled": true,
  "encryption_type": "AES-256-GCM"
}
```

**客户端启动日志**：
```json
{
  "level": "warn",
  "timestamp": "2025-06-24T21:08:12.838+0800",
  "caller": "client/main.go:98",
  "msg": "Using default encryption key - CHANGE THIS IN PRODUCTION!",
  "key_hint": "change-this-encr..."
}
{
  "level": "info",
  "timestamp": "2025-06-24T21:08:12.839+0800",
  "caller": "client/main.go:108",
  "msg": "Starting Cyber Bastion Client",
  "version": "1.0.0",
  "server": "localhost:8080",
  "log_level": "info",
  "tls_enabled": true,
  "encryption_enabled": true,
  "encryption_type": "AES-256-GCM"
}
```

### 场景2：加密禁用

**启动日志**：
```json
{
  "level": "info",
  "timestamp": "2025-06-24T21:08:12.839+0800",
  "caller": "server/main.go:104",
  "msg": "Starting Cyber Bastion Server",
  "version": "1.0.0",
  "host": "0.0.0.0",
  "port": 8080,
  "log_level": "info",
  "tls_enabled": true,
  "encryption_enabled": false
}
```

### 场景3：加密启用，自定义密钥

**启动日志**：
```json
{
  "level": "info",
  "timestamp": "2025-06-24T21:08:12.838+0800",
  "caller": "server/main.go:97",
  "msg": "Custom encryption key configured",
  "key_hint": "my-custo..."
}
{
  "level": "info",
  "timestamp": "2025-06-24T21:08:12.839+0800",
  "caller": "server/main.go:104",
  "msg": "Starting Cyber Bastion Server",
  "version": "1.0.0",
  "host": "0.0.0.0",
  "port": 8080,
  "log_level": "info",
  "tls_enabled": true,
  "encryption_enabled": true,
  "encryption_type": "AES-256-GCM"
}
```

## 日志字段说明

### 主要状态字段
- `tls_enabled`: TLS传输层加密状态
- `encryption_enabled`: 消息级加密状态
- `encryption_type`: 加密算法类型（AES-256-GCM）

### 安全提示字段
- `key_hint`: 加密密钥提示（显示前几个字符）
- `msg`: 安全警告或确认消息

## 安全建议

### 1. 默认密钥警告
当使用默认密钥时，系统会显示警告：
```
"Using default encryption key - CHANGE THIS IN PRODUCTION!"
```

**建议**：
- 在生产环境中必须更换默认密钥
- 使用32字节长度的强密钥
- 定期轮换加密密钥

### 2. 自定义密钥确认
当使用自定义密钥时，系统会显示确认：
```
"Custom encryption key configured"
```

### 3. 密钥管理最佳实践
- 使用环境变量存储密钥
- 不要在日志中记录完整密钥
- 使用密钥管理系统
- 定期审计密钥使用情况

## 监控和告警

### 1. 日志监控
监控以下关键日志消息：
- `Using default encryption key` - 默认密钥警告
- `encryption_enabled: false` - 加密禁用
- `tls_enabled: false` - TLS禁用

### 2. 告警规则
建议设置以下告警：
- 生产环境使用默认密钥
- 加密功能意外禁用
- TLS功能意外禁用

## 故障排除

### 常见问题

1. **加密状态不匹配**
   - 检查客户端和服务器配置文件
   - 确保`enable_encryption`设置一致
   - 确保`encryption_key`设置一致

2. **默认密钥警告**
   - 更新配置文件中的`encryption_key`
   - 使用32字节长度的自定义密钥
   - 重启应用以应用新配置

3. **日志中未显示加密状态**
   - 检查日志级别设置
   - 确保使用最新版本的应用
   - 检查配置文件格式

### 调试命令
```bash
# 检查服务器启动日志
./bin/server --config configs/server.yaml --log-level info | grep -E "(encryption|tls)"

# 检查客户端启动日志
./bin/client --config configs/client.yaml --log-level info | grep -E "(encryption|tls)"

# 验证配置文件
cat configs/server.yaml | grep -A2 -B2 encryption
cat configs/client.yaml | grep -A2 -B2 encryption
```

## 相关文档

- [TLS配置文档](TLS_ANY_IP_SOLUTION.md)
- [HTTP伪装功能](HTTP_MASQUERADING.md)
- [安全配置指南](../README.md#安全特性)
