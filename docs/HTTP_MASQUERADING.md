# HTTP伪装功能

## 概述

为了保护服务器免受探测和攻击，Cyber Bastion服务器实现了HTTP伪装功能。当接收到标准HTTP或HTTPS请求时，服务器会返回HTTP 200状态码和JSON数据，伪装成普通的Web服务器。

## 功能特性

### 1. HTTP请求检测
- 自动检测标准HTTP方法（GET、POST、PUT、DELETE、HEAD、OPTIONS、PATCH、TRACE、CONNECT）
- 支持HTTP/1.1协议
- 在TLS握手之前进行检测，避免协议冲突

### 2. HTTPS请求检测
- 在TLS握手完成后检测HTTPS请求
- 支持标准TLS证书验证
- 自动处理加密连接内的HTTP请求

### 3. 伪装响应
- 返回HTTP 200 OK状态码
- 提供JSON格式的响应数据
- 模拟nginx服务器头部
- 包含时间戳和版本信息

### 4. 安全事件记录
- 记录所有HTTP/HTTPS请求尝试
- 分类为低风险安全事件
- 包含客户端IP和请求详情

## 响应格式

### HTTP响应示例
```http
HTTP/1.1 200 OK
Content-Type: application/json
Content-Length: 121
Server: nginx/1.18.0
Date: Tue, 24 Jun 2025 20:20:32 CST
Connection: close

{
  "status": "ok",
  "message": "Service is running",
  "timestamp": "2025-06-24T20:20:32+08:00",
  "version": "1.0.0"
}
```

## 配置

HTTP伪装功能会在以下情况下自动启用：

1. **TLS启用时**：当`security.enable_tls`为`true`时
2. **普通TCP模式**：当TLS未启用时也支持HTTP检测

### 配置示例
```yaml
security:
  enable_tls: true
  tls_cert_file: "certs/server.crt"
  tls_key_file: "certs/server.key"
  tls_ca_file: "certs/ca.crt"
```

## 工作原理

### 1. 连接处理流程
```
客户端连接 → HTTP检测 → 是HTTP? → 返回伪装响应
                ↓
              不是HTTP → TLS握手(如果启用) → HTTPS检测 → 是HTTPS? → 返回伪装响应
                                                    ↓
                                                  不是HTTPS → 正常协议处理
```

### 2. 技术实现
- 使用`peekableConn`包装器实现数据预览
- 避免消费TLS握手所需的数据
- 支持连接复用和数据缓冲

## 测试

### 手动测试
```bash
# 测试HTTP请求
curl http://localhost:8080

# 测试HTTPS请求
curl https://localhost:8080 -k

# 测试不同HTTP方法
curl -X POST http://localhost:8080
curl -X HEAD http://localhost:8080
```

### 自动化测试
```bash
# 运行HTTP伪装测试脚本
./scripts/test-http-masquerade.sh
```

### 单元测试
```bash
# 运行服务器测试
go test ./internal/server -v -run TestServerHTTPMasquerading
```

## 安全考虑

### 优势
1. **隐藏服务性质**：外部扫描器看到的是普通Web服务器
2. **减少攻击面**：避免暴露专有协议特征
3. **记录攻击尝试**：所有HTTP请求都被记录为安全事件
4. **保持兼容性**：不影响正常客户端连接

### 注意事项
1. **性能影响**：每个连接都需要进行协议检测
2. **日志量增加**：HTTP请求会产生安全事件日志
3. **速率限制**：频繁的HTTP请求可能触发IP限制

## 日志示例

### HTTP请求日志
```json
{
  "level": "info",
  "timestamp": "2025-06-24T20:20:32+08:00",
  "caller": "server/server.go:744",
  "msg": "HTTP request detected",
  "client_id": "[::1]:54815",
  "method": "GET"
}
```

### 安全事件日志
```json
{
  "level": "info",
  "timestamp": "2025-06-24T20:20:32+08:00",
  "caller": "server/server.go:685",
  "msg": "Security Event",
  "type": "HTTP_REQUEST",
  "ip": "::1",
  "description": "HTTP request received on secure service",
  "severity": "LOW"
}
```

## 故障排除

### 常见问题

1. **HTTP请求超时**
   - 检查服务器是否正常启动
   - 验证端口是否可访问
   - 查看服务器日志

2. **HTTPS握手失败**
   - 确认TLS证书配置正确
   - 检查证书文件路径
   - 验证证书有效性

3. **响应格式错误**
   - 检查服务器版本
   - 验证HTTP伪装功能是否启用
   - 查看错误日志

### 调试命令
```bash
# 查看服务器日志
tail -f server.log

# 测试连接
telnet localhost 8080

# 验证TLS证书
openssl s_client -connect localhost:8080 -servername localhost
```

## 相关文档

- [TLS配置文档](TLS_ANY_IP_SOLUTION.md)
- [安全配置文档](../README.md#安全特性)
- [证书管理文档](CERTIFICATE_SCRIPTS_SUMMARY.md)
