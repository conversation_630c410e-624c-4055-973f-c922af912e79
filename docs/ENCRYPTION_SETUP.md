# 加密配置指南

## 🔐 加密系统概述

Cyber Bastion 使用 AES-256-GCM 加密算法对传输的数据进行端到端加密，确保数据在传输过程中的安全性。

## 📋 配置要求

### 客户端和服务端必须配置相同的加密参数：

```yaml
# 客户端配置 (configs/client.yaml)
enable_encryption: true
encryption_key: "your-secure-encryption-key-here"

# 服务端配置 (configs/server.yaml)  
security:
  enable_encryption: true
  encryption_key: "your-secure-encryption-key-here"  # 必须与客户端完全一致
```

## ⚠️ 重要提醒

### 1. 密钥一致性
- **客户端和服务端必须使用完全相同的加密密钥**
- 密钥不匹配会导致连接失败或数据解密错误

### 2. 密钥安全性
- **最少16个字符**，推荐32个字符以上
- **使用强随机密码**，包含大小写字母、数字和特殊字符
- **不要使用默认密钥** `your-encryption-key-change-in-production`

### 3. 密钥管理
- **妥善保管密钥**，不要在代码仓库中提交真实密钥
- **定期更换密钥**，建议每3-6个月更换一次
- **备份密钥**，确保密钥丢失时能够恢复

## 🔧 配置步骤

### 方法1：使用自动配置脚本（推荐）

```bash
# 运行自动配置脚本，会自动生成随机密钥
./scripts/setup-single-server.sh
```

脚本会自动：
- 生成32字节的随机加密密钥
- 生成16字节的随机认证令牌
- 在客户端和服务端配置中使用相同的密钥

### 方法2：手动配置

#### 1. 生成随机密钥
```bash
# 生成加密密钥（32字节，Base64编码）
ENCRYPTION_KEY=$(openssl rand -base64 32)
echo "加密密钥: $ENCRYPTION_KEY"

# 生成认证令牌（16字节，十六进制）
AUTH_TOKEN=$(openssl rand -hex 16)
echo "认证令牌: $AUTH_TOKEN"
```

#### 2. 更新客户端配置
```yaml
# configs/client.yaml
auth_token: "生成的认证令牌"
enable_encryption: true
encryption_key: "生成的加密密钥"

# 多隧道配置中也要使用相同密钥
tunnels:
  - name: "primary"
    # ... 其他配置 ...
    enable_encryption: true
    encryption_key: "生成的加密密钥"  # 与全局密钥一致
```

#### 3. 更新服务端配置
```yaml
# configs/server.yaml
auth_token: "生成的认证令牌"  # 与客户端一致
security:
  enable_encryption: true
  encryption_key: "生成的加密密钥"  # 与客户端一致
```

## 🔍 验证配置

### 使用验证脚本
```bash
./scripts/verify-config.sh
```

验证脚本会检查：
- ✅ 客户端和服务端是否都启用了加密
- ✅ 加密密钥是否一致
- ⚠️ 是否使用了默认密钥（需要更改）

### 手动验证
```bash
# 检查客户端加密配置
grep -A1 "enable_encryption" configs/client.yaml
grep "encryption_key" configs/client.yaml

# 检查服务端加密配置  
grep -A1 "enable_encryption" configs/server.yaml
grep "encryption_key" configs/server.yaml
```

## 🚀 启动验证

### 查看启动日志
启动时会显示加密状态：

```
INFO    Encryption enabled with secure key
INFO    Client connected with encryption enabled
```

如果看到以下警告，说明配置有问题：
```
WARN    Using default encryption key, please change for security
ERROR   Encryption key mismatch
ERROR   Failed to decrypt data
```

## 🔧 故障排除

### 常见问题

#### 1. 连接失败
**症状**: 客户端无法连接到服务端
**原因**: 加密密钥不匹配
**解决**: 确保客户端和服务端使用相同的 `encryption_key`

#### 2. 数据解密失败
**症状**: 连接建立但数据传输失败
**原因**: 加密密钥在连接过程中发生变化
**解决**: 重启客户端和服务端，确保配置一致

#### 3. 性能问题
**症状**: 加密后性能下降
**说明**: AES-256-GCM 加密会消耗一定CPU资源，这是正常现象
**优化**: 可以在测试环境中临时禁用加密进行性能对比

### 调试步骤

#### 1. 检查配置
```bash
# 验证配置文件语法
python3 -c "import yaml; print(yaml.safe_load(open('configs/client.yaml')))"
python3 -c "import yaml; print(yaml.safe_load(open('configs/server.yaml')))"
```

#### 2. 启用调试日志
```yaml
# 在配置文件中启用调试日志
logger:
  level: "debug"
  format: "text"
  output: "stdout"
```

#### 3. 测试连接
```bash
# 启动服务端（调试模式）
./bin/cyber-bastion-server -config configs/server.yaml -log-level debug

# 启动客户端（调试模式）
./bin/cyber-bastion-client -config configs/client.yaml -log-level debug
```

## 🛡️ 安全最佳实践

### 1. 密钥管理
- 使用密钥管理系统（如 HashiCorp Vault）
- 不要在配置文件中硬编码密钥
- 使用环境变量或配置文件外部化密钥

### 2. 密钥轮换
```bash
# 定期生成新密钥
NEW_KEY=$(openssl rand -base64 32)

# 更新配置文件
sed -i "s/old-encryption-key/$NEW_KEY/g" configs/client.yaml
sed -i "s/old-encryption-key/$NEW_KEY/g" configs/server.yaml

# 重启服务
./scripts/start-server.sh
./scripts/start-client.sh
```

### 3. 监控和审计
- 监控加密状态和密钥使用
- 记录密钥更换历史
- 定期审计加密配置

## 📊 性能影响

### 加密开销
- **CPU使用**: 增加约5-15%
- **内存使用**: 增加约10-20MB
- **延迟**: 增加约1-5ms
- **吞吐量**: 减少约5-10%

### 优化建议
- 使用硬件加速（AES-NI）
- 调整工作线程数
- 优化缓冲区大小

---

**🔐 记住：安全的加密配置是保护数据传输的第一道防线！**
