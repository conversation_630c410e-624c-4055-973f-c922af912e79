# Testing Guide

This document provides comprehensive information about the testing strategy and implementation for the Cyber Bastion project.

## Test Structure

The project follows Go testing best practices with a comprehensive test suite covering:

- **Unit Tests**: Testing individual components in isolation
- **Integration Tests**: Testing end-to-end functionality between client and server
- **Coverage Reports**: Ensuring adequate test coverage across the codebase

## Test Organization

```
cyber-bastion/
├── pkg/                    # Package-level unit tests
│   ├── protocol/
│   │   └── message_test.go
│   ├── config/
│   │   └── config_test.go
│   └── logger/
│       └── logger_test.go
├── internal/               # Internal component tests
│   ├── server/
│   │   └── server_test.go
│   └── client/
│       └── client_test.go
└── test/                   # Integration tests
    └── integration_test.go
```

## Running Tests

### All Tests
```bash
# Run all tests
make test

# Run tests with verbose output
go test -v ./...
```

### Unit Tests Only
```bash
# Test specific packages
go test -v ./pkg/...
go test -v ./internal/...

# Test individual packages
go test -v ./pkg/protocol
go test -v ./pkg/config
go test -v ./pkg/logger
go test -v ./internal/server
go test -v ./internal/client
```

### Integration Tests
```bash
# Run integration tests only
go test -v ./test/...
```

### Coverage Reports
```bash
# Generate coverage report
make test-coverage

# View coverage summary
make test-coverage-summary

# Generate and open HTML coverage report
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out -o coverage.html
open coverage.html  # macOS
```

## Test Coverage Summary

Based on the latest test run:

| Package | Coverage | Status |
|---------|----------|--------|
| pkg/protocol | 86.8% | ✅ Excellent |
| pkg/config | 94.6% | ✅ Excellent |
| pkg/logger | 100.0% | ✅ Perfect |
| internal/server | 42.4% | ⚠️ Good |
| internal/client | 49.0% | ⚠️ Good |
| **Overall** | **47.9%** | ⚠️ Good |

## Test Categories

### 1. Protocol Tests (`pkg/protocol/message_test.go`)

Tests the core communication protocol:
- Message creation and serialization
- Network communication over TCP pipes
- Message size limits and error handling
- Round-trip serialization/deserialization
- All message types (Auth, Data, Heartbeat, Response, Error)

**Key Test Cases:**
- `TestNewMessage` - Message construction
- `TestSerializeDeserializeRoundTrip` - Data integrity
- `TestSendReceiveMessage` - Network communication
- `TestReceiveMessageSizeLimit` - Security limits

### 2. Configuration Tests (`pkg/config/config_test.go`)

Tests configuration management:
- Default configuration values
- File-based configuration loading
- Environment variable overrides
- Error handling for invalid configurations

**Key Test Cases:**
- `TestLoadServerConfigWithFile` - File loading
- `TestLoadServerConfigWithEnvironmentVariables` - Env vars
- `TestLoadConfigWithInvalidYAML` - Error handling

### 3. Logger Tests (`pkg/logger/logger_test.go`)

Tests logging functionality:
- Logger creation with different configurations
- Multiple output formats (JSON, console)
- Different log levels
- File and stdout/stderr output

**Key Test Cases:**
- `TestNewLoggerWithJSONFormat` - JSON logging
- `TestLoggerLevels` - Log level filtering
- `TestLoggerOutput` - Output verification

### 4. Server Tests (`internal/server/server_test.go`)

Tests server core functionality:
- Client connection management
- Message handling and routing
- Authentication flow
- Data processing

**Key Test Cases:**
- `TestServerHandleAuth` - Authentication
- `TestServerHandleData` - Data processing
- `TestServerClientManagement` - Connection management

### 5. Client Tests (`internal/client/client_test.go`)

Tests client core functionality:
- Connection establishment
- Authentication process
- Data sending
- Message handling

**Key Test Cases:**
- `TestClientSendDataSuccess` - Data transmission
- `TestClientAuthenticate` - Authentication flow
- `TestClientHandleMessage` - Message processing

### 6. Integration Tests (`test/integration_test.go`)

Tests end-to-end functionality:
- Complete client-server communication
- Multiple client scenarios
- Reconnection handling
- Authentication failure scenarios

**Key Test Cases:**
- `TestClientServerIntegration` - Basic communication
- `TestMultipleClientsIntegration` - Concurrent clients
- `TestClientReconnectionIntegration` - Reconnection logic
- `TestAuthenticationFailureIntegration` - Security testing

## Test Results Summary

### ✅ Passing Tests: 47/47 (100%)

**Unit Tests:**
- Protocol: 13/13 tests passing
- Config: 11/11 tests passing  
- Logger: 12/12 tests passing
- Server: 7/7 tests passing
- Client: 10/10 tests passing

**Integration Tests:**
- 4/4 tests passing

### 🎯 Coverage Goals

- **Current Overall Coverage**: 47.9%
- **Target Coverage**: 60%+ for production readiness
- **High Priority Areas**: Server and Client internal packages

### 📈 Coverage Improvement Opportunities

1. **Server Package (42.4% → 60%+)**
   - Add tests for `Start()` method
   - Test connection handling edge cases
   - Test cleanup routines

2. **Client Package (49.0% → 60%+)**
   - Add tests for `Start()` method
   - Test heartbeat loop functionality
   - Test reconnection scenarios

3. **Command Line Applications (0% → 30%+)**
   - Add CLI integration tests
   - Test command-line argument parsing
   - Test application startup/shutdown

## Best Practices

### Writing Tests

1. **Use Table-Driven Tests** for multiple scenarios
2. **Mock External Dependencies** using interfaces
3. **Test Error Conditions** as well as success paths
4. **Use Descriptive Test Names** that explain the scenario
5. **Clean Up Resources** in defer statements

### Test Naming Convention

```go
func TestComponentAction(t *testing.T)           // Basic test
func TestComponentActionWithCondition(t *testing.T)  // Specific scenario
func TestComponentActionFailure(t *testing.T)   // Error case
```

### Example Test Structure

```go
func TestExampleFunction(t *testing.T) {
    // Arrange
    input := "test data"
    expected := "expected result"
    
    // Act
    result, err := ExampleFunction(input)
    
    // Assert
    if err != nil {
        t.Fatalf("Unexpected error: %v", err)
    }
    if result != expected {
        t.Errorf("Expected %s, got %s", expected, result)
    }
}
```

## Continuous Integration

The test suite is designed to run in CI/CD pipelines:

```bash
# CI test command
go test -v -race -coverprofile=coverage.out ./...

# Generate coverage report for CI
go tool cover -func=coverage.out
```

## Troubleshooting

### Common Issues

1. **Port Conflicts in Integration Tests**
   - Tests use fixed ports (18080-18083)
   - Ensure ports are available before running tests

2. **Timing Issues**
   - Some tests include sleep statements for timing
   - Increase timeouts if tests are flaky

3. **Resource Cleanup**
   - Tests properly clean up network connections
   - Use `defer` statements for cleanup

### Debug Mode

Run tests with additional logging:

```bash
go test -v -args -test.v=true ./...
```

## Future Improvements

1. **Benchmark Tests** for performance validation
2. **Fuzz Testing** for protocol robustness
3. **Load Testing** for scalability validation
4. **Mock Servers** for more isolated testing
5. **Property-Based Testing** for edge case discovery
