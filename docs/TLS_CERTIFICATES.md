# TLS Certificates for Cyber Bastion

This document explains how to generate and manage TLS certificates for secure communication between Cyber Bastion server and client components.

## Overview

Cyber Bastion supports TLS encryption for secure communication. The project includes several scripts to help you generate, verify, and manage certificates:

- `scripts/generate-certs.sh` - Full-featured certificate generation with customizable options
- `scripts/quick-certs.sh` - Quick certificate generation for development
- `scripts/verify-certs.sh` - Certificate verification and validation

## Quick Start

### For Development (Recommended)

Generate certificates quickly for development:

```bash
# Using make (recommended)
make certs-quick

# Or directly
./scripts/quick-certs.sh
```

This creates self-signed certificates in the `certs/` directory with default settings suitable for development.

### Enable TLS in Configuration

After generating certificates, enable TLS in your configuration files:

```bash
# Generate certificates and automatically enable TLS
make certs-enable

# Or manually edit configs/server.yaml and configs/client.yaml
# Set: enable_tls: true
```

### Run with TLS

```bash
# Run server with TLS
make run-server-tls

# Run client with T<PERSON>
make run-client-tls
```

## Advanced Certificate Generation

### Full Certificate Generation

For production or custom requirements, use the full certificate generation script:

```bash
# Basic usage
./scripts/generate-certs.sh

# Custom domain and validity period
./scripts/generate-certs.sh --server-cn "bastion.example.com" --days 730

# Custom organization details
./scripts/generate-certs.sh \
  --country "US" \
  --state "California" \
  --city "San Francisco" \
  --org "My Company" \
  --org-unit "IT Security"

# Clean existing certificates and generate new ones
./scripts/generate-certs.sh --clean
```

### Script Options

The `generate-certs.sh` script supports the following options:

| Option | Description | Default |
|--------|-------------|---------|
| `-d, --dir` | Certificate directory | `certs` |
| `-t, --days` | Certificate validity in days | `365` |
| `-k, --key-size` | RSA key size | `2048` |
| `-c, --country` | Country code | `CN` |
| `-s, --state` | State/Province | `Beijing` |
| `-l, --city` | City/Locality | `Beijing` |
| `-o, --org` | Organization | `Cyber Bastion` |
| `-u, --org-unit` | Organizational Unit | `Security` |
| `--server-cn` | Server Common Name | `localhost` |
| `--client-cn` | Client Common Name | `cyber-bastion-client` |
| `--clean` | Clean existing certificates | - |
| `-h, --help` | Show help message | - |

## Certificate Verification

Verify your certificates after generation:

```bash
# Using make
make certs-verify

# Or directly
./scripts/verify-certs.sh

# Verify certificates in custom directory
./scripts/verify-certs.sh --dir /path/to/certs
```

The verification script checks:
- Certificate file existence and validity
- Private key integrity and permissions
- Certificate chain validation
- Certificate and key matching
- Certificate expiration dates
- Certificate extensions and usage

## Generated Files

The certificate generation creates the following files in the `certs/` directory:

| File | Description | Permissions |
|------|-------------|-------------|
| `ca.crt` | Certificate Authority certificate | 644 |
| `ca.key` | CA private key | 600 |
| `server.crt` | Server certificate | 644 |
| `server.key` | Server private key | 600 |
| `client.crt` | Client certificate | 644 |
| `client.key` | Client private key | 600 |
| `README.md` | Certificate information summary | 644 |

## Configuration

### Server Configuration (configs/server.yaml)

```yaml
security:
  enable_tls: true                    # Enable TLS
  tls_cert_file: "certs/server.crt"  # Server certificate
  tls_key_file: "certs/server.key"   # Server private key
  tls_ca_file: "certs/ca.crt"        # CA certificate for client verification
  tls_skip_verify: false             # Don't skip certificate verification
```

### Client Configuration (configs/client.yaml)

```yaml
enable_tls: true                    # Enable TLS
tls_cert_file: "certs/client.crt"  # Client certificate (for mutual TLS)
tls_key_file: "certs/client.key"   # Client private key
tls_ca_file: "certs/ca.crt"        # CA certificate for server verification
tls_skip_verify: false             # Don't skip certificate verification
```

## Make Targets

The project includes several make targets for certificate management:

| Target | Description |
|--------|-------------|
| `make certs` | Generate certificates with full options |
| `make certs-quick` | Quick certificate generation for development |
| `make certs-verify` | Verify existing certificates |
| `make certs-clean` | Remove all certificates |
| `make certs-enable` | Generate certificates and enable TLS in configs |
| `make run-server-tls` | Run server with TLS enabled |
| `make run-client-tls` | Run client with TLS enabled |

## Security Considerations

### Development vs Production

**Development (Self-signed certificates):**
- Use `quick-certs.sh` or `generate-certs.sh` with default settings
- Suitable for local development and testing
- Browsers will show security warnings

**Production:**
- Use certificates from a trusted Certificate Authority (CA)
- Use proper domain names (not localhost)
- Implement proper certificate rotation
- Monitor certificate expiration

### Best Practices

1. **Private Key Security:**
   - Keep private keys secure (permissions 600)
   - Never share private keys
   - Store production keys in secure key management systems

2. **Certificate Rotation:**
   - Monitor certificate expiration dates
   - Implement automated certificate renewal
   - Test certificate rotation procedures

3. **Network Security:**
   - Use TLS 1.2 or higher
   - Disable weak cipher suites
   - Implement proper certificate validation

4. **Monitoring:**
   - Monitor certificate expiration
   - Log TLS handshake failures
   - Implement certificate transparency monitoring

## Troubleshooting

### Common Issues

1. **Certificate verification failed:**
   ```bash
   # Check certificate validity
   make certs-verify
   
   # Regenerate certificates
   make certs-clean
   make certs-quick
   ```

2. **Permission denied errors:**
   ```bash
   # Fix file permissions
   chmod 600 certs/*.key
   chmod 644 certs/*.crt
   ```

3. **TLS handshake failures:**
   - Verify certificate and key match
   - Check certificate expiration
   - Ensure proper CA certificate configuration

4. **Domain name mismatch:**
   ```bash
   # Generate certificates with correct domain
   ./scripts/generate-certs.sh --server-cn "your-domain.com"
   ```

### Debug Commands

```bash
# Check certificate details
openssl x509 -in certs/server.crt -text -noout

# Verify certificate chain
openssl verify -CAfile certs/ca.crt certs/server.crt

# Test TLS connection
openssl s_client -connect localhost:8080 -CAfile certs/ca.crt

# Check private key
openssl rsa -in certs/server.key -check -noout
```

## Integration with CI/CD

For automated deployments, you can integrate certificate generation into your CI/CD pipeline:

```bash
# In your CI/CD script
make certs-quick
make build
make test

# Or for production with custom settings
./scripts/generate-certs.sh --server-cn "$PRODUCTION_DOMAIN" --days 90
```

## Support

For issues related to certificate generation or TLS configuration:

1. Check the certificate verification output: `make certs-verify`
2. Review the generated certificate summary: `cat certs/README.md`
3. Check the application logs for TLS-related errors
4. Refer to the troubleshooting section above
