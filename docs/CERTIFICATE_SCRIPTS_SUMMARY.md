# Certificate Scripts Summary

This document summarizes the certificate generation and management scripts added to the Cyber Bastion project.

## Added Scripts

### 1. `scripts/generate-certs.sh`
**Full-featured certificate generation script**

- **Purpose**: Generate production-ready TLS certificates with customizable options
- **Features**:
  - Customizable certificate parameters (country, organization, validity period, etc.)
  - Support for custom domain names
  - Proper certificate extensions (SAN, Key Usage)
  - Certificate chain validation
  - Detailed certificate information display
  - Automatic README generation
- **Usage**: `./scripts/generate-certs.sh [OPTIONS]`
- **Key Options**:
  - `--server-cn`: Custom server domain name
  - `--days`: Certificate validity period
  - `--clean`: Clean existing certificates
  - `--org`, `--country`: Organization details

### 2. `scripts/quick-certs.sh`
**Simplified certificate generation for development**

- **Purpose**: Quick certificate generation for development and testing
- **Features**:
  - Minimal configuration required
  - Fast generation with sensible defaults
  - Self-signed certificates suitable for development
  - Proper file permissions
- **Usage**: `./scripts/quick-certs.sh`
- **Output**: Generates CA, server, and client certificates in `certs/` directory

### 3. `scripts/verify-certs.sh`
**Certificate verification and validation**

- **Purpose**: Verify the integrity and validity of generated certificates
- **Features**:
  - File existence checks
  - Certificate validity verification
  - Private key integrity checks
  - Certificate chain validation
  - Certificate and key matching verification
  - Permission checks
  - Expiration warnings
  - Certificate extension analysis
- **Usage**: `./scripts/verify-certs.sh [--dir CERT_DIR]`

### 4. `scripts/test-tls.sh`
**TLS functionality testing**

- **Purpose**: End-to-end testing of TLS functionality
- **Features**:
  - Automatic certificate generation if needed
  - Server and client startup with TLS
  - Connection testing
  - Log analysis
  - Automatic cleanup
- **Usage**: `./scripts/test-tls.sh [--duration SECONDS]`

## Makefile Targets

The following make targets have been added for certificate management:

| Target | Description | Script Used |
|--------|-------------|-------------|
| `make certs` | Generate certificates with full options | `generate-certs.sh` |
| `make certs-quick` | Quick certificate generation for development | `quick-certs.sh` |
| `make certs-verify` | Verify existing certificates | `verify-certs.sh` |
| `make certs-clean` | Remove all certificates | Built-in |
| `make certs-enable` | Generate certificates and enable TLS in configs | `quick-certs.sh` + config update |
| `make run-server-tls` | Run server with TLS enabled | Built-in |
| `make run-client-tls` | Run client with TLS enabled | Built-in |
| `make test-tls` | Test TLS functionality | `test-tls.sh` |

## Generated Files

When certificates are generated, the following files are created in the `certs/` directory:

| File | Description | Permissions | Purpose |
|------|-------------|-------------|---------|
| `ca.crt` | Certificate Authority certificate | 644 | Root certificate for validation |
| `ca.key` | CA private key | 600 | CA signing key (keep secure) |
| `server.crt` | Server certificate | 644 | Server identity certificate |
| `server.key` | Server private key | 600 | Server private key (keep secure) |
| `client.crt` | Client certificate | 644 | Client identity certificate |
| `client.key` | Client private key | 600 | Client private key (keep secure) |
| `README.md` | Certificate summary | 644 | Generated documentation |

## Configuration Integration

The scripts integrate with the existing configuration system:

### Server Configuration (`configs/server.yaml`)
```yaml
security:
  enable_tls: true                    # Enable/disable TLS
  tls_cert_file: "certs/server.crt"  # Server certificate path
  tls_key_file: "certs/server.key"   # Server private key path
  tls_ca_file: "certs/ca.crt"        # CA certificate path
  tls_skip_verify: false             # Certificate verification
```

### Client Configuration (`configs/client.yaml`)
```yaml
enable_tls: true                    # Enable/disable TLS
tls_cert_file: "certs/client.crt"  # Client certificate path
tls_key_file: "certs/client.key"   # Client private key path
tls_ca_file: "certs/ca.crt"        # CA certificate path
tls_skip_verify: false             # Certificate verification
```

## Security Features

### Certificate Security
- **RSA 2048-bit keys**: Strong encryption keys
- **Proper extensions**: Subject Alternative Names, Key Usage
- **Certificate chain**: Proper CA hierarchy
- **File permissions**: Restrictive permissions on private keys (600)

### Validation Features
- **Certificate chain validation**: Ensures proper signing
- **Key matching**: Verifies certificate and key pairs match
- **Expiration checking**: Warns about expiring certificates
- **Extension verification**: Checks proper certificate usage

## Usage Workflows

### Development Workflow
```bash
# Quick setup for development
make certs-enable
make build
make run-server-tls    # Terminal 1
make run-client-tls    # Terminal 2
```

### Production Workflow
```bash
# Generate production certificates
./scripts/generate-certs.sh \
  --server-cn "your-domain.com" \
  --days 730 \
  --org "Your Company"

# Verify certificates
make certs-verify

# Enable TLS in configs manually
# Build and deploy
make build-all
```

### Testing Workflow
```bash
# Automated TLS testing
make test-tls

# Manual testing with verification
make certs-quick
make certs-verify
make build
# Start server and client manually
```

## Compatibility

### OpenSSL Compatibility
- **LibreSSL**: Fully compatible (tested on macOS)
- **OpenSSL 1.1+**: Fully compatible
- **OpenSSL 3.0+**: Fully compatible

### Platform Compatibility
- **macOS**: Full support (Intel and Apple Silicon)
- **Linux**: Full support (amd64 and arm64)
- **Windows**: Full support (with WSL or native OpenSSL)

## Documentation

### Added Documentation
- `docs/TLS_CERTIFICATES.md`: Comprehensive TLS certificate guide
- `docs/CERTIFICATE_SCRIPTS_SUMMARY.md`: This summary document
- `certs/README.md`: Auto-generated certificate information
- Updated main `README.md` with TLS section

### Integration with Existing Docs
- Updated main README with TLS quick start
- Added certificate management to build documentation
- Integrated with existing troubleshooting guides

## Error Handling

### Script Error Handling
- **Validation checks**: Pre-flight checks for dependencies
- **Cleanup on failure**: Automatic cleanup of partial files
- **Detailed error messages**: Clear error reporting
- **Exit codes**: Proper exit codes for automation

### Common Issues Addressed
- **Missing OpenSSL**: Clear error message and instructions
- **Permission issues**: Automatic permission setting
- **Certificate validation failures**: Detailed validation output
- **Configuration conflicts**: Backup and restore mechanisms

## Future Enhancements

### Potential Improvements
- **Certificate rotation**: Automated certificate renewal
- **HSM integration**: Hardware security module support
- **ACME protocol**: Let's Encrypt integration
- **Certificate monitoring**: Expiration monitoring and alerts
- **Multi-domain certificates**: Support for multiple SANs

### Integration Opportunities
- **CI/CD integration**: Automated certificate generation in pipelines
- **Container integration**: Docker image with certificates
- **Kubernetes integration**: Certificate management in K8s
- **Monitoring integration**: Certificate metrics and alerts

## Summary

The certificate management system provides:

✅ **Complete certificate lifecycle management**
✅ **Development and production workflows**
✅ **Comprehensive validation and testing**
✅ **Integration with existing configuration**
✅ **Cross-platform compatibility**
✅ **Detailed documentation and examples**
✅ **Error handling and troubleshooting**
✅ **Make target integration for easy use**

This implementation enables secure TLS communication for Cyber Bastion with minimal setup complexity while providing advanced options for production use.
