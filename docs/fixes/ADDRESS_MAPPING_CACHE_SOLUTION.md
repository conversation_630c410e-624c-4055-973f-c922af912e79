# 地址映射缓存解决方案

## 问题背景

根据用户提供的数据流程和日志分析：

### 数据流程
```
************* -> tun:******** -> default route -> server:*************(lan:********) -> *************
```

### 问题现象
- **发送包**：源IP=*************（客户端真实IP），目标IP=*************（外部目标）✅
- **响应包**：源IP=*************（外部目标）✅，目标IP=********（服务器内网IP）❌

**核心问题**：响应包的目标IP应该是客户端真实IP（*************），但却变成了服务器内网IP（********）

## 解决方案：地址映射缓存

### 核心思想

在服务器端实现地址映射关系缓存，记录并维护客户端真实IP与数据包处理的映射关系：

1. **数据包接收时**：记录 `客户端真实IP` ↔ `服务器本地IP` ↔ `目标IP` 的映射关系
2. **响应包构造时**：使用缓存的客户端真实IP作为响应包的目标IP

### 实现架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   客户端真实IP   │    │   地址映射缓存    │    │   响应包构造     │
│  *************  │───▶│  ClientID ↔ IP  │───▶│  目标IP正确映射  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 代码实现

### 1. 地址映射缓存结构

```go
// AddressMapping 地址映射关系
type AddressMapping struct {
    ClientRealIP  net.IP    // 客户端真实IP
    ServerLocalIP net.IP    // 服务器本地IP
    TargetIP      net.IP    // 目标IP
    LastUsed      time.Time // 最后使用时间
    PacketCount   int64     // 数据包计数
}

// AddressMappingCache 地址映射缓存
type AddressMappingCache struct {
    mappings map[string]*AddressMapping // key: clientID
    mu       sync.RWMutex
    ttl      time.Duration // 缓存TTL
}
```

### 2. 缓存管理方法

- `Set(clientID, clientRealIP, serverLocalIP, targetIP)` - 设置映射
- `Get(clientID)` - 获取映射
- `Update(clientID)` - 更新使用时间
- `Cleanup()` - 清理过期映射

### 3. 数据包处理流程

#### A. 接收数据包时记录映射
```go
// 在 handleTunData 函数中
if srcIPAddr != nil && dstIPAddr != nil {
    serverLocalIP := net.ParseIP("********")
    
    if mapping, exists := s.addressMappingCache.Get(client.ID); exists {
        s.addressMappingCache.Update(client.ID)
    } else {
        s.addressMappingCache.Set(client.ID, srcIPAddr, serverLocalIP, dstIPAddr)
    }
}
```

#### B. 响应包构造时使用缓存
```go
// 在 forwardICMPWithResponse 函数中
var clientRealIP net.IP = originalClientIP

if mapping, exists := s.addressMappingCache.Get(clientID); exists {
    clientRealIP = mapping.ClientRealIP
}

responsePacket, err := s.buildICMPResponsePacket(clientRealIP, dstIP, icmpData)
```

## 关键特性

### 1. 自动缓存管理
- **TTL机制**：10分钟过期时间
- **定期清理**：每5分钟清理过期映射
- **使用计数**：跟踪数据包数量

### 2. 线程安全
- 使用 `sync.RWMutex` 保护并发访问
- 读写分离锁提高性能

### 3. 调试支持
- 详细的日志记录
- 映射创建、更新、使用的完整追踪

## 预期效果

### 修复前
```log
DEBUG client/client.go:662 Received TUN response packet {"src_ip": "*************", "dst_ip": "********", "protocol": "ICMP"}
```

### 修复后
```log
DEBUG server/server.go:xxx Created new address mapping {"client_real_ip": "*************", "server_local_ip": "********"}
DEBUG server/server.go:xxx Using cached address mapping for response {"cached_client_real_ip": "*************"}
DEBUG server/server.go:xxx ICMP response packet constructed with address mapping {"response_dst_ip": "*************"}
DEBUG client/client.go:662 Received TUN response packet {"src_ip": "*************", "dst_ip": "*************", "protocol": "ICMP"}
```

## 测试验证

### 1. 编译代码
```bash
go build -o bin/server cmd/server/main.go
go build -o bin/client cmd/client/main.go
```

### 2. 运行测试
```bash
chmod +x test_icmp_fix.sh
./test_icmp_fix.sh
```

### 3. 验证要点
1. 服务器日志显示地址映射的创建和使用
2. 客户端接收的响应包目标IP为客户端真实IP（*************）
3. ICMP ping能够正常工作

## 优势

1. **精确映射**：确保响应包路由到正确的客户端IP
2. **性能优化**：缓存机制减少重复计算
3. **自动管理**：TTL和定期清理避免内存泄漏
4. **调试友好**：详细日志便于问题排查
5. **扩展性好**：支持多客户端并发访问

## 相关文件

- `internal/server/server.go` - 主要实现文件
- `test_icmp_fix.sh` - 测试脚本
- `ICMP_IP_MAPPING_FIX.md` - 详细修复文档
- `ADDRESS_MAPPING_CACHE_SOLUTION.md` - 本解决方案文档
