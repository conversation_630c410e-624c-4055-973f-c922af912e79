# ICMP IP地址映射修复

## 问题描述

用户报告ICMP ping测试中存在IP地址映射错误的问题：

### 原始日志分析
```
2025-06-25T09:20:01.234+0800	DEBUG	client/client.go:965	TUN packet forwarded to server	{"size": 84, "src_ip": "*************", "dst_ip": "*************", "protocol": "ICMP"}
2025-06-25T09:20:01.299+0800	DEBUG	client/client.go:662	Received TUN response packet	{"message_id": "tun-1750814401233944819", "packet_length": 84, "src_ip": "*************", "dst_ip": "********", "protocol": "ICMP"}
```

### 问题分析
- **发送包**：源IP=*************（客户端），目标IP=*************（外部目标）✅
- **响应包**：源IP=*************（外部目标）✅，目标IP=********（服务器IP）❌

**根本原因**：响应包的目标IP应该是客户端IP（*************），但却变成了服务器IP（********）

## 数据流程分析

根据用户提供的完整数据流程：
```
************* -> tun:******** -> default route -> server:*************(lan:********) -> *************
```

问题分析：
1. **客户端真实IP**：*************
2. **TUN接口IP**：********
3. **服务器公网IP**：*************
4. **服务器内网IP**：********
5. **目标IP**：*************

**核心问题**：响应包的目标IP变成了服务器内网IP (********)，而不是客户端真实IP (*************)

## 修复方案：地址映射缓存

### 1. 核心思想

实现服务器端地址映射关系缓存，记录客户端真实IP与数据包处理的映射关系：

1. **接收数据包时**：记录 `客户端真实IP` ↔ `服务器本地IP` ↔ `目标IP` 的映射关系
2. **构造响应包时**：使用缓存的客户端真实IP作为响应包的目标IP

### 2. 实现架构

#### A. 地址映射缓存结构

```go
// AddressMapping 地址映射关系
type AddressMapping struct {
    ClientRealIP  net.IP    // 客户端真实IP
    ServerLocalIP net.IP    // 服务器本地IP
    TargetIP      net.IP    // 目标IP
    LastUsed      time.Time // 最后使用时间
    PacketCount   int64     // 数据包计数
}

// AddressMappingCache 地址映射缓存
type AddressMappingCache struct {
    mappings map[string]*AddressMapping // key: clientID
    mu       sync.RWMutex
    ttl      time.Duration // 缓存TTL
}
```

#### B. 数据包接收时记录映射

在 `handleTunData` 函数中：

```go
// 记录地址映射关系（用于响应包的正确路由）
if srcIPAddr != nil && dstIPAddr != nil {
    serverLocalIP := net.ParseIP("********") // 从配置或系统获取

    if mapping, exists := s.addressMappingCache.Get(client.ID); exists {
        // 更新现有映射
        s.addressMappingCache.Update(client.ID)
    } else {
        // 创建新映射
        s.addressMappingCache.Set(client.ID, srcIPAddr, serverLocalIP, dstIPAddr)
    }
}
```

#### C. 响应包构造时使用缓存

在 `forwardICMPWithResponse` 函数中：

```go
// 使用地址映射缓存确保正确的IP地址映射
var clientRealIP net.IP = originalClientIP // 默认使用数据包中的源IP

// 从地址映射缓存中获取客户端真实IP
if mapping, exists := s.addressMappingCache.Get(clientID); exists {
    clientRealIP = mapping.ClientRealIP
    s.logger.Debug("Using cached address mapping for response", ...)
} else {
    s.logger.Debug("No cached address mapping found, using packet source IP", ...)
}

// 构造响应包时使用客户端真实IP
responsePacket, err := s.buildICMPResponsePacket(clientRealIP, dstIP, icmpData)
```

### 3. 预期效果

修复后的日志应该显示：

#### 地址映射创建：
```log
DEBUG server/server.go:xxx Created new address mapping {"client_id": "xxx", "client_real_ip": "*************", "server_local_ip": "********", "target_ip": "*************"}
```

#### 地址映射使用：
```log
DEBUG server/server.go:xxx Using cached address mapping for response {"client_id": "xxx", "cached_client_real_ip": "*************", "target_ip": "*************"}
```

#### ICMP响应包构造：
```log
DEBUG server/server.go:xxx ICMP response packet constructed with address mapping {"response_src_ip": "*************", "response_dst_ip": "*************"}
```

#### 客户端接收：
```log
DEBUG client/client.go:662 Received TUN response packet {"src_ip": "*************", "dst_ip": "*************", "protocol": "ICMP"}
```

## 测试方法

### 1. 编译更新的代码
```bash
go build -o bin/server cmd/server/main.go
go build -o bin/client cmd/client/main.go
```

### 2. 运行测试脚本
```bash
chmod +x test_icmp_fix.sh
./test_icmp_fix.sh
```

### 3. 手动测试
1. 启动服务器：`sudo ./bin/server -config configs/server.yaml`
2. 启动客户端：`sudo ./bin/client -config configs/client.yaml`
3. 通过TUN接口发送ping：`ping -I cyber-tun *************`
4. 观察日志中的IP地址映射

## 验证要点

1. **服务器日志**：确认 `Building ICMP response packet` 显示正确的IP映射
2. **客户端日志**：确认响应包的目标IP是客户端真实IP，而不是服务器IP
3. **网络连通性**：确认ICMP ping能够正常工作

## 相关文件

- `internal/server/server.go` - 主要修复文件
- `test_icmp_fix.sh` - 测试脚本
- `ICMP_IP_MAPPING_FIX.md` - 本文档

## 注意事项

如果问题仍然存在，可能需要进一步调查：
1. 系统网络栈的行为
2. TUN接口的配置
3. 路由表的影响
4. 防火墙规则的影响
