# Cyber Bastion

一个使用 Go 语言构建的高性能 TCP 客户端-服务器应用程序，具有身份验证、心跳监控和自动重连功能。

## 功能特性

- **TCP 长连接**：客户端与服务器之间的持久 TCP 连接
- **身份验证**：基于令牌的身份验证系统
- **心跳监控**：自动心跳检测连接问题
- **智能重连**：客户端在连接丢失时自动重连，支持指数退避策略
- **结构化日志**：使用 zap 进行高性能结构化日志记录
- **配置管理**：基于 YAML 的配置，支持环境变量
- **命令行界面**：使用 cobra 的命令行界面
- **优雅关闭**：正确的信号处理和优雅关闭
- **跨平台**：支持多个平台和架构
- **连接监控**：实时连接统计和健康检查
- **高级重连**：指数退避重连策略，最大重试次数限制

## 支持的平台

| 平台 | 架构 | 状态 |
|----------|-------------|--------|
| Linux | amd64 | ✅ 支持 |
| Linux | arm64 | ✅ 支持 |
| macOS | amd64 (Intel) | ✅ 支持 |
| macOS | arm64 (Apple Silicon) | ✅ 支持 |
| Windows | amd64 | ✅ 支持 |

## 项目架构

```
cyber-bastion/
├── cmd/                    # 应用程序入口点
│   ├── server/            # 服务器应用程序
│   └── client/            # 客户端应用程序
├── internal/              # 私有应用程序代码
│   ├── server/            # 服务器实现
│   └── client/            # 客户端实现
├── pkg/                   # 公共库代码
│   ├── protocol/          # 通信协议
│   ├── config/            # 配置管理
│   └── logger/            # 日志工具
├── configs/               # 配置文件
├── docs/                  # 文档
└── scripts/               # 构建和部署脚本
```

## 快速开始

### 前置要求

- Go 1.23.0 或更高版本
- Make（可选，用于使用 Makefile）

### 安装

1. 克隆仓库：
```bash
git clone <repository-url>
cd cyber-bastion
```

2. 安装依赖：
```bash
go mod tidy
```

3. 构建应用程序：
```bash
make build
```

### 运行应用程序

#### 启动服务器

```bash
# 使用 Makefile
make run-server

# 或直接运行
./bin/server --config configs/server.yaml
```

#### 启动客户端

```bash
# 使用 Makefile（测试模式）
make run-client

# 交互模式
make run-client-interactive

# 或直接运行
./bin/client --config configs/client.yaml --interactive
```

## 配置

### 服务器配置 (configs/server.yaml)

```yaml
host: "0.0.0.0"
port: 8080
read_timeout: 30
write_timeout: 30
max_clients: 100
auth_token: "cyber-bastion-secret-token"

logger:
  level: "info"
  format: "json"
  output: "stdout"
```

### 客户端配置 (configs/client.yaml)

```yaml
server_host: "localhost"
server_port: 8080
connect_timeout: 10
heartbeat_interval: 30
reconnect_delay: 5
max_reconnect_delay: 60
max_reconnect_tries: 10
auth_token: "cyber-bastion-secret-token"

logger:
  level: "info"
  format: "console"
  output: "stdout"
```

### 环境变量

您可以使用前缀为 `CYBER_BASTION_SERVER_` 或 `CYBER_BASTION_CLIENT_` 的环境变量来覆盖配置值：

```bash
export CYBER_BASTION_SERVER_PORT=9090
export CYBER_BASTION_CLIENT_SERVER_HOST=*************
```

## 命令行选项

### 服务器

```bash
./bin/server [flags]

标志：
  -c, --config string     配置文件路径
  -H, --host string       服务器主机
  -l, --log-level string  日志级别 (debug, info, warn, error)
  -p, --port int          服务器端口
```

### 客户端

```bash
./bin/client [flags]

标志：
  -c, --config string     配置文件路径
  -i, --interactive       以交互模式运行
  -l, --log-level string  日志级别 (debug, info, warn, error)
  -p, --port int          服务器端口
  -s, --server string     服务器主机
```

## 通信协议

应用程序使用自定义 TCP 协议，消息格式如下：

```
[4字节长度][JSON消息]
```

### 消息类型

- `MessageTypeAuth`：身份验证消息
- `MessageTypeHeartbeat`：心跳消息
- `MessageTypeData`：数据消息
- `MessageTypeResponse`：响应消息
- `MessageTypeError`：错误消息

### 消息结构

```json
{
  "type": 1,
  "id": "message-id",
  "timestamp": 1640995200,
  "data": "base64-encoded-data"
}
```

## 高级功能

### 客户端连接管理

客户端具有以下高级连接管理功能：

#### 智能重连策略
- **指数退避**：重连延迟从初始值开始，每次失败后翻倍
- **最大延迟限制**：防止重连延迟过长
- **最大重试次数**：避免无限重连尝试
- **并发安全**：防止多个重连协程同时运行

#### 心跳监控增强
- **响应验证**：心跳不仅发送，还等待并验证服务器响应
- **超时检测**：5秒心跳超时，确保及时发现连接问题
- **失败统计**：跟踪连续失败次数和总失败次数

#### 连接统计
客户端提供详细的连接统计信息：
- 连接时间和持续时间
- 发送/接收消息计数
- 心跳成功/失败统计
- 重连次数统计
- 最后活动时间

#### 配置参数说明
```yaml
# 重连相关配置
reconnect_delay: 5          # 初始重连延迟（秒）
max_reconnect_delay: 60     # 最大重连延迟（秒）
max_reconnect_tries: 10     # 最大重连尝试次数
```

## 开发

### 构建

```bash
# 构建所有二进制文件（推荐用于大多数情况）
make build

# 为所有平台构建（Linux、macOS、Windows - 支持的 amd64 和 arm64）
make build-all

# 为特定平台构建
make build-linux      # Linux amd64 和 arm64
make build-darwin     # macOS amd64 和 arm64（Intel 和 Apple Silicon）
make build-windows    # Windows amd64

# 使用 Git 版本信息构建（需要干净的 Git 仓库）
make build-with-vcs

# 为所有平台使用 Git 版本信息构建
make build-all-with-vcs

# 清理构建产物
make clean

# 诊断 Git 相关构建问题
make diagnose-git

# 显示构建二进制文件的信息
make show-build-info

# 打包发布归档
make package-release VERSION=1.0.0

# 完整发布工作流（构建 + 打包）
make release VERSION=1.0.0
```

#### 构建问题

如果遇到 Git 相关的构建错误，如：
```
error obtaining VCS status: exit status 128
Use -buildvcs=false to disable VCS stamping.
```

**解决方案：**
1. 使用默认构建命令（`make build` 或 `make build-all`），它们会自动禁用 VCS 标记
2. 运行 `make diagnose-git` 获取详细诊断和建议
3. 对于带版本信息的构建，确保您的 Git 仓库是干净的，并使用 `make build-with-vcs`

### 测试

```bash
# 运行测试
make test

# 运行带覆盖率的测试
make test-coverage
```

### 代码质量

```bash
# 格式化代码
make fmt

# 代码检查（需要 golangci-lint）
make lint

# 整理依赖
make tidy
```

## 部署

### 二进制部署

1. 构建二进制文件：
```bash
make build-all
```

2. 将适当的二进制文件和配置文件复制到目标系统。

3. 使用所需配置运行应用程序。

### Docker 部署

```bash
# 构建 Docker 镜像
make docker-build

# 运行服务器
docker run -p 8080:8080 -v $(pwd)/configs:/app/configs cyber-bastion-server

# 运行客户端
docker run --network host -v $(pwd)/configs:/app/configs cyber-bastion-client
```

## 监控和日志

应用程序提供可配置级别和格式的结构化日志：

- **级别**：debug、info、warn、error
- **格式**：json（用于生产环境）、console（用于开发环境）
- **输出**：stdout、stderr 或文件路径

### 日志示例

```json
{
  "level": "info",
  "timestamp": "2023-12-31T12:00:00Z",
  "caller": "server/server.go:123",
  "msg": "Client connected",
  "client_id": "*************:54321"
}
```

## 故障排除

### 常见问题

1. **连接被拒绝**：检查服务器是否正在运行以及端口是否正确。
2. **身份验证失败**：验证客户端和服务器之间的 auth_token 是否匹配。
3. **连接超时**：检查网络连接和防火墙设置。
4. **频繁重连**：检查网络稳定性，调整心跳间隔和重连参数。
5. **心跳超时**：网络延迟较高时，可能需要调整心跳超时设置。

### 连接质量监控

使用客户端的连接统计功能监控连接质量：

```bash
# 在调试模式下运行客户端查看详细统计
./bin/client --log-level debug
```

关注以下指标：
- `connected_duration`：连接持续时间
- `messages_sent/received`：消息发送/接收计数
- `heartbeat_failures`：心跳失败次数
- `reconnect_count`：重连次数

### 调试模式

使用调试日志运行应用程序以获取详细信息：

```bash
./bin/server --log-level debug
./bin/client --log-level debug
```

## 贡献

1. Fork 仓库
2. 创建功能分支
3. 进行更改
4. 为新功能添加测试
5. 运行测试并确保通过
6. 提交拉取请求

## 许可证

本项目采用 MIT 许可证 - 详情请参阅 LICENSE 文件。
