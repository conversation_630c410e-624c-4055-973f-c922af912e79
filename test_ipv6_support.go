package main

import (
	"fmt"
	"net"

	"cyber-bastion/pkg/checksum"
)

func main() {
	fmt.Println("🧪 Testing IPv6 Protocol Support")
	fmt.Println("================================")

	// 测试IPv6地址解析
	testIPv6AddressParsing()

	// 测试IPv6校验和计算
	testIPv6ChecksumCalculation()

	// 测试IPv6数据包构造
	testIPv6PacketConstruction()

	fmt.Println("\n✅ IPv6 Protocol Support Test Completed!")
}

func testIPv6AddressParsing() {
	fmt.Println("\n📋 Testing IPv6 Address Parsing...")

	testCases := []struct {
		name  string
		addr  string
		valid bool
	}{
		{"Loopback", "::1", true},
		{"Google DNS", "2001:4860:4860::8888", true},
		{"Link Local", "fe80::1", true},
		{"Multicast", "ff02::1", true},
		{"Invalid", "invalid::address::too::many", false},
		{"IPv4 Mapped", "::ffff:***********", true},
	}

	for _, tc := range testCases {
		ip := net.ParseIP(tc.addr)
		isValid := ip != nil && ip.To16() != nil

		status := "❌"
		if isValid == tc.valid {
			status = "✅"
		}

		fmt.Printf("  %s %s: %s (valid: %v)\n", status, tc.name, tc.addr, isValid)
	}
}

func testIPv6ChecksumCalculation() {
	fmt.Println("\n🔢 Testing IPv6 Checksum Calculation...")

	srcIP := net.ParseIP("2001:db8::1")
	dstIP := net.ParseIP("2001:db8::2")

	// 测试UDP over IPv6校验和
	udpData := []byte{
		0x04, 0xd2, // 源端口 1234
		0x00, 0x35, // 目标端口 53 (DNS)
		0x00, 0x0c, // 长度 12
		0x00, 0x00, // 校验和（设为0）
		0x74, 0x65, 0x73, 0x74, // 数据 "test"
	}

	udpChecksum := checksum.CalculateUDPv6Checksum(srcIP, dstIP, udpData)
	fmt.Printf("  ✅ UDP over IPv6 checksum: 0x%04x\n", udpChecksum)

	// 测试TCP over IPv6校验和
	tcpData := []byte{
		0x04, 0xd2, // 源端口 1234
		0x00, 0x50, // 目标端口 80 (HTTP)
		0x00, 0x00, 0x00, 0x01, // 序列号
		0x00, 0x00, 0x00, 0x00, // 确认号
		0x50, 0x02, // 头部长度和标志
		0x20, 0x00, // 窗口大小
		0x00, 0x00, // 校验和（设为0）
		0x00, 0x00, // 紧急指针
	}

	tcpChecksum := checksum.CalculateTCPv6Checksum(srcIP, dstIP, tcpData)
	fmt.Printf("  ✅ TCP over IPv6 checksum: 0x%04x\n", tcpChecksum)

	// 测试ICMPv6校验和
	icmpData := []byte{
		0x80, 0x00, // 类型和代码 (Echo Request)
		0x00, 0x00, // 校验和（设为0）
		0x00, 0x00, // 标识符
		0x00, 0x01, // 序列号
		0x61, 0x62, 0x63, 0x64, // 数据 "abcd"
	}

	icmpChecksum := checksum.CalculateICMPv6Checksum(srcIP, dstIP, icmpData)
	fmt.Printf("  ✅ ICMPv6 checksum: 0x%04x\n", icmpChecksum)
}

func testIPv6PacketConstruction() {
	fmt.Println("\n📦 Testing IPv6 Packet Construction...")

	srcIP := net.ParseIP("2001:db8::1")
	dstIP := net.ParseIP("2001:db8::2")

	// 构造IPv6 UDP数据包
	udpPacket := constructIPv6UDPPacket(srcIP, dstIP, 1234, 53, []byte("test"))
	fmt.Printf("  ✅ IPv6 UDP packet constructed: %d bytes\n", len(udpPacket))

	// 验证IPv6头部
	if len(udpPacket) >= 40 {
		version := udpPacket[0] >> 4
		nextHeader := udpPacket[6]
		fmt.Printf("    - IP version: %d\n", version)
		fmt.Printf("    - Next header: %d (UDP)\n", nextHeader)
		fmt.Printf("    - Source IP: %s\n", net.IP(udpPacket[8:24]).String())
		fmt.Printf("    - Dest IP: %s\n", net.IP(udpPacket[24:40]).String())
	}

	// 构造IPv6 ICMPv6数据包
	icmpPacket := constructIPv6ICMPPacket(srcIP, dstIP, []byte("ping"))
	fmt.Printf("  ✅ IPv6 ICMPv6 packet constructed: %d bytes\n", len(icmpPacket))

	// 验证ICMPv6头部
	if len(icmpPacket) >= 44 {
		icmpType := icmpPacket[40]
		icmpCode := icmpPacket[41]
		fmt.Printf("    - ICMP type: %d\n", icmpType)
		fmt.Printf("    - ICMP code: %d\n", icmpCode)
	}
}

func constructIPv6UDPPacket(srcIP, dstIP net.IP, srcPort, dstPort uint16, data []byte) []byte {
	ipv6HeaderLen := 40
	udpHeaderLen := 8
	totalLen := ipv6HeaderLen + udpHeaderLen + len(data)

	packet := make([]byte, totalLen)

	// 构造IPv6头部
	packet[0] = 0x60 // 版本(6) + 流量类别高4位
	packet[1] = 0x00 // 流量类别低4位 + 流标签高4位
	packet[2] = 0x00 // 流标签中8位
	packet[3] = 0x00 // 流标签低8位
	payloadLen := udpHeaderLen + len(data)
	packet[4] = byte(payloadLen >> 8)   // 载荷长度高字节
	packet[5] = byte(payloadLen & 0xFF) // 载荷长度低字节
	packet[6] = 17                      // 下一个头部(UDP)
	packet[7] = 64                      // 跳数限制

	// 源IP地址 (16字节)
	copy(packet[8:24], srcIP.To16())
	// 目标IP地址 (16字节)
	copy(packet[24:40], dstIP.To16())

	// 构造UDP头部
	udpStart := ipv6HeaderLen
	packet[udpStart] = byte(srcPort >> 8)        // 源端口高字节
	packet[udpStart+1] = byte(srcPort & 0xFF)    // 源端口低字节
	packet[udpStart+2] = byte(dstPort >> 8)      // 目标端口高字节
	packet[udpStart+3] = byte(dstPort & 0xFF)    // 目标端口低字节
	packet[udpStart+4] = byte(payloadLen >> 8)   // UDP长度高字节
	packet[udpStart+5] = byte(payloadLen & 0xFF) // UDP长度低字节
	packet[udpStart+6] = 0x00                    // 校验和高字节（稍后计算）
	packet[udpStart+7] = 0x00                    // 校验和低字节

	// 复制数据
	copy(packet[udpStart+8:], data)

	// 计算UDP校验和
	udpChecksum := checksum.CalculateUDPv6Checksum(srcIP, dstIP, packet[udpStart:])
	packet[udpStart+6] = byte(udpChecksum >> 8)
	packet[udpStart+7] = byte(udpChecksum & 0xFF)

	return packet
}

func constructIPv6ICMPPacket(srcIP, dstIP net.IP, data []byte) []byte {
	ipv6HeaderLen := 40
	icmpHeaderLen := 8
	totalLen := ipv6HeaderLen + icmpHeaderLen + len(data)

	packet := make([]byte, totalLen)

	// 构造IPv6头部
	packet[0] = 0x60 // 版本(6) + 流量类别高4位
	packet[1] = 0x00 // 流量类别低4位 + 流标签高4位
	packet[2] = 0x00 // 流标签中8位
	packet[3] = 0x00 // 流标签低8位
	payloadLen := icmpHeaderLen + len(data)
	packet[4] = byte(payloadLen >> 8)   // 载荷长度高字节
	packet[5] = byte(payloadLen & 0xFF) // 载荷长度低字节
	packet[6] = 58                      // 下一个头部(ICMPv6)
	packet[7] = 64                      // 跳数限制

	// 源IP地址 (16字节)
	copy(packet[8:24], srcIP.To16())
	// 目标IP地址 (16字节)
	copy(packet[24:40], dstIP.To16())

	// 构造ICMPv6头部
	icmpStart := ipv6HeaderLen
	packet[icmpStart] = 128 // 类型 (Echo Request)
	packet[icmpStart+1] = 0 // 代码
	packet[icmpStart+2] = 0 // 校验和高字节（稍后计算）
	packet[icmpStart+3] = 0 // 校验和低字节
	packet[icmpStart+4] = 0 // 标识符高字节
	packet[icmpStart+5] = 0 // 标识符低字节
	packet[icmpStart+6] = 0 // 序列号高字节
	packet[icmpStart+7] = 1 // 序列号低字节

	// 复制数据
	copy(packet[icmpStart+8:], data)

	// 计算ICMPv6校验和
	icmpChecksum := checksum.CalculateICMPv6Checksum(srcIP, dstIP, packet[icmpStart:])
	packet[icmpStart+2] = byte(icmpChecksum >> 8)
	packet[icmpStart+3] = byte(icmpChecksum & 0xFF)

	return packet
}
