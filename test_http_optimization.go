package main

import (
	"fmt"
	"log"
	"net/http"
	"time"
)

func main() {
	fmt.Println("🚀 HTTP优化测试")
	fmt.Println("================")
	
	// 创建一个简单的HTTP服务器来模拟目标服务器行为
	go func() {
		http.HandleFunc("/test", func(w http.ResponseWriter, r *http.Request) {
			fmt.Printf("📥 收到HTTP请求: %s %s\n", r.Method, r.URL.Path)
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{"status": "ok", "message": "HTTP optimization test"}`))
			fmt.Printf("📤 发送HTTP响应\n")
		})
		
		fmt.Println("🌐 启动测试HTTP服务器在 :8888")
		log.Fatal(http.ListenAndServe(":8888", nil))
	}()
	
	// 等待服务器启动
	time.Sleep(time.Second)
	
	fmt.Println("\n📋 测试说明:")
	fmt.Println("1. 启动cyber-bastion服务器和客户端")
	fmt.Println("2. 在客户端机器上运行:")
	fmt.Println("   wget --bind-address ************* -O- http://127.0.0.1:8888/test")
	fmt.Println("3. 观察服务器日志中的HTTP优化处理")
	fmt.Println("\n🔍 期望看到的日志:")
	fmt.Println("   - HTTP/HTTPS traffic detected, using optimized handling")
	fmt.Println("   - Handling HTTP/HTTPS traffic with optimized strategy")
	fmt.Println("   - Marked connection as HTTP-used")
	
	fmt.Println("\n⏳ HTTP测试服务器运行中... (按Ctrl+C停止)")
	
	// 保持服务器运行
	select {}
}
