# 单服务器客户端配置
# 服务器IP: *************

# 基本连接配置
server_host: "*************"
server_port: 8080
connect_timeout: 10
heartbeat_interval: 30
reconnect_delay: 5
max_reconnect_delay: 60
max_reconnect_tries: 10
auth_token: "your-auth-token-here"

# TLS配置
enable_tls: true
tls_cert_file: "certs/client.crt"
tls_key_file: "certs/client.key"
tls_ca_file: "certs/ca.crt"
tls_skip_verify: false

# 加密配置
enable_encryption: true
encryption_key: "your-encryption-key-change-in-production"

# TUN接口配置
tun:
  enabled: true
  name: "utun9"
  ip: "********"
  netmask: "*************"
  mtu: 1500

# 日志配置
logger:
  level: "info"
  format: "console"
  output: "stdout"

# 多隧道配置（推荐使用）
tunnels:
  - name: "primary"
    server: "*************"
    port: 8080
    protocol: "tcp"
    priority: 1
    enabled: true
    auth_token: "primary-tunnel-token"
    enable_tls: true
    tls_cert_file: "certs/client.crt"
    tls_key_file: "certs/client.key"
    tls_ca_file: "certs/ca.crt"
    tls_skip_verify: false
    enable_encryption: true
    encryption_key: "your-encryption-key-change-in-production"
    connect_timeout: 10
    heartbeat_interval: 30
    reconnect_delay: 5
    max_reconnect_delay: 60
    max_reconnect_tries: 10

  - name: "websocket"
    server: "*************"
    port: 8081
    protocol: "websocket"
    priority: 2
    enabled: true
    auth_token: "websocket-tunnel-token"
    enable_tls: true
    tls_cert_file: "certs/client.crt"
    tls_key_file: "certs/client.key"
    tls_ca_file: "certs/ca.crt"
    tls_skip_verify: false
    enable_encryption: true
    encryption_key: "your-encryption-key-change-in-production"
    connect_timeout: 15
    heartbeat_interval: 45
    reconnect_delay: 10
    max_reconnect_delay: 120
    max_reconnect_tries: 5

# 路由策略配置
routing:
  strategy: "priority"  # 使用优先级策略
  rules:
    - name: "internal_networks"
      destination: "10.0.0.0/8"
      action: "tunnel"
      tunnel: "primary"
      priority: 10

    - name: "private_networks"
      destination: "***********/16"
      action: "tunnel"
      tunnel: "primary"
      priority: 20

    - name: "corporate_networks"
      destination: "**********/12"
      action: "tunnel"
      tunnel: "primary"
      priority: 30

    - name: "local_network"
      destination: "***********/24"
      action: "direct"
      priority: 5

    - name: "default_route"
      destination: "0.0.0.0/0"
      action: "tunnel"
      priority: 100

# 控制器配置
controller:
  # 健康检查配置
  health_check_interval: 30
  health_check_timeout: 5

  # 故障转移配置
  failover_enabled: true
  failover_threshold: 3
  failover_cooldown: 60

  # 负载均衡配置（单服务器环境不需要）
  load_balance_enabled: false
  load_balance_method: "round_robin"

  # 监控配置
  metrics_enabled: true
  metrics_interval: 60
