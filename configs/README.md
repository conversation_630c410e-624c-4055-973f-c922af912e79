# 配置文件说明

## 服务器信息
- **服务器IP**: `*************`
- **主要端口**: `8080` (TCP)
- **WebSocket端口**: `8081` (WebSocket over TLS)
- **备用端口**: `443` (TCP over TLS)

## 配置文件概览

### 客户端配置

#### 1. `client-single-server.yaml` (推荐)
- **用途**: 单服务器环境的客户端配置
- **特点**: 
  - 配置了主隧道 (TCP, 8080) 和备用隧道 (WebSocket, 8081)
  - 使用优先级路由策略
  - 启用故障转移
  - 适合生产环境使用

#### 2. `client-v2.yaml`
- **用途**: 完整的多隧道配置示例
- **特点**: 
  - 展示所有可用配置选项
  - 包含多个隧道配置
  - 适合学习和参考

### 服务端配置

#### 1. `server-single.yaml` (推荐)
- **用途**: 单服务器环境的服务端配置
- **特点**:
  - 监听多个端口 (8080 TCP, 8081 WebSocket)
  - 优化的单服务器设置
  - 禁用了不必要的集群功能
  - 适合生产环境使用

#### 2. `server-v2.yaml`
- **用途**: 完整的服务端配置示例
- **特点**:
  - 展示所有可用配置选项
  - 包含集群和高可用配置
  - 适合学习和参考

## 快速开始

### 1. 客户端配置
```bash
# 复制推荐配置
cp configs/client-single-server.yaml configs/client.yaml

# 根据需要修改以下配置项：
# - auth_token: 设置认证令牌
# - encryption_key: 设置加密密钥
# - tls_cert_file, tls_key_file, tls_ca_file: 设置证书路径
```

### 2. 服务端配置
```bash
# 复制推荐配置
cp configs/server-single.yaml configs/server.yaml

# 根据需要修改以下配置项：
# - auth_token: 设置认证令牌（与客户端保持一致）
# - tls_cert_file, tls_key_file, tls_ca_file: 设置证书路径
```

## 重要配置项说明

### 认证和加密配置
- **auth_token**: 客户端和服务端必须使用相同的认证令牌
- **enable_encryption**: 是否启用数据加密（强烈推荐启用）
- **encryption_key**: 用于数据加密的密钥，客户端和服务端必须完全一致
  - 最少16个字符
  - 建议使用强随机密码
  - 生产环境必须更改默认值

### TLS证书配置
- **tls_cert_file**: 服务器证书文件路径
- **tls_key_file**: 服务器私钥文件路径
- **tls_ca_file**: CA证书文件路径
- **tls_skip_verify**: 是否跳过证书验证（生产环境建议设为false）

### 网络配置
- **TUN接口**: 客户端虚拟网卡，默认IP为 `********/24`
- **路由规则**: 控制哪些流量通过隧道，哪些直接访问

### 性能配置
- **worker_count**: 工作线程数，建议设为CPU核心数
- **queue_size**: 队列大小，影响并发处理能力
- **max_connections**: 最大连接数

## 隧道配置说明

### 主隧道 (primary)
- **协议**: TCP
- **端口**: 8080
- **优先级**: 1 (最高)
- **用途**: 主要的数据传输通道

### WebSocket隧道 (websocket)
- **协议**: WebSocket over TLS
- **端口**: 8081
- **优先级**: 2
- **用途**: 备用通道，适合穿越防火墙

### 路由策略

#### 默认规则
1. **本地网络** (`***********/24`) → 直接访问
2. **内网地址** (`10.0.0.0/8`, `***********/16`, `**********/12`) → 通过隧道
3. **其他地址** → 通过隧道

#### 自定义规则
可以在 `routing.rules` 中添加自定义路由规则：
```yaml
routing:
  rules:
    - name: "custom_rule"
      destination: "***********/24"
      action: "block"  # tunnel, direct, block
      priority: 1
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查服务器IP和端口是否正确
   - 确认防火墙设置
   - 验证认证令牌是否匹配

2. **TLS错误**
   - 检查证书文件路径
   - 确认证书有效期
   - 验证CA证书配置

3. **性能问题**
   - 调整worker_count
   - 增加queue_size
   - 检查网络带宽

### 日志配置
```yaml
logger:
  level: "debug"  # 调试时使用debug级别
  format: "text"  # 人类可读格式
  output: "stdout"
```

## 安全建议

1. **更改默认密钥**:
   - 修改 `auth_token` 和 `encryption_key`
   - 确保客户端和服务端使用相同的加密密钥
   - 使用强随机密码（至少16个字符）
2. **启用加密**: 确保 `enable_encryption: true`
3. **使用有效证书**: 不要在生产环境中跳过证书验证
4. **限制访问**: 配置防火墙规则，只允许必要的端口访问
5. **定期更新**: 定期更新证书和密钥
6. **监控日志**: 启用日志记录并定期检查异常

## 性能优化

1. **调整工作线程数**: 根据CPU核心数设置 `worker_count`
2. **优化缓冲区**: 根据网络环境调整 `read_buffer_size` 和 `write_buffer_size`
3. **启用缓存**: 确保 `enable_cache` 为 true
4. **调整超时**: 根据网络延迟调整各种超时设置
