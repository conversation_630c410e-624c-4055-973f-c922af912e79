# Cyber Bastion Server Configuration

# Server settings
host: "0.0.0.0"
port: 8080
read_timeout: 30    # seconds
write_timeout: 30   # seconds
max_clients: 100
auth_token: "cyber-bastion-secret-token"

# Security settings
security:
  enable_ip_filter: true     # Enable IP whitelist/blacklist filtering
  allowed_ips: []           # IP whitelist (empty = allow all except blocked)
    # - "***********/24"
    # - "********"
  blocked_ips:              # IP blacklist
    - "*************"       # Block the attacking IP from the error log
  max_conn_per_ip: 5        # Maximum connections per IP
  rate_limit_enabled: true  # Enable connection rate limiting
  rate_limit_per_min: 10    # Maximum connections per minute per IP

  # TLS encryption settings
  enable_tls: true         # Enable TLS encryption (requires certificates)
  tls_cert_file: "certs/server.crt"  # Server certificate file
  tls_key_file: "certs/server.key"   # Server private key file
  tls_ca_file: "certs/ca.crt"        # CA certificate for client verification
  tls_skip_verify: false    # Skip certificate verification (not recommended for production)

  # Message-level encryption settings
  enable_encryption: true  # Enable AES encryption for message content
  encryption_key: "change-this-encryption-key-in-production-32-bytes"  # 32-byte encryption key

# Transparent forwarding settings
forwarding:
  enabled: true            # Enable transparent packet forwarding
  bind_interface: false    # Bind forwarding to specific network interface
  interface_name: ""       # Network interface name to bind (empty = use default routing)
  allowed_networks: []     # Allowed destination networks in CIDR format (empty = allow all)
    # - "***********/16"
    # - "10.0.0.0/8"
  blocked_networks:        # Blocked destination networks in CIDR format
    - "*********/8"        # Block localhost
    - "***********/16"     # Block link-local addresses

# Logger settings
logger:
  level: "info"        # debug, info, warn, error
  format: "console"       # json, console
  output: "stdout"     # stdout, stderr, or file path
  max_size: 100        # MB
  max_backups: 3
  max_age: 28          # days
  compress: true
