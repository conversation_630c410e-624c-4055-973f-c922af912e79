# 单服务器配置
# 服务器IP: *************

# 基本服务器配置
host: "0.0.0.0"  # 监听所有接口
port: 8080
read_timeout: 30
write_timeout: 30
max_clients: 1000
auth_token: "your-auth-token-here"

# 安全配置
security:
  enable_ip_filter: false  # 单服务器环境可以关闭IP过滤
  allowed_ips: []
  blocked_ips: []
  max_conn_per_ip: 10
  rate_limit_enabled: true
  rate_limit_per_min: 100
  enable_tls: true
  tls_cert_file: "certs/server.crt"
  tls_key_file: "certs/server.key"
  tls_ca_file: "certs/ca.crt"
  enable_encryption: true
  encryption_key: "your-encryption-key-change-in-production"

# 转发配置
forwarding:
  enabled: true
  bind_interface: false  # 单服务器环境不绑定特定接口
  interface_name: ""
  allowed_networks:
    - "0.0.0.0/0"  # 允许所有网络
  blocked_networks:
    - "*********/8"    # 阻止本地回环
    - "***********/16" # 阻止链路本地地址

# 日志配置
logger:
  level: "info"
  format: "console"
  output: "stdout"

# 多协议监听器配置
listeners:
  - protocol: "tcp"
    address: "0.0.0.0"
    port: 8080
    enable_tls: true
    tls_cert_file: "certs/server.crt"
    tls_key_file: "certs/server.key"
    max_connections: 1000
    read_timeout: 30
    write_timeout: 30
    idle_timeout: 300

  - protocol: "websocket"
    address: "0.0.0.0"
    port: 8081
    enable_tls: true
    tls_cert_file: "certs/server.crt"
    tls_key_file: "certs/server.key"
    websocket_path: "/ws"
    max_connections: 500
    read_timeout: 60
    write_timeout: 60
    idle_timeout: 600
    headers:
      "X-Server-Version": "2.0"
      "X-Protocol": "cyber-bastion-ws"

# 优化转发处理器配置
optimized_forwarding:
  enabled: true
  worker_count: 4
  queue_size: 1000
  max_idle_conns: 10
  max_active_conns: 100
  idle_timeout: 300
  dial_timeout: 10
  enable_metrics: true
  metrics_interval: 30
  enable_cache: true
  cache_size: 1000
  cache_ttl: 300

# NAT配置
nat:
  mapping_timeout: 600
  cleanup_interval: 300
  port_range_start: 10000
  port_range_end: 65535
  server_ip: "*************"  # 实际服务器IP
  max_mappings: 10000
  enable_metrics: true

# 地址映射缓存配置
address_mapping:
  ttl: 600
  cleanup_interval: 300
  max_entries: 10000

# 网络出口管理配置
egress:
  default_interface: "eth0"
  interfaces:
    - name: "eth0"
      priority: 1
      enabled: true
      max_bandwidth: "1Gbps"
  
  # 路由表配置
  routes:
    - destination: "0.0.0.0/0"
      interface: "eth0"
      gateway: "auto"  # 自动检测网关
      metric: 100

# 监控和指标配置
monitoring:
  enabled: true
  metrics_interval: 60
  health_check_interval: 30
  
  # 性能阈值
  thresholds:
    max_cpu_usage: 80
    max_memory_usage: 80
    max_connection_rate: 1000
    max_packet_loss: 5

# 高可用配置（单服务器环境禁用）
high_availability:
  enabled: false

# 性能调优配置
performance:
  # 系统级配置
  max_open_files: 65536
  tcp_keepalive: true
  tcp_nodelay: true
  
  # 缓冲区配置
  read_buffer_size: 4096
  write_buffer_size: 4096
  
  # 并发配置
  max_goroutines: 10000
  gc_percent: 100
