# Cyber Bastion Client Configuration

# Server connection settings
server_host: "localhost"
server_port: 8080
connect_timeout: 10      # seconds
heartbeat_interval: 30   # seconds
reconnect_delay: 5       # seconds (initial delay)
max_reconnect_delay: 60  # seconds (maximum delay for exponential backoff)
max_reconnect_tries: 10  # maximum number of reconnection attempts
auth_token: "cyber-bastion-secret-token"

# TLS encryption settings
enable_tls: true         # Enable TLS encryption
tls_cert_file: "certs/client.crt"  # Client certificate file (for mutual TLS)
tls_key_file: "certs/client.key"   # Client private key file
tls_ca_file: "certs/ca.crt"        # CA certificate for server verification
tls_skip_verify: true     # Skip certificate verification (allows self-signed certificates)

# Message-level encryption settings
enable_encryption: true  # Enable AES encryption for message content
encryption_key: "change-this-encryption-key-in-production-32-bytes"  # 32-byte encryption key

# Transparent forwarding settings (TUN interface)
tun:
  enabled: true           # Enable transparent forwarding via TUN interface
  name: "cyber-tun"        # TUN interface name
  ip: "********"          # TUN interface IP address
  netmask: "*************" # TUN interface netmask
  mtu: 1500               # TUN interface MTU size

# Logger settings
logger:
  level: "info"        # debug, info, warn, error
  format: "console"       # json, console
  output: "stdout"     # stdout, stderr, or file path
  max_size: 100        # MB
  max_backups: 3
  max_age: 28          # days
  compress: true
