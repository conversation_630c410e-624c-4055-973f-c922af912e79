# 新架构客户端配置示例
# 支持多隧道、路由策略、负载均衡等高级功能

# 兼容性配置（单隧道模式，向后兼容）
server_host: "*************"
server_port: 8080
connect_timeout: 10
heartbeat_interval: 30
reconnect_delay: 5
max_reconnect_delay: 60
max_reconnect_tries: 10
auth_token: "your-auth-token-here"
enable_tls: true
tls_cert_file: "certs/client.crt"
tls_key_file: "certs/client.key"
tls_ca_file: "certs/ca.crt"
tls_skip_verify: false
enable_encryption: true
encryption_key: "your-encryption-key-change-in-production"

# TUN接口配置
tun:
  enabled: true
  name: "cyber-tun0"
  ip: "********"
  netmask: "*************"
  mtu: 1500

# 日志配置
logger:
  level: "info"
  format: "json"
  output: "stdout"

# 新架构配置（多隧道模式）
tunnels:
  - name: "primary"
    server: "*************"
    port: 8080
    protocol: "tcp"
    priority: 1
    enabled: true
    auth_token: "primary-tunnel-token"
    enable_tls: true
    tls_cert_file: "certs/client.crt"
    tls_key_file: "certs/client.key"
    tls_ca_file: "certs/ca.crt"
    tls_skip_verify: false
    enable_encryption: true
    encryption_key: "your-encryption-key-change-in-production"
    connect_timeout: 10
    heartbeat_interval: 30
    reconnect_delay: 5
    max_reconnect_delay: 60
    max_reconnect_tries: 10

  - name: "secondary"
    server: "*************"
    port: 8081
    protocol: "websocket"
    priority: 2
    enabled: true
    auth_token: "secondary-tunnel-token"
    enable_tls: true
    tls_cert_file: "certs/client.crt"
    tls_key_file: "certs/client.key"
    tls_ca_file: "certs/ca.crt"
    tls_skip_verify: false
    enable_encryption: true
    encryption_key: "your-encryption-key-change-in-production"
    connect_timeout: 15
    heartbeat_interval: 45
    reconnect_delay: 10
    max_reconnect_delay: 120
    max_reconnect_tries: 5

  - name: "backup"
    server: "*************"
    port: 443
    protocol: "tcp"
    priority: 3
    enabled: false  # 备用隧道，默认禁用
    auth_token: "backup-tunnel-token"
    enable_tls: true
    tls_cert_file: "certs/client.crt"
    tls_key_file: "certs/client.key"
    tls_ca_file: "certs/ca.crt"
    tls_skip_verify: false
    enable_encryption: true
    encryption_key: "your-encryption-key-change-in-production"
    connect_timeout: 20
    heartbeat_interval: 60
    reconnect_delay: 15
    max_reconnect_delay: 180
    max_reconnect_tries: 3

# 路由策略配置
routing:
  strategy: "priority"  # priority, round_robin, load_balance, failover
  rules:
    - name: "internal_networks"
      destination: "10.0.0.0/8"
      action: "tunnel"
      tunnel: "primary"
      priority: 10

    - name: "private_networks"
      destination: "***********/16"
      action: "tunnel"
      tunnel: "primary"
      priority: 20

    - name: "corporate_vpn"
      destination: "**********/12"
      action: "tunnel"
      tunnel: "primary"
      priority: 30

    - name: "local_network"
      destination: "***********/24"
      action: "direct"
      priority: 5

    - name: "blocked_sites"
      destination: "***********/24"
      action: "block"
      priority: 1

    - name: "default_route"
      destination: "0.0.0.0/0"
      action: "tunnel"
      priority: 100

# 控制器配置
controller:
  # 健康检查配置
  health_check_interval: 30
  health_check_timeout: 5

  # 故障转移配置
  failover_enabled: true
  failover_threshold: 3
  failover_cooldown: 60

  # 负载均衡配置
  load_balance_enabled: false
  load_balance_method: "round_robin"  # round_robin, least_conn, weighted

  # 监控配置
  metrics_enabled: true
  metrics_interval: 60
