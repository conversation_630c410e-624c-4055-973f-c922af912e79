#!/bin/bash

# Git Build Fix Script for Cyber Bastion
# This script helps diagnose and fix Git-related build issues

set -e

echo "=== Cyber Bastion Git Build Fix Script ==="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in a Git repository
print_info "Checking Git repository status..."

if ! git rev-parse --git-dir > /dev/null 2>&1; then
    print_warning "Not in a Git repository. This is fine for building."
    print_info "You can use 'make build' or 'make build-all' which disable VCS stamping."
    exit 0
fi

print_success "In a Git repository"

# Check Git status
print_info "Checking Git working directory status..."

if ! git status --porcelain > /dev/null 2>&1; then
    print_error "Git status command failed"
    print_info "This might be due to:"
    print_info "  1. Corrupted Git repository"
    print_info "  2. Permission issues"
    print_info "  3. Incomplete Git clone"
    print_info ""
    print_info "Solutions:"
    print_info "  1. Use 'make build' (disables VCS stamping)"
    print_info "  2. Re-clone the repository"
    print_info "  3. Check file permissions"
    exit 1
fi

# Check if there are uncommitted changes
if [ -n "$(git status --porcelain)" ]; then
    print_warning "Working directory has uncommitted changes"
    print_info "Files with changes:"
    git status --porcelain
    print_info ""
    print_info "This might cause VCS stamping to fail."
    print_info "Options:"
    print_info "  1. Commit your changes: git add . && git commit -m 'Your message'"
    print_info "  2. Use 'make build' (disables VCS stamping)"
    print_info "  3. Use 'make build-with-vcs' to try building with VCS info anyway"
else
    print_success "Working directory is clean"
    print_info "You can use 'make build-with-vcs' or 'make build-all-with-vcs' for builds with VCS info"
fi

# Check Git configuration
print_info "Checking Git configuration..."

if ! git config user.name > /dev/null 2>&1; then
    print_warning "Git user.name not configured"
    print_info "Set it with: git config user.name 'Your Name'"
fi

if ! git config user.email > /dev/null 2>&1; then
    print_warning "Git user.email not configured"
    print_info "Set it with: git config user.email '<EMAIL>'"
fi

# Check if we can get commit info
print_info "Testing Git commit information access..."

if git rev-parse HEAD > /dev/null 2>&1; then
    COMMIT=$(git rev-parse --short HEAD)
    BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
    print_success "Git commit info accessible"
    print_info "Current commit: $COMMIT"
    print_info "Current branch: $BRANCH"
else
    print_error "Cannot access Git commit information"
    print_info "This repository might be incomplete or corrupted"
    print_info "Use 'make build' to build without VCS information"
fi

print_info ""
print_info "=== Build Recommendations ==="
print_info ""

if [ -n "$(git status --porcelain 2>/dev/null)" ]; then
    print_info "🔧 For development (with uncommitted changes):"
    print_info "   make build          # Build without VCS info (recommended)"
    print_info "   make build-all      # Multi-platform build without VCS info"
    print_info ""
    print_info "🚀 For clean builds:"
    print_info "   git add . && git commit -m 'Your changes'"
    print_info "   make build-with-vcs # Build with VCS info"
else
    print_info "🚀 For production builds (clean repo):"
    print_info "   make build-with-vcs     # Build with VCS info"
    print_info "   make build-all-with-vcs # Multi-platform build with VCS info"
    print_info ""
    print_info "🔧 For CI/CD or when VCS info is not needed:"
    print_info "   make build              # Build without VCS info"
    print_info "   make build-all          # Multi-platform build without VCS info"
fi

print_info ""
print_success "Git build diagnosis complete!"
