#!/bin/bash

# 单服务器环境快速配置脚本
# 服务器IP: *************

set -e

echo "🚀 Cyber Bastion 单服务器环境配置脚本"
echo "服务器IP: *************"
echo "========================================="

# 检查是否在项目根目录
if [ ! -f "go.mod" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p certs
mkdir -p logs
mkdir -p data

# 生成随机加密密钥
echo "🔐 生成随机加密密钥..."
ENCRYPTION_KEY=$(openssl rand -base64 32)
AUTH_TOKEN=$(openssl rand -hex 16)

echo "生成的密钥:"
echo "  认证令牌: $AUTH_TOKEN"
echo "  加密密钥: $ENCRYPTION_KEY"

# 复制推荐配置文件
echo "📋 复制推荐配置文件..."

# 客户端配置
if [ ! -f "configs/client.yaml" ]; then
    cp configs/client-single-server.yaml configs/client.yaml
    # 替换默认密钥
    sed -i.bak "s/your-auth-token-here/$AUTH_TOKEN/g" configs/client.yaml
    sed -i.bak "s/your-encryption-key-change-in-production/$ENCRYPTION_KEY/g" configs/client.yaml
    rm configs/client.yaml.bak
    echo "✅ 已创建客户端配置: configs/client.yaml"
else
    echo "⚠️  客户端配置已存在: configs/client.yaml"
fi

# 服务端配置
if [ ! -f "configs/server.yaml" ]; then
    cp configs/server-single.yaml configs/server.yaml
    # 替换默认密钥
    sed -i.bak "s/server-auth-token/$AUTH_TOKEN/g" configs/server.yaml
    sed -i.bak "s/your-encryption-key-change-in-production/$ENCRYPTION_KEY/g" configs/server.yaml
    rm configs/server.yaml.bak
    echo "✅ 已创建服务端配置: configs/server.yaml"
else
    echo "⚠️  服务端配置已存在: configs/server.yaml"
fi

# 生成自签名证书（仅用于测试）
echo "🔐 生成测试用自签名证书..."
if [ ! -f "certs/server.crt" ]; then
    # 生成CA私钥
    openssl genrsa -out certs/ca.key 4096
    
    # 生成CA证书
    openssl req -new -x509 -days 365 -key certs/ca.key -out certs/ca.crt -subj "/C=CN/ST=Beijing/L=Beijing/O=CyberBastion/OU=CA/CN=CyberBastion-CA"
    
    # 生成服务器私钥
    openssl genrsa -out certs/server.key 4096
    
    # 生成服务器证书请求
    openssl req -new -key certs/server.key -out certs/server.csr -subj "/C=CN/ST=Beijing/L=Beijing/O=CyberBastion/OU=Server/CN=*************"
    
    # 创建扩展文件，支持IP地址
    cat > certs/server.ext << EOF
[v3_req]
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature, nonRepudiation, keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth, clientAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = cyber-bastion
DNS.3 = *.localhost
IP.1 = 127.0.0.1
IP.2 = ::1
IP.3 = *************
EOF
    
    # 生成服务器证书
    openssl x509 -req -in certs/server.csr -CA certs/ca.crt -CAkey certs/ca.key -CAcreateserial -out certs/server.crt -days 365 -extensions v3_req -extfile certs/server.ext
    
    # 生成客户端私钥
    openssl genrsa -out certs/client.key 4096
    
    # 生成客户端证书请求
    openssl req -new -key certs/client.key -out certs/client.csr -subj "/C=CN/ST=Beijing/L=Beijing/O=CyberBastion/OU=Client/CN=client"
    
    # 生成客户端证书
    openssl x509 -req -in certs/client.csr -CA certs/ca.crt -CAkey certs/ca.key -CAcreateserial -out certs/client.crt -days 365
    
    # 清理临时文件
    rm certs/server.csr certs/client.csr certs/server.ext
    
    echo "✅ 证书生成完成"
    echo "   CA证书: certs/ca.crt"
    echo "   服务器证书: certs/server.crt"
    echo "   服务器私钥: certs/server.key"
    echo "   客户端证书: certs/client.crt"
    echo "   客户端私钥: certs/client.key"
else
    echo "⚠️  证书已存在，跳过生成"
fi

# 设置证书权限
chmod 600 certs/*.key
chmod 644 certs/*.crt

# 编译项目
echo "🔨 编译项目..."
go build -o bin/cyber-bastion-server ./cmd/server
go build -o bin/cyber-bastion-client ./cmd/client

echo "✅ 编译完成"
echo "   服务端: bin/cyber-bastion-server"
echo "   客户端: bin/cyber-bastion-client"

# 生成启动脚本
echo "📝 生成启动脚本..."

# 服务端启动脚本
cat > scripts/start-server.sh << 'EOF'
#!/bin/bash
cd "$(dirname "$0")/.."
echo "🚀 启动 Cyber Bastion 服务端..."
echo "服务器IP: *************"
echo "TCP端口: 8080"
echo "WebSocket端口: 8081"
echo "=========================="
./bin/cyber-bastion-server -config configs/server.yaml
EOF

# 客户端启动脚本
cat > scripts/start-client.sh << 'EOF'
#!/bin/bash
cd "$(dirname "$0")/.."
echo "🚀 启动 Cyber Bastion 客户端..."
echo "连接服务器: *************:8080"
echo "=========================="
./bin/cyber-bastion-client -config configs/client.yaml
EOF

chmod +x scripts/start-server.sh
chmod +x scripts/start-client.sh

echo "✅ 启动脚本生成完成"
echo "   服务端启动: scripts/start-server.sh"
echo "   客户端启动: scripts/start-client.sh"

# 显示配置摘要
echo ""
echo "📋 配置摘要"
echo "========================================="
echo "服务器IP: *************"
echo "主端口 (TCP): 8080"
echo "WebSocket端口: 8081"
echo "TUN接口: cyber-tun0 (********/24)"
echo "证书位置: certs/"
echo "配置文件: configs/client.yaml, configs/server.yaml"
echo ""
echo "🎯 下一步操作:"
echo "1. 在服务器上运行: ./scripts/start-server.sh"
echo "2. 在客户端上运行: ./scripts/start-client.sh"
echo ""
echo "⚠️  注意事项:"
echo "- 生成的证书仅用于测试，生产环境请使用正式证书"
echo "- 已自动生成随机的认证令牌和加密密钥"
echo "- 客户端和服务端已配置相同的加密密钥"
echo "- 确保服务器防火墙开放 8080 和 8081 端口"
echo "- 请妥善保管生成的密钥信息"
echo ""
echo "✅ 配置完成！"
