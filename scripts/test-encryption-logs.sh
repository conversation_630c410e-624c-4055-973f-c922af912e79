#!/bin/bash

# 加密状态日志测试脚本
# 测试不同加密配置下的启动日志显示

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 备份原始配置
backup_configs() {
    print_info "备份原始配置文件..."
    cp configs/server.yaml configs/server.yaml.backup
    cp configs/client.yaml configs/client.yaml.backup
    print_success "配置文件备份完成"
}

# 恢复原始配置
restore_configs() {
    print_info "恢复原始配置文件..."
    mv configs/server.yaml.backup configs/server.yaml
    mv configs/client.yaml.backup configs/client.yaml
    print_success "配置文件恢复完成"
}

# 构建项目
build_project() {
    print_info "构建项目..."
    
    if ! go build -o bin/server ./cmd/server; then
        print_error "服务器构建失败"
        exit 1
    fi
    
    if ! go build -o bin/client ./cmd/client; then
        print_error "客户端构建失败"
        exit 1
    fi
    
    print_success "项目构建完成"
}

# 测试场景1：加密启用，默认密钥
test_encryption_enabled_default_key() {
    print_info "测试场景1：加密启用，默认密钥"
    
    # 修改配置
    sed -i '' 's/enable_encryption: false/enable_encryption: true/' configs/server.yaml
    sed -i '' 's/enable_encryption: false/enable_encryption: true/' configs/client.yaml
    
    print_info "启动服务器..."
    timeout 5 ./bin/server --config configs/server.yaml --log-level info > server_test1.log 2>&1 &
    SERVER_PID=$!
    sleep 2
    
    print_info "启动客户端..."
    timeout 3 ./bin/client --config configs/client.yaml --log-level info > client_test1.log 2>&1 &
    CLIENT_PID=$!
    sleep 2
    
    # 停止进程
    kill $SERVER_PID $CLIENT_PID 2>/dev/null || true
    wait $SERVER_PID $CLIENT_PID 2>/dev/null || true
    
    # 检查日志
    print_info "检查服务器日志..."
    if grep -q '"encryption_enabled":true' server_test1.log; then
        print_success "服务器正确显示加密已启用"
    else
        print_error "服务器未正确显示加密状态"
    fi
    
    if grep -q "Using default encryption key" server_test1.log; then
        print_success "服务器正确警告使用默认密钥"
    else
        print_warning "服务器未警告默认密钥"
    fi
    
    print_info "检查客户端日志..."
    if grep -q '"encryption_enabled":true' client_test1.log; then
        print_success "客户端正确显示加密已启用"
    else
        print_error "客户端未正确显示加密状态"
    fi
    
    if grep -q "Using default encryption key" client_test1.log; then
        print_success "客户端正确警告使用默认密钥"
    else
        print_warning "客户端未警告默认密钥"
    fi
    
    # 显示日志示例
    print_info "服务器启动日志示例："
    grep "Starting Cyber Bastion Server" server_test1.log || echo "未找到启动日志"
    
    print_info "客户端启动日志示例："
    grep "Starting Cyber Bastion Client" client_test1.log || echo "未找到启动日志"
    
    echo ""
}

# 测试场景2：加密禁用
test_encryption_disabled() {
    print_info "测试场景2：加密禁用"
    
    # 修改配置
    sed -i '' 's/enable_encryption: true/enable_encryption: false/' configs/server.yaml
    sed -i '' 's/enable_encryption: true/enable_encryption: false/' configs/client.yaml
    
    print_info "启动服务器..."
    timeout 5 ./bin/server --config configs/server.yaml --log-level info > server_test2.log 2>&1 &
    SERVER_PID=$!
    sleep 2
    
    print_info "启动客户端..."
    timeout 3 ./bin/client --config configs/client.yaml --log-level info > client_test2.log 2>&1 &
    CLIENT_PID=$!
    sleep 2
    
    # 停止进程
    kill $SERVER_PID $CLIENT_PID 2>/dev/null || true
    wait $SERVER_PID $CLIENT_PID 2>/dev/null || true
    
    # 检查日志
    print_info "检查服务器日志..."
    if grep -q '"encryption_enabled":false' server_test2.log; then
        print_success "服务器正确显示加密已禁用"
    else
        print_error "服务器未正确显示加密状态"
    fi
    
    print_info "检查客户端日志..."
    if grep -q '"encryption_enabled":false' client_test2.log; then
        print_success "客户端正确显示加密已禁用"
    else
        print_error "客户端未正确显示加密状态"
    fi
    
    # 显示日志示例
    print_info "服务器启动日志示例："
    grep "Starting Cyber Bastion Server" server_test2.log || echo "未找到启动日志"
    
    print_info "客户端启动日志示例："
    grep "Starting Cyber Bastion Client" client_test2.log || echo "未找到启动日志"
    
    echo ""
}

# 测试场景3：加密启用，自定义密钥
test_encryption_enabled_custom_key() {
    print_info "测试场景3：加密启用，自定义密钥"
    
    # 修改配置
    sed -i '' 's/enable_encryption: false/enable_encryption: true/' configs/server.yaml
    sed -i '' 's/enable_encryption: false/enable_encryption: true/' configs/client.yaml
    sed -i '' 's/change-this-encryption-key-in-production-32-bytes/my-custom-production-encryption-key-32-bytes-long/' configs/server.yaml
    sed -i '' 's/change-this-encryption-key-in-production-32-bytes/my-custom-production-encryption-key-32-bytes-long/' configs/client.yaml
    
    print_info "启动服务器..."
    timeout 5 ./bin/server --config configs/server.yaml --log-level info > server_test3.log 2>&1 &
    SERVER_PID=$!
    sleep 2
    
    print_info "启动客户端..."
    timeout 3 ./bin/client --config configs/client.yaml --log-level info > client_test3.log 2>&1 &
    CLIENT_PID=$!
    sleep 2
    
    # 停止进程
    kill $SERVER_PID $CLIENT_PID 2>/dev/null || true
    wait $SERVER_PID $CLIENT_PID 2>/dev/null || true
    
    # 检查日志
    print_info "检查服务器日志..."
    if grep -q '"encryption_enabled":true' server_test3.log; then
        print_success "服务器正确显示加密已启用"
    else
        print_error "服务器未正确显示加密状态"
    fi
    
    if grep -q "Custom encryption key configured" server_test3.log; then
        print_success "服务器正确显示自定义密钥"
    else
        print_warning "服务器未显示自定义密钥信息"
    fi
    
    print_info "检查客户端日志..."
    if grep -q '"encryption_enabled":true' client_test3.log; then
        print_success "客户端正确显示加密已启用"
    else
        print_error "客户端未正确显示加密状态"
    fi
    
    if grep -q "Custom encryption key configured" client_test3.log; then
        print_success "客户端正确显示自定义密钥"
    else
        print_warning "客户端未显示自定义密钥信息"
    fi
    
    # 显示日志示例
    print_info "服务器启动日志示例："
    grep "Starting Cyber Bastion Server" server_test3.log || echo "未找到启动日志"
    
    print_info "客户端启动日志示例："
    grep "Starting Cyber Bastion Client" client_test3.log || echo "未找到启动日志"
    
    echo ""
}

# 清理测试文件
cleanup() {
    print_info "清理测试文件..."
    rm -f server_test*.log client_test*.log
    print_success "清理完成"
}

# 主函数
main() {
    print_info "开始加密状态日志测试..."
    
    # 设置清理函数
    trap 'restore_configs; cleanup' EXIT
    
    backup_configs
    build_project
    
    test_encryption_enabled_default_key
    test_encryption_disabled
    test_encryption_enabled_custom_key
    
    print_success "所有加密状态日志测试完成！"
    
    print_info "总结："
    echo "1. 加密启用时，日志显示 encryption_enabled: true 和 encryption_type: AES-256-GCM"
    echo "2. 加密禁用时，日志显示 encryption_enabled: false"
    echo "3. 使用默认密钥时，显示警告信息"
    echo "4. 使用自定义密钥时，显示确认信息和密钥提示"
}

# 运行主函数
main "$@"
