#!/bin/bash

# 数据转发测试脚本
# 测试ICMP、TCP、UDP数据转发是否正常工作

set -e

echo "🧪 Cyber Bastion 数据转发测试脚本"
echo "服务器IP: *************"
echo "================================"

# 检查是否在项目根目录
if [ ! -f "go.mod" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 检查是否已编译
if [ ! -f "bin/cyber-bastion-server" ] || [ ! -f "bin/cyber-bastion-client" ]; then
    echo "📦 编译程序..."
    go build -o bin/cyber-bastion-server ./cmd/server
    go build -o bin/cyber-bastion-client ./cmd/client
    echo "✅ 编译完成"
fi

# 检查配置文件
if [ ! -f "configs/server.yaml" ] || [ ! -f "configs/client.yaml" ]; then
    echo "📋 生成配置文件..."
    ./scripts/setup-single-server.sh
fi

# 验证证书
echo "🔐 验证证书配置..."
./scripts/verify-certificates.sh | grep -q "证书验证通过" || {
    echo "❌ 证书验证失败，请运行 ./scripts/setup-single-server.sh"
    exit 1
}

echo "✅ 证书验证通过"

# 启动服务端（后台运行）
echo "🚀 启动服务端..."
./bin/cyber-bastion-server -config configs/server.yaml > logs/server.log 2>&1 &
SERVER_PID=$!

# 等待服务端启动
sleep 3

# 检查服务端是否启动成功
if ! kill -0 $SERVER_PID 2>/dev/null; then
    echo "❌ 服务端启动失败"
    cat logs/server.log
    exit 1
fi

echo "✅ 服务端启动成功 (PID: $SERVER_PID)"

# 启动客户端（后台运行）
echo "🚀 启动客户端..."
./bin/cyber-bastion-client -config configs/client.yaml > logs/client.log 2>&1 &
CLIENT_PID=$!

# 等待客户端启动和连接
sleep 5

# 检查客户端是否启动成功
if ! kill -0 $CLIENT_PID 2>/dev/null; then
    echo "❌ 客户端启动失败"
    cat logs/client.log
    kill $SERVER_PID 2>/dev/null || true
    exit 1
fi

echo "✅ 客户端启动成功 (PID: $CLIENT_PID)"

# 检查TUN接口是否创建
echo "🔍 检查TUN接口..."
if ip addr show cyber-tun0 >/dev/null 2>&1; then
    echo "✅ TUN接口 cyber-tun0 已创建"
    ip addr show cyber-tun0 | grep "inet "
else
    echo "❌ TUN接口未创建"
    kill $CLIENT_PID $SERVER_PID 2>/dev/null || true
    exit 1
fi

# 清理函数
cleanup() {
    echo ""
    echo "🧹 清理进程..."
    kill $CLIENT_PID $SERVER_PID 2>/dev/null || true
    sleep 2
    
    # 强制杀死如果还在运行
    kill -9 $CLIENT_PID $SERVER_PID 2>/dev/null || true
    
    echo "✅ 清理完成"
}

# 设置清理陷阱
trap cleanup EXIT

# 测试函数
test_icmp() {
    echo ""
    echo "🏓 测试ICMP (ping)..."
    
    # 测试ping到公共DNS
    if ping -c 3 -W 5 ******* >/dev/null 2>&1; then
        echo "✅ ICMP测试成功: ping *******"
        return 0
    else
        echo "❌ ICMP测试失败: ping *******"
        return 1
    fi
}

test_tcp() {
    echo ""
    echo "🌐 测试TCP (HTTP)..."
    
    # 测试HTTP请求
    if curl -s --max-time 10 http://httpbin.org/ip >/dev/null 2>&1; then
        echo "✅ TCP测试成功: HTTP请求到 httpbin.org"
        return 0
    else
        echo "❌ TCP测试失败: HTTP请求到 httpbin.org"
        return 1
    fi
}

test_udp() {
    echo ""
    echo "📡 测试UDP (DNS)..."
    
    # 测试DNS查询
    if nslookup google.com ******* >/dev/null 2>&1; then
        echo "✅ UDP测试成功: DNS查询 google.com"
        return 0
    else
        echo "❌ UDP测试失败: DNS查询 google.com"
        return 1
    fi
}

# 运行测试
echo ""
echo "🧪 开始数据转发测试..."
echo "================================"

ICMP_RESULT=0
TCP_RESULT=0
UDP_RESULT=0

# 测试ICMP
test_icmp || ICMP_RESULT=1

# 测试TCP
test_tcp || TCP_RESULT=1

# 测试UDP
test_udp || UDP_RESULT=1

# 显示测试结果
echo ""
echo "📊 测试结果总结"
echo "================================"

if [ $ICMP_RESULT -eq 0 ]; then
    echo "✅ ICMP (ping): 通过"
else
    echo "❌ ICMP (ping): 失败"
fi

if [ $TCP_RESULT -eq 0 ]; then
    echo "✅ TCP (HTTP): 通过"
else
    echo "❌ TCP (HTTP): 失败"
fi

if [ $UDP_RESULT -eq 0 ]; then
    echo "✅ UDP (DNS): 通过"
else
    echo "❌ UDP (DNS): 失败"
fi

# 计算总体结果
TOTAL_FAILED=$((ICMP_RESULT + TCP_RESULT + UDP_RESULT))

echo ""
if [ $TOTAL_FAILED -eq 0 ]; then
    echo "🎉 所有测试通过！数据转发工作正常"
    echo ""
    echo "📋 测试详情:"
    echo "- ICMP ping到*******成功"
    echo "- TCP HTTP请求到httpbin.org成功"
    echo "- UDP DNS查询到*******成功"
    echo ""
    echo "✅ 数据转发问题已修复！"
else
    echo "⚠️  有 $TOTAL_FAILED 个测试失败"
    echo ""
    echo "🔍 故障排除建议:"
    echo "1. 检查服务端日志: cat logs/server.log"
    echo "2. 检查客户端日志: cat logs/client.log"
    echo "3. 检查网络连接: ping *************"
    echo "4. 检查防火墙设置"
    echo "5. 检查TUN接口配置: ip addr show cyber-tun0"
    echo ""
    echo "📚 更多信息请查看: docs/DATA_FORWARDING_FINAL_FIX.md"
fi

echo ""
echo "📝 日志文件位置:"
echo "- 服务端日志: logs/server.log"
echo "- 客户端日志: logs/client.log"

exit $TOTAL_FAILED
