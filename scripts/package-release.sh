#!/bin/bash

# Package Release Script for Cyber Bastion
# This script packages binaries for different platforms into release archives

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Configuration
VERSION=${1:-"dev"}
RELEASE_DIR="releases"
PROJECT_NAME="cyber-bastion"

echo -e "${BLUE}=== Cyber Bastion Release Packaging ===${NC}"
echo -e "${BLUE}Version: ${VERSION}${NC}"
echo

# Check if bin directory exists and has binaries
if [ ! -d "bin" ] || [ -z "$(ls -A bin 2>/dev/null)" ]; then
    echo -e "${RED}Error: No binaries found. Run 'make build-all' first.${NC}"
    exit 1
fi

# Create release directory
mkdir -p "$RELEASE_DIR"
rm -rf "$RELEASE_DIR"/*

echo -e "${GREEN}Creating release packages...${NC}"

# Function to create a package for a platform
create_package() {
    local platform=$1
    local arch=$2
    local os_name=$3
    local extension=$4
    
    local package_name="${PROJECT_NAME}-${VERSION}-${platform}-${arch}"
    local package_dir="${RELEASE_DIR}/${package_name}"
    
    echo -e "${BLUE}Creating package: ${package_name}${NC}"
    
    # Create package directory
    mkdir -p "$package_dir"
    
    # Copy binaries
    if [ -f "bin/server-${platform}-${arch}${extension}" ]; then
        cp "bin/server-${platform}-${arch}${extension}" "$package_dir/"
    fi
    
    if [ -f "bin/client-${platform}-${arch}${extension}" ]; then
        cp "bin/client-${platform}-${arch}${extension}" "$package_dir/"
    fi
    
    # Copy configuration files
    if [ -d "configs" ]; then
        cp -r configs "$package_dir/"
    fi
    
    # Copy documentation
    [ -f "README.md" ] && cp README.md "$package_dir/"
    [ -f "LICENSE" ] && cp LICENSE "$package_dir/"
    [ -d "docs" ] && cp -r docs "$package_dir/"
    
    # Create platform-specific README
    cat > "$package_dir/README-${os_name}.md" << EOF
# Cyber Bastion - ${os_name} ${arch}

This package contains the Cyber Bastion binaries for ${os_name} ${arch}.

## Contents

- \`server-${platform}-${arch}${extension}\` - Cyber Bastion Server
- \`client-${platform}-${arch}${extension}\` - Cyber Bastion Client
- \`configs/\` - Configuration files
- \`docs/\` - Documentation

## Quick Start

1. Extract this archive to your desired location
2. Copy and modify configuration files as needed:
   \`\`\`bash
   cp configs/server.yaml.example configs/server.yaml
   cp configs/client.yaml.example configs/client.yaml
   \`\`\`

3. Run the server:
   \`\`\`bash
   ./server-${platform}-${arch}${extension} --config configs/server.yaml
   \`\`\`

4. Run the client:
   \`\`\`bash
   ./client-${platform}-${arch}${extension} --config configs/client.yaml
   \`\`\`

## Installation

You can install the binaries to your system PATH:

EOF

    # Add platform-specific installation instructions
    case $platform in
        "linux"|"darwin")
            cat >> "$package_dir/README-${os_name}.md" << EOF
\`\`\`bash
# Install to /usr/local/bin (requires sudo)
sudo cp server-${platform}-${arch}${extension} /usr/local/bin/cyber-bastion-server
sudo cp client-${platform}-${arch}${extension} /usr/local/bin/cyber-bastion-client
sudo chmod +x /usr/local/bin/cyber-bastion-*

# Or install to user directory
mkdir -p ~/.local/bin
cp server-${platform}-${arch}${extension} ~/.local/bin/cyber-bastion-server
cp client-${platform}-${arch}${extension} ~/.local/bin/cyber-bastion-client
chmod +x ~/.local/bin/cyber-bastion-*
\`\`\`
EOF
            ;;
        "windows")
            cat >> "$package_dir/README-${os_name}.md" << EOF
1. Copy the .exe files to a directory in your PATH
2. Or create a new directory and add it to your PATH
3. Run from Command Prompt or PowerShell

\`\`\`cmd
# Run server
server-${platform}-${arch}${extension} --config configs\\server.yaml

# Run client  
client-${platform}-${arch}${extension} --config configs\\client.yaml
\`\`\`
EOF
            ;;
    esac
    
    # Create archive
    cd "$RELEASE_DIR"
    if command -v tar >/dev/null 2>&1; then
        tar -czf "${package_name}.tar.gz" "$package_name"
        echo -e "${GREEN}Created: ${package_name}.tar.gz${NC}"
    fi
    
    if command -v zip >/dev/null 2>&1; then
        zip -r "${package_name}.zip" "$package_name" >/dev/null
        echo -e "${GREEN}Created: ${package_name}.zip${NC}"
    fi
    
    cd ..
}

# Create packages for each platform
create_package "linux" "amd64" "Linux" ""
create_package "linux" "arm64" "Linux" ""
create_package "darwin" "amd64" "macOS" ""
create_package "darwin" "arm64" "macOS" ""
create_package "windows" "amd64" "Windows" ".exe"

echo
echo -e "${GREEN}Release packaging complete!${NC}"
echo -e "${BLUE}Release directory: ${RELEASE_DIR}${NC}"
echo

# Show created packages
echo -e "${GREEN}Created packages:${NC}"
ls -la "$RELEASE_DIR"/*.tar.gz "$RELEASE_DIR"/*.zip 2>/dev/null || true

echo
echo -e "${BLUE}Usage:${NC}"
echo "  Extract and run the appropriate package for your platform"
echo "  Each package includes binaries, configs, and documentation"
echo
echo -e "${BLUE}Example:${NC}"
echo "  tar -xzf releases/${PROJECT_NAME}-${VERSION}-linux-amd64.tar.gz"
echo "  cd ${PROJECT_NAME}-${VERSION}-linux-amd64"
echo "  ./server-linux-amd64 --config configs/server.yaml"
