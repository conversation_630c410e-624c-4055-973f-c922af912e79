#!/bin/bash

# HTTP伪装功能测试脚本
# 测试服务器对标准HTTP/HTTPS请求的伪装响应

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    print_info "检查依赖..."
    
    if ! command -v curl &> /dev/null; then
        print_error "curl 未安装"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        print_warning "jq 未安装，将跳过JSON解析验证"
        JQ_AVAILABLE=false
    else
        JQ_AVAILABLE=true
    fi
    
    print_success "依赖检查完成"
}

# 构建服务器
build_server() {
    print_info "构建服务器..."
    if ! go build -o bin/server ./cmd/server; then
        print_error "服务器构建失败"
        exit 1
    fi
    print_success "服务器构建完成"
}

# 启动服务器
start_server() {
    print_info "启动服务器..."
    
    # 检查端口是否被占用
    if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_warning "端口8080已被占用，尝试停止现有进程..."
        pkill -f "bin/server" || true
        sleep 2
    fi
    
    # 启动服务器
    ./bin/server --config configs/server.yaml --log-level info > server_test.log 2>&1 &
    SERVER_PID=$!
    
    # 等待服务器启动
    sleep 3
    
    # 检查服务器是否正在运行
    if ! kill -0 $SERVER_PID 2>/dev/null; then
        print_error "服务器启动失败"
        cat server_test.log
        exit 1
    fi
    
    print_success "服务器已启动 (PID: $SERVER_PID)"
}

# 停止服务器
stop_server() {
    if [ ! -z "$SERVER_PID" ] && kill -0 $SERVER_PID 2>/dev/null; then
        print_info "停止服务器..."
        kill $SERVER_PID
        wait $SERVER_PID 2>/dev/null || true
        print_success "服务器已停止"
    fi
}

# 测试HTTP请求
test_http_request() {
    print_info "测试HTTP请求..."
    
    # 发送HTTP请求
    HTTP_RESPONSE=$(curl -s -w "HTTPCODE:%{http_code}" http://localhost:8080 2>/dev/null || echo "FAILED")
    
    if [[ $HTTP_RESPONSE == *"FAILED"* ]]; then
        print_error "HTTP请求失败"
        return 1
    fi
    
    # 提取HTTP状态码
    HTTP_CODE=$(echo "$HTTP_RESPONSE" | grep -o "HTTPCODE:[0-9]*" | cut -d: -f2)
    HTTP_BODY=$(echo "$HTTP_RESPONSE" | sed 's/HTTPCODE:[0-9]*$//')
    
    print_info "HTTP状态码: $HTTP_CODE"
    
    if [ "$HTTP_CODE" = "200" ]; then
        print_success "HTTP请求返回200状态码"
    else
        print_error "HTTP请求返回非200状态码: $HTTP_CODE"
        return 1
    fi
    
    # 验证响应内容
    if [[ $HTTP_BODY == *"application/json"* ]] || [[ $HTTP_BODY == *'"status": "ok"'* ]]; then
        print_success "HTTP响应包含预期的JSON内容"
    else
        print_warning "HTTP响应内容可能不正确"
        echo "响应内容: $HTTP_BODY"
    fi
    
    # 如果有jq，验证JSON格式
    if [ "$JQ_AVAILABLE" = true ]; then
        if echo "$HTTP_BODY" | jq . >/dev/null 2>&1; then
            print_success "HTTP响应是有效的JSON格式"
            
            # 验证JSON字段
            STATUS=$(echo "$HTTP_BODY" | jq -r '.status' 2>/dev/null || echo "")
            if [ "$STATUS" = "ok" ]; then
                print_success "JSON状态字段正确"
            else
                print_warning "JSON状态字段不正确: $STATUS"
            fi
        else
            print_warning "HTTP响应不是有效的JSON格式"
        fi
    fi
    
    return 0
}

# 测试HTTPS请求
test_https_request() {
    print_info "测试HTTPS请求..."
    
    # 发送HTTPS请求（忽略证书验证）
    HTTPS_RESPONSE=$(curl -s -k -w "HTTPCODE:%{http_code}" https://localhost:8080 2>/dev/null || echo "FAILED")
    
    if [[ $HTTPS_RESPONSE == *"FAILED"* ]]; then
        print_error "HTTPS请求失败"
        return 1
    fi
    
    # 提取HTTP状态码
    HTTPS_CODE=$(echo "$HTTPS_RESPONSE" | grep -o "HTTPCODE:[0-9]*" | cut -d: -f2)
    HTTPS_BODY=$(echo "$HTTPS_RESPONSE" | sed 's/HTTPCODE:[0-9]*$//')
    
    print_info "HTTPS状态码: $HTTPS_CODE"
    
    if [ "$HTTPS_CODE" = "200" ]; then
        print_success "HTTPS请求返回200状态码"
    else
        print_error "HTTPS请求返回非200状态码: $HTTPS_CODE"
        return 1
    fi
    
    # 验证响应内容
    if [[ $HTTPS_BODY == *"application/json"* ]] || [[ $HTTPS_BODY == *'"status": "ok"'* ]]; then
        print_success "HTTPS响应包含预期的JSON内容"
    else
        print_warning "HTTPS响应内容可能不正确"
        echo "响应内容: $HTTPS_BODY"
    fi
    
    return 0
}

# 测试不同的HTTP方法
test_http_methods() {
    print_info "测试不同的HTTP方法..."
    
    local methods=("GET" "POST" "HEAD" "PUT" "DELETE" "OPTIONS")
    local success_count=0
    
    for method in "${methods[@]}"; do
        print_info "测试 $method 方法..."
        
        if [ "$method" = "HEAD" ]; then
            # HEAD请求只检查状态码
            HTTP_CODE=$(curl -s -I -X "$method" http://localhost:8080 | head -n1 | grep -o "[0-9][0-9][0-9]" || echo "FAILED")
        else
            HTTP_CODE=$(curl -s -w "%{http_code}" -X "$method" http://localhost:8080 -o /dev/null 2>/dev/null || echo "FAILED")
        fi
        
        if [ "$HTTP_CODE" = "200" ]; then
            print_success "$method 方法返回200状态码"
            ((success_count++))
        else
            print_warning "$method 方法返回状态码: $HTTP_CODE"
        fi
    done
    
    print_info "HTTP方法测试完成: $success_count/${#methods[@]} 成功"
    return 0
}

# 主函数
main() {
    print_info "开始HTTP伪装功能测试..."
    
    # 设置清理函数
    trap stop_server EXIT
    
    # 执行测试步骤
    check_dependencies
    build_server
    start_server
    
    # 等待服务器完全启动
    sleep 2
    
    # 执行测试
    local test_results=0
    
    if test_http_request; then
        print_success "HTTP请求测试通过"
    else
        print_error "HTTP请求测试失败"
        ((test_results++))
    fi
    
    if test_https_request; then
        print_success "HTTPS请求测试通过"
    else
        print_error "HTTPS请求测试失败"
        ((test_results++))
    fi
    
    if test_http_methods; then
        print_success "HTTP方法测试通过"
    else
        print_error "HTTP方法测试失败"
        ((test_results++))
    fi
    
    # 显示服务器日志摘要
    print_info "服务器日志摘要:"
    if [ -f server_test.log ]; then
        grep -E "(HTTP|HTTPS).*request.*detected|Security Event" server_test.log | tail -10 || true
    fi
    
    # 总结
    if [ $test_results -eq 0 ]; then
        print_success "所有HTTP伪装测试通过！"
        print_info "服务器成功伪装为普通Web服务器，对标准HTTP/HTTPS请求返回200状态码和JSON数据"
    else
        print_error "有 $test_results 个测试失败"
        exit 1
    fi
}

# 运行主函数
main "$@"
