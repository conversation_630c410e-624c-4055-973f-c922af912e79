#!/bin/bash

# 证书验证脚本
# 验证TLS证书是否包含正确的IP地址和域名

set -e

echo "🔐 Cyber Bastion 证书验证脚本"
echo "服务器IP: *************"
echo "================================"

# 检查是否在项目根目录
if [ ! -f "go.mod" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 检查证书文件是否存在
echo "📋 检查证书文件..."

cert_files=("certs/ca.crt" "certs/server.crt" "certs/server.key" "certs/client.crt" "certs/client.key")
all_certs_exist=true

for cert_file in "${cert_files[@]}"; do
    if [ -f "$cert_file" ]; then
        echo "✅ $cert_file 存在"
    else
        echo "❌ $cert_file 不存在"
        all_certs_exist=false
    fi
done

if [ "$all_certs_exist" = false ]; then
    echo ""
    echo "❌ 部分证书文件缺失，请运行 ./scripts/setup-single-server.sh 生成证书"
    exit 1
fi

# 验证服务器证书
echo ""
echo "🔍 验证服务器证书..."

# 检查证书有效期
if openssl x509 -in certs/server.crt -checkend 86400 > /dev/null; then
    echo "✅ 服务器证书在有效期内"
else
    echo "❌ 服务器证书已过期或即将过期"
fi

# 检查证书主题
CERT_SUBJECT=$(openssl x509 -in certs/server.crt -subject -noout | sed 's/subject=//')
echo "📋 证书主题: $CERT_SUBJECT"

# 检查Subject Alternative Name
echo ""
echo "🌐 检查Subject Alternative Name..."
SAN_INFO=$(openssl x509 -in certs/server.crt -text -noout | grep -A 1 "Subject Alternative Name" | tail -1)
echo "📋 SAN信息: $SAN_INFO"

# 检查是否包含必要的IP地址和域名
if echo "$SAN_INFO" | grep -q "*************"; then
    echo "✅ 证书包含服务器IP: *************"
else
    echo "❌ 证书不包含服务器IP: *************"
    echo "   这会导致TLS连接失败"
fi

if echo "$SAN_INFO" | grep -q "127.0.0.1"; then
    echo "✅ 证书包含本地IP: 127.0.0.1"
else
    echo "⚠️  证书不包含本地IP: 127.0.0.1"
fi

if echo "$SAN_INFO" | grep -q "localhost"; then
    echo "✅ 证书包含localhost域名"
else
    echo "⚠️  证书不包含localhost域名"
fi

# 验证证书链
echo ""
echo "🔗 验证证书链..."

if openssl verify -CAfile certs/ca.crt certs/server.crt > /dev/null 2>&1; then
    echo "✅ 服务器证书链验证成功"
else
    echo "❌ 服务器证书链验证失败"
fi

if openssl verify -CAfile certs/ca.crt certs/client.crt > /dev/null 2>&1; then
    echo "✅ 客户端证书链验证成功"
else
    echo "❌ 客户端证书链验证失败"
fi

# 测试TLS连接（如果服务器正在运行）
echo ""
echo "🔌 测试TLS连接..."

if command -v openssl > /dev/null; then
    echo "测试连接到 *************:8080..."
    
    # 使用timeout避免长时间等待
    if timeout 5 openssl s_client -connect *************:8080 -CAfile certs/ca.crt -verify_return_error < /dev/null > /dev/null 2>&1; then
        echo "✅ TLS连接测试成功"
    else
        echo "⚠️  TLS连接测试失败（服务器可能未启动）"
    fi
    
    echo "测试连接到 localhost:8080..."
    if timeout 5 openssl s_client -connect localhost:8080 -CAfile certs/ca.crt -verify_return_error < /dev/null > /dev/null 2>&1; then
        echo "✅ 本地TLS连接测试成功"
    else
        echo "⚠️  本地TLS连接测试失败（服务器可能未启动）"
    fi
else
    echo "⚠️  openssl命令不可用，跳过连接测试"
fi

# 检查证书权限
echo ""
echo "🔒 检查证书权限..."

for key_file in "certs/server.key" "certs/client.key" "certs/ca.key"; do
    if [ -f "$key_file" ]; then
        PERMS=$(stat -f "%A" "$key_file" 2>/dev/null || stat -c "%a" "$key_file" 2>/dev/null)
        if [ "$PERMS" = "600" ]; then
            echo "✅ $key_file 权限正确 ($PERMS)"
        else
            echo "⚠️  $key_file 权限不安全 ($PERMS)，建议设置为600"
            echo "   运行: chmod 600 $key_file"
        fi
    fi
done

# 显示证书详细信息
echo ""
echo "📊 证书详细信息"
echo "================================"

echo "🏢 CA证书信息:"
openssl x509 -in certs/ca.crt -subject -issuer -dates -noout | sed 's/^/   /'

echo ""
echo "🖥️  服务器证书信息:"
openssl x509 -in certs/server.crt -subject -issuer -dates -noout | sed 's/^/   /'

echo ""
echo "👤 客户端证书信息:"
openssl x509 -in certs/client.crt -subject -issuer -dates -noout | sed 's/^/   /'

# 总结
echo ""
echo "📋 验证总结"
echo "================================"

# 检查关键问题
CRITICAL_ISSUES=0

if ! echo "$SAN_INFO" | grep -q "*************"; then
    echo "❌ 关键问题: 服务器证书不包含IP *************"
    CRITICAL_ISSUES=$((CRITICAL_ISSUES + 1))
fi

if ! openssl verify -CAfile certs/ca.crt certs/server.crt > /dev/null 2>&1; then
    echo "❌ 关键问题: 服务器证书链验证失败"
    CRITICAL_ISSUES=$((CRITICAL_ISSUES + 1))
fi

if ! openssl x509 -in certs/server.crt -checkend 86400 > /dev/null; then
    echo "❌ 关键问题: 服务器证书已过期"
    CRITICAL_ISSUES=$((CRITICAL_ISSUES + 1))
fi

if [ $CRITICAL_ISSUES -eq 0 ]; then
    echo "🎉 证书验证通过！可以正常使用TLS连接"
    echo ""
    echo "✅ 重要提醒:"
    echo "- 服务器证书包含正确的IP地址: *************"
    echo "- 证书链验证成功"
    echo "- 客户端配置了正确的CA证书文件"
    echo ""
    echo "🚀 启动命令:"
    echo "服务端: ./scripts/start-server.sh"
    echo "客户端: ./scripts/start-client.sh"
else
    echo "⚠️  发现 $CRITICAL_ISSUES 个关键问题，需要修复"
    echo ""
    echo "🔧 修复建议:"
    echo "1. 重新生成证书: ./scripts/setup-single-server.sh"
    echo "2. 检查服务器IP配置是否正确"
    echo "3. 确保证书包含所有必要的IP地址和域名"
    echo "4. 确保客户端代码正确加载CA证书"
fi

echo ""
echo "📚 更多信息请查看: docs/ENCRYPTION_SETUP.md"
