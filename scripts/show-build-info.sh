#!/bin/bash

# Show Build Information Script for Cyber Bastion
# This script displays information about built binaries

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Cyber Bastion Build Information ===${NC}"
echo

# Check if bin directory exists
if [ ! -d "bin" ]; then
    echo -e "${YELLOW}No bin directory found. Run 'make build' or 'make build-all' first.${NC}"
    exit 1
fi

# Check if there are any binaries
if [ -z "$(ls -A bin 2>/dev/null)" ]; then
    echo -e "${YELLOW}No binaries found in bin directory. Run 'make build' or 'make build-all' first.${NC}"
    exit 1
fi

echo -e "${GREEN}Built binaries:${NC}"
echo

# Function to get file size in human readable format
get_size() {
    if command -v numfmt >/dev/null 2>&1; then
        numfmt --to=iec-i --suffix=B --format="%.1f" "$1"
    else
        # Fallback for macOS
        local size=$1
        if [ $size -gt 1073741824 ]; then
            echo "$(echo "scale=1; $size/1073741824" | bc)GB"
        elif [ $size -gt 1048576 ]; then
            echo "$(echo "scale=1; $size/1048576" | bc)MB"
        elif [ $size -gt 1024 ]; then
            echo "$(echo "scale=1; $size/1024" | bc)KB"
        else
            echo "${size}B"
        fi
    fi
}

# Display binary information
printf "%-30s %-10s %-12s %-15s\n" "Binary" "Type" "Size" "Architecture"
printf "%-30s %-10s %-12s %-15s\n" "------------------------------" "----------" "------------" "---------------"

for binary in bin/*; do
    if [ -f "$binary" ]; then
        filename=$(basename "$binary")
        
        # Determine type (server/client)
        if [[ $filename == server-* ]]; then
            type="Server"
        elif [[ $filename == client-* ]]; then
            type="Client"
        else
            type="Unknown"
        fi
        
        # Get file size
        if [ "$(uname)" = "Darwin" ]; then
            size=$(stat -f%z "$binary")
        else
            size=$(stat -c%s "$binary")
        fi
        size_human=$(get_size $size)
        
        # Determine architecture from filename
        if [[ $filename == *-linux-amd64* ]]; then
            arch="Linux x86_64"
        elif [[ $filename == *-linux-arm64* ]]; then
            arch="Linux ARM64"
        elif [[ $filename == *-darwin-amd64* ]]; then
            arch="macOS x86_64"
        elif [[ $filename == *-darwin-arm64* ]]; then
            arch="macOS ARM64"
        elif [[ $filename == *-windows-amd64* ]]; then
            arch="Windows x86_64"
        else
            arch="Unknown"
        fi
        
        printf "%-30s %-10s %-12s %-15s\n" "$filename" "$type" "$size_human" "$arch"
    fi
done

echo
echo -e "${GREEN}Total binaries: $(ls -1 bin | wc -l | tr -d ' ')${NC}"

# Show total size
if [ "$(uname)" = "Darwin" ]; then
    total_size=$(find bin -type f -exec stat -f%z {} \; | awk '{sum+=$1} END {print sum}')
else
    total_size=$(find bin -type f -exec stat -c%s {} \; | awk '{sum+=$1} END {print sum}')
fi

if [ -n "$total_size" ] && [ "$total_size" -gt 0 ]; then
    total_size_human=$(get_size $total_size)
    echo -e "${GREEN}Total size: $total_size_human${NC}"
fi

echo
echo -e "${BLUE}Platform Support:${NC}"
echo "✅ Linux amd64    - Standard x86_64 Linux servers"
echo "✅ Linux arm64    - ARM64 Linux (AWS Graviton, Raspberry Pi, etc.)"
echo "✅ macOS amd64    - Intel-based Macs"
echo "✅ macOS arm64    - Apple Silicon Macs (M1, M2, M3, etc.)"
echo "✅ Windows amd64  - 64-bit Windows systems"

echo
echo -e "${BLUE}Usage Examples:${NC}"
echo "# Run on current platform:"
echo "  ./bin/server --config configs/server.yaml"
echo "  ./bin/client --config configs/client.yaml"
echo
echo "# Deploy to Linux ARM64 server:"
echo "  scp bin/server-linux-arm64 user@server:/usr/local/bin/cyber-bastion-server"
echo
echo "# Deploy to Apple Silicon Mac:"
echo "  cp bin/server-darwin-arm64 /usr/local/bin/cyber-bastion-server"
