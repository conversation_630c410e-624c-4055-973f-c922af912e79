#!/bin/bash

# TCP转发功能测试脚本
# 用于验证混合转发策略是否正常工作

set -e

echo "🚀 TCP转发功能测试脚本"
echo "=========================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖工具..."
    
    if ! command -v tcpdump &> /dev/null; then
        log_warning "tcpdump 未安装，无法进行包监控"
    fi
    
    if ! command -v curl &> /dev/null; then
        log_error "curl 未安装，无法进行HTTP测试"
        exit 1
    fi
    
    if ! command -v nc &> /dev/null; then
        log_warning "netcat 未安装，无法进行TCP连接测试"
    fi
    
    log_success "依赖检查完成"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    # 检查server是否运行
    if pgrep -f "./bin/server" > /dev/null; then
        log_success "Server 正在运行"
    else
        log_error "Server 未运行，请先启动 server"
        echo "启动命令: sudo ./bin/server --config configs/server.yaml --log-level debug"
        exit 1
    fi
    
    # 检查client是否运行
    if pgrep -f "./bin/client" > /dev/null; then
        log_success "Client 正在运行"
    else
        log_error "Client 未运行，请先启动 client"
        echo "启动命令: sudo ./bin/client --config configs/client.yaml --log-level debug"
        exit 1
    fi
    
    # 检查TUN接口
    if ip link show cyber-tun &> /dev/null; then
        log_success "TUN接口 cyber-tun 存在"
        ip addr show cyber-tun | grep -E "inet|inet6" || log_warning "TUN接口未配置IP地址"
    else
        log_error "TUN接口 cyber-tun 不存在"
        exit 1
    fi
}

# 测试ICMP（作为对比基准）
test_icmp() {
    log_info "测试ICMP转发（基准测试）..."
    
    # 通过TUN接口ping
    if ping -c 3 -W 5 ******* > /dev/null 2>&1; then
        log_success "ICMP转发正常工作"
        return 0
    else
        log_error "ICMP转发失败"
        return 1
    fi
}

# 测试TCP连接
test_tcp_connection() {
    local host=$1
    local port=$2
    local description=$3
    
    log_info "测试TCP连接: $description ($host:$port)"
    
    # 使用curl测试HTTP/HTTPS连接
    if [[ $port == "80" ]]; then
        if curl -s --max-time 10 --connect-timeout 5 "http://$host" > /dev/null; then
            log_success "HTTP连接成功: $host:$port"
            return 0
        else
            log_error "HTTP连接失败: $host:$port"
            return 1
        fi
    elif [[ $port == "443" ]]; then
        if curl -s --max-time 10 --connect-timeout 5 "https://$host" > /dev/null; then
            log_success "HTTPS连接成功: $host:$port"
            return 0
        else
            log_error "HTTPS连接失败: $host:$port"
            return 1
        fi
    else
        # 使用nc测试其他端口
        if command -v nc &> /dev/null; then
            if nc -z -w 5 "$host" "$port" 2>/dev/null; then
                log_success "TCP连接成功: $host:$port"
                return 0
            else
                log_error "TCP连接失败: $host:$port"
                return 1
            fi
        else
            log_warning "无法测试端口 $port (nc未安装)"
            return 2
        fi
    fi
}

# 监控网络流量
monitor_traffic() {
    local duration=${1:-10}
    
    log_info "监控TUN接口流量 ($duration 秒)..."
    
    if command -v tcpdump &> /dev/null; then
        log_info "启动tcpdump监控..."
        timeout $duration tcpdump -i cyber-tun -n -c 20 2>/dev/null | head -10 || true
        log_info "流量监控完成"
    else
        log_warning "tcpdump未安装，跳过流量监控"
    fi
}

# 检查日志
check_logs() {
    log_info "检查最近的转发日志..."
    
    # 检查server日志中的TCP转发记录
    if journalctl -u cyber-bastion-server --since "1 minute ago" --no-pager -q 2>/dev/null | grep -i "tcp.*forwarding" | tail -5; then
        log_success "发现TCP转发日志"
    else
        log_warning "未发现TCP转发日志，可能需要检查日志级别"
    fi
}

# 主测试流程
main() {
    echo
    log_info "开始TCP转发功能测试..."
    echo
    
    # 1. 检查依赖和服务
    check_dependencies
    echo
    check_services
    echo
    
    # 2. 基准测试 - ICMP
    log_info "=== 基准测试 ==="
    test_icmp
    echo
    
    # 3. TCP连接测试
    log_info "=== TCP连接测试 ==="
    
    # 测试常见的HTTP/HTTPS服务
    test_tcp_connection "www.google.com" "80" "Google HTTP"
    test_tcp_connection "www.google.com" "443" "Google HTTPS"
    test_tcp_connection "www.github.com" "443" "GitHub HTTPS"
    test_tcp_connection "*******" "53" "Google DNS"
    
    echo
    
    # 4. 流量监控
    log_info "=== 流量监控 ==="
    monitor_traffic 5
    echo
    
    # 5. 日志检查
    log_info "=== 日志检查 ==="
    check_logs
    echo
    
    log_success "TCP转发功能测试完成！"
    echo
    echo "📋 测试建议："
    echo "1. 如果TCP连接失败，检查server日志中的错误信息"
    echo "2. 使用 'sudo ./bin/server --log-level debug' 启用详细日志"
    echo "3. 检查防火墙和路由配置"
    echo "4. 确认TUN接口配置正确"
    echo
    echo "🔍 调试命令："
    echo "- 查看server日志: journalctl -u cyber-bastion-server -f"
    echo "- 监控TUN流量: sudo tcpdump -i cyber-tun -n"
    echo "- 检查路由: ip route show"
    echo "- 检查TUN接口: ip addr show cyber-tun"
}

# 运行主函数
main "$@"
