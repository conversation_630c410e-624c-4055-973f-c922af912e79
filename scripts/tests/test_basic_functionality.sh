#!/bin/bash

# 基本功能测试脚本
# 测试编译、启动和基本功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 清理函数
cleanup() {
    log_info "Cleaning up..."
    sudo pkill -f "./bin/server" 2>/dev/null || true
    sudo pkill -f "./bin/client" 2>/dev/null || true
    sleep 2
}

trap cleanup EXIT

# 测试编译
test_compilation() {
    log_info "Testing compilation..."
    
    # 清理旧的二进制文件
    rm -f bin/server bin/client
    
    # 编译server
    log_info "Compiling server..."
    if go build -o bin/server ./cmd/server; then
        log_success "Server compiled successfully"
    else
        log_error "Server compilation failed"
        return 1
    fi
    
    # 编译client
    log_info "Compiling client..."
    if go build -o bin/client ./cmd/client; then
        log_success "Client compiled successfully"
    else
        log_error "Client compilation failed"
        return 1
    fi
    
    # 检查二进制文件
    if [[ -f "bin/server" && -f "bin/client" ]]; then
        log_success "Both binaries created successfully"
        ls -la bin/
    else
        log_error "Binary files not found"
        return 1
    fi
}

# 测试server启动
test_server_startup() {
    log_info "Testing server startup..."
    
    # 检查配置文件
    if [[ ! -f "configs/server.yaml" ]]; then
        log_error "Server config file not found"
        return 1
    fi
    
    # 启动server
    log_info "Starting server..."
    sudo ./bin/server --config configs/server.yaml --log-level debug > server_startup.log 2>&1 &
    SERVER_PID=$!
    
    # 等待启动
    sleep 5
    
    # 检查进程是否运行
    if kill -0 $SERVER_PID 2>/dev/null; then
        log_success "Server started successfully (PID: $SERVER_PID)"
        
        # 检查日志中的关键信息
        if grep -q "Server started" server_startup.log; then
            log_success "Server startup message found in logs"
        fi
        
        if grep -q "Optimized forwarding handler started successfully" server_startup.log; then
            log_success "Optimized forwarding handler initialized"
        else
            log_warning "Optimized forwarding handler not found in logs"
        fi
        
        # 显示启动日志摘要
        log_info "Server startup log summary:"
        grep -E "(INFO|ERROR|WARN)" server_startup.log | tail -10 | sed 's/^/  /'
        
    else
        log_error "Server failed to start"
        log_info "Server startup log:"
        cat server_startup.log | sed 's/^/  /'
        return 1
    fi
    
    # 停止server
    sudo kill $SERVER_PID 2>/dev/null || true
    sleep 2
}

# 测试client启动
test_client_startup() {
    log_info "Testing client startup..."
    
    # 检查配置文件
    if [[ ! -f "configs/client.yaml" ]]; then
        log_error "Client config file not found"
        return 1
    fi
    
    # 先启动server
    log_info "Starting server for client test..."
    sudo ./bin/server --config configs/server.yaml --log-level info > server_for_client.log 2>&1 &
    SERVER_PID=$!
    sleep 3
    
    # 启动client
    log_info "Starting client..."
    sudo ./bin/client --config configs/client.yaml --log-level debug > client_startup.log 2>&1 &
    CLIENT_PID=$!
    
    # 等待启动
    sleep 5
    
    # 检查client进程
    if kill -0 $CLIENT_PID 2>/dev/null; then
        log_success "Client started successfully (PID: $CLIENT_PID)"
        
        # 检查日志
        if grep -q "Client started" client_startup.log; then
            log_success "Client startup message found in logs"
        fi
        
        if grep -q "Connected to server" client_startup.log; then
            log_success "Client connected to server"
        else
            log_warning "Client connection not confirmed in logs"
        fi
        
        # 显示启动日志摘要
        log_info "Client startup log summary:"
        grep -E "(INFO|ERROR|WARN)" client_startup.log | tail -10 | sed 's/^/  /'
        
    else
        log_error "Client failed to start"
        log_info "Client startup log:"
        cat client_startup.log | sed 's/^/  /'
        return 1
    fi
    
    # 停止进程
    sudo kill $CLIENT_PID 2>/dev/null || true
    sudo kill $SERVER_PID 2>/dev/null || true
    sleep 2
}

# 测试HTTP监控端点
test_monitoring_endpoints() {
    log_info "Testing monitoring endpoints..."
    
    # 启动server
    log_info "Starting server for monitoring test..."
    sudo ./bin/server --config configs/server.yaml --log-level info > server_monitoring.log 2>&1 &
    SERVER_PID=$!
    sleep 5
    
    # 检查server是否运行
    if ! kill -0 $SERVER_PID 2>/dev/null; then
        log_error "Server failed to start for monitoring test"
        return 1
    fi
    
    # 获取server端口
    local server_port=$(grep -E "^\s*port:" configs/server.yaml | awk '{print $2}' 2>/dev/null || echo "8443")
    
    # 测试基本HTTP响应
    log_info "Testing basic HTTP response..."
    if curl -s --max-time 5 "http://localhost:$server_port/" > /dev/null; then
        log_success "Basic HTTP response working"
    else
        log_warning "Basic HTTP response not working"
    fi
    
    # 测试监控端点
    log_info "Testing metrics endpoint..."
    if curl -s --max-time 5 "http://localhost:$server_port/metrics/forwarding" > metrics_test.json; then
        log_success "Metrics endpoint accessible"
        log_info "Metrics response sample:"
        head -5 metrics_test.json | sed 's/^/  /'
    else
        log_warning "Metrics endpoint not accessible"
    fi
    
    log_info "Testing status endpoint..."
    if curl -s --max-time 5 "http://localhost:$server_port/status/forwarding" > status_test.json; then
        log_success "Status endpoint accessible"
        log_info "Status response sample:"
        head -5 status_test.json | sed 's/^/  /'
    else
        log_warning "Status endpoint not accessible"
    fi
    
    # 停止server
    sudo kill $SERVER_PID 2>/dev/null || true
    sleep 2
}

# 测试配置文件
test_configurations() {
    log_info "Testing configuration files..."
    
    # 检查server配置
    if [[ -f "configs/server.yaml" ]]; then
        log_success "Server config file exists"
        log_info "Server config summary:"
        grep -E "^[a-z]" configs/server.yaml | head -10 | sed 's/^/  /'
    else
        log_error "Server config file missing"
        return 1
    fi
    
    # 检查client配置
    if [[ -f "configs/client.yaml" ]]; then
        log_success "Client config file exists"
        log_info "Client config summary:"
        grep -E "^[a-z]" configs/client.yaml | head -10 | sed 's/^/  /'
    else
        log_error "Client config file missing"
        return 1
    fi
}

# 检查系统要求
check_system_requirements() {
    log_info "Checking system requirements..."
    
    # 检查Go版本
    if command -v go &> /dev/null; then
        local go_version=$(go version)
        log_success "Go is installed: $go_version"
    else
        log_error "Go is not installed"
        return 1
    fi
    
    # 检查sudo权限
    if sudo -n true 2>/dev/null; then
        log_success "Sudo access available"
    else
        log_warning "Sudo access may be required for some tests"
    fi
    
    # 检查网络工具
    local tools=("curl" "ping" "ip")
    for tool in "${tools[@]}"; do
        if command -v "$tool" &> /dev/null; then
            log_success "$tool is available"
        else
            log_warning "$tool is not available"
        fi
    done
}

# 主测试流程
main() {
    echo
    log_info "🔧 Starting Basic Functionality Test"
    log_info "===================================="
    echo
    
    # 1. 检查系统要求
    check_system_requirements
    echo
    
    # 2. 测试配置文件
    test_configurations
    echo
    
    # 3. 测试编译
    test_compilation
    echo
    
    # 4. 测试server启动
    test_server_startup
    echo
    
    # 5. 测试client启动
    test_client_startup
    echo
    
    # 6. 测试监控端点
    test_monitoring_endpoints
    echo
    
    log_success "🎉 Basic Functionality Test Completed!"
    echo
    
    log_info "Test Summary:"
    echo "  ✓ System requirements checked"
    echo "  ✓ Configuration files validated"
    echo "  ✓ Compilation successful"
    echo "  ✓ Server startup tested"
    echo "  ✓ Client startup tested"
    echo "  ✓ Monitoring endpoints tested"
    echo
    
    log_info "Next Steps:"
    echo "  1. Run with sudo for full functionality testing"
    echo "  2. Check log files for detailed information"
    echo "  3. Test with actual network traffic"
    echo
    
    log_info "Generated Files:"
    echo "  - server_startup.log"
    echo "  - client_startup.log"
    echo "  - metrics_test.json"
    echo "  - status_test.json"
}

# 运行主函数
main "$@"
