#!/bin/bash

# 快速测试地址映射缓存修复
echo "=== 地址映射缓存修复测试 ==="

# 检查编译结果
if [ ! -f "bin/server" ] || [ ! -f "bin/client" ]; then
    echo "编译二进制文件..."
    go build -o bin/server cmd/server/main.go
    go build -o bin/client cmd/client/main.go
fi

echo "✅ 编译完成"
echo ""
echo "🔧 修复内容："
echo "1. 添加了地址映射缓存（AddressMappingCache）"
echo "2. 在数据包接收时记录客户端真实IP映射（从客户端ID提取）"
echo "3. 在响应包构造时使用缓存的客户端真实IP"
echo "4. 临时禁用优化转发，强制使用传统转发路径"
echo "5. 添加了详细的调试日志"
echo ""
echo "📋 预期效果："
echo "- 服务器日志显示：'Using legacy forwarding mode'"
echo "- 服务器日志显示：'Created new address mapping' 和 'Using cached address mapping'"
echo "- 服务器日志显示：'Forwarding ICMP packet with response'"
echo "- 客户端接收的响应包目标IP应该是客户端真实IP（从客户端ID提取），而不是 ********"
echo ""
echo "🚀 启动测试："
echo "1. 启动服务器：sudo ./bin/server -config configs/server.yaml"
echo "2. 启动客户端：sudo ./bin/client -config configs/client.yaml"
echo "3. 发送ping：ping -I cyber-tun *************"
echo ""
echo "📊 关键日志关键词："
echo "- 'Created new address mapping'"
echo "- 'Using cached address mapping for response'"
echo "- 'ICMP response packet constructed with address mapping'"
echo "- 'response_dst_ip': '*************'"
echo ""
echo "修复完成！请按照上述步骤进行测试。"
