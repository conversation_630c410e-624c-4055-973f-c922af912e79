#!/bin/bash

# 集成系统全面测试脚本
# 测试优化转发处理器集成后的完整系统功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 配置
SERVER_CONFIG="configs/server.yaml"
CLIENT_CONFIG="configs/client.yaml"
TEST_DURATION=60
LOG_LEVEL="debug"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_test() {
    echo -e "${PURPLE}[TEST]${NC} $1"
}

# 清理函数
cleanup() {
    log_info "Cleaning up processes..."
    
    # 停止server和client
    pkill -f "./bin/server" 2>/dev/null || true
    pkill -f "./bin/client" 2>/dev/null || true
    
    # 等待进程完全停止
    sleep 2
    
    # 清理TUN接口
    sudo ip link delete cyber-tun 2>/dev/null || true
    
    log_info "Cleanup completed"
}

# 设置信号处理
trap cleanup EXIT INT TERM

# 检查依赖
check_dependencies() {
    log_info "Checking dependencies..."
    
    # 检查必要的命令
    local deps=("go" "sudo" "ip" "ping" "curl" "nc")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            log_error "$dep is not installed"
            exit 1
        fi
    done
    
    # 检查配置文件
    if [[ ! -f "$SERVER_CONFIG" ]]; then
        log_error "Server config file not found: $SERVER_CONFIG"
        exit 1
    fi
    
    if [[ ! -f "$CLIENT_CONFIG" ]]; then
        log_error "Client config file not found: $CLIENT_CONFIG"
        exit 1
    fi
    
    log_success "Dependencies check passed"
}

# 编译程序
build_programs() {
    log_info "Building programs..."
    
    # 编译server
    if ! go build -o bin/server ./cmd/server; then
        log_error "Failed to build server"
        exit 1
    fi
    
    # 编译client
    if ! go build -o bin/client ./cmd/client; then
        log_error "Failed to build client"
        exit 1
    fi
    
    log_success "Programs built successfully"
}

# 启动server
start_server() {
    log_info "Starting server..."
    
    # 启动server（后台运行）
    sudo ./bin/server --config "$SERVER_CONFIG" --log-level "$LOG_LEVEL" > server.log 2>&1 &
    SERVER_PID=$!
    
    # 等待server启动
    sleep 3
    
    # 检查server是否正在运行
    if ! kill -0 $SERVER_PID 2>/dev/null; then
        log_error "Server failed to start"
        cat server.log
        exit 1
    fi
    
    log_success "Server started (PID: $SERVER_PID)"
}

# 启动client
start_client() {
    log_info "Starting client..."
    
    # 启动client（后台运行）
    sudo ./bin/client --config "$CLIENT_CONFIG" --log-level "$LOG_LEVEL" > client.log 2>&1 &
    CLIENT_PID=$!
    
    # 等待client启动
    sleep 5
    
    # 检查client是否正在运行
    if ! kill -0 $CLIENT_PID 2>/dev/null; then
        log_error "Client failed to start"
        cat client.log
        exit 1
    fi
    
    log_success "Client started (PID: $CLIENT_PID)"
}

# 检查TUN接口
check_tun_interface() {
    log_test "Checking TUN interface..."
    
    # 检查TUN接口是否存在
    if ! ip link show cyber-tun &> /dev/null; then
        log_error "TUN interface 'cyber-tun' not found"
        return 1
    fi
    
    # 检查TUN接口状态
    local tun_status=$(ip link show cyber-tun | grep -o "state [A-Z]*" | cut -d' ' -f2)
    if [[ "$tun_status" != "UP" ]]; then
        log_warning "TUN interface is not UP (status: $tun_status)"
    fi
    
    # 显示TUN接口信息
    log_info "TUN interface information:"
    ip addr show cyber-tun | sed 's/^/  /'
    
    log_success "TUN interface check completed"
}

# 测试连接性
test_connectivity() {
    log_test "Testing basic connectivity..."
    
    # 测试ICMP (ping)
    log_info "Testing ICMP forwarding..."
    if ping -c 3 -W 5 ******* > /dev/null 2>&1; then
        log_success "ICMP forwarding works"
    else
        log_error "ICMP forwarding failed"
        return 1
    fi
    
    # 测试DNS解析
    log_info "Testing DNS resolution..."
    if nslookup google.com ******* > /dev/null 2>&1; then
        log_success "DNS resolution works"
    else
        log_warning "DNS resolution failed (may be normal)"
    fi
    
    log_success "Basic connectivity test completed"
}

# 测试HTTP/HTTPS连接
test_http_connections() {
    log_test "Testing HTTP/HTTPS connections..."
    
    # 测试HTTP连接
    log_info "Testing HTTP connection..."
    if timeout 10 curl -s --max-time 5 http://httpbin.org/ip > /dev/null; then
        log_success "HTTP connection works"
    else
        log_warning "HTTP connection failed"
    fi
    
    # 测试HTTPS连接
    log_info "Testing HTTPS connection..."
    if timeout 10 curl -s --max-time 5 https://httpbin.org/ip > /dev/null; then
        log_success "HTTPS connection works"
    else
        log_warning "HTTPS connection failed"
    fi
    
    log_success "HTTP/HTTPS connection test completed"
}

# 测试转发性能监控
test_monitoring() {
    log_test "Testing forwarding performance monitoring..."
    
    # 获取server监听端口
    local server_port=$(grep -E "^\s*port:" "$SERVER_CONFIG" | awk '{print $2}' || echo "8443")
    local server_host="localhost"
    
    # 测试转发指标端点
    log_info "Testing forwarding metrics endpoint..."
    if timeout 5 curl -s "http://$server_host:$server_port/metrics/forwarding" > metrics.json; then
        log_success "Forwarding metrics endpoint accessible"
        log_info "Metrics sample:"
        head -10 metrics.json | sed 's/^/  /'
    else
        log_warning "Forwarding metrics endpoint not accessible"
    fi
    
    # 测试转发状态端点
    log_info "Testing forwarding status endpoint..."
    if timeout 5 curl -s "http://$server_host:$server_port/status/forwarding" > status.json; then
        log_success "Forwarding status endpoint accessible"
        log_info "Status sample:"
        head -10 status.json | sed 's/^/  /'
    else
        log_warning "Forwarding status endpoint not accessible"
    fi
    
    log_success "Monitoring test completed"
}

# 性能压力测试
test_performance() {
    log_test "Running performance stress test..."
    
    local test_count=100
    local concurrent_tests=10
    
    log_info "Running $test_count ping tests with $concurrent_tests concurrent connections..."
    
    # 并发ping测试
    local success_count=0
    local total_time=0
    
    for ((i=1; i<=test_count; i++)); do
        if ((i % concurrent_tests == 0)); then
            wait # 等待当前批次完成
        fi
        
        {
            local start_time=$(date +%s.%N)
            if ping -c 1 -W 2 ******* > /dev/null 2>&1; then
                local end_time=$(date +%s.%N)
                local duration=$(echo "$end_time - $start_time" | bc -l 2>/dev/null || echo "0")
                echo "SUCCESS:$duration"
            else
                echo "FAILED"
            fi
        } &
    done
    
    wait # 等待所有测试完成
    
    log_success "Performance stress test completed"
}

# 检查日志错误
check_logs() {
    log_test "Checking logs for errors..."
    
    # 检查server日志
    log_info "Checking server logs..."
    local server_errors=$(grep -i "error\|failed\|panic" server.log | wc -l)
    if [[ $server_errors -gt 0 ]]; then
        log_warning "Found $server_errors error entries in server log"
        log_info "Recent server errors:"
        grep -i "error\|failed\|panic" server.log | tail -5 | sed 's/^/  /'
    else
        log_success "No errors found in server log"
    fi
    
    # 检查client日志
    log_info "Checking client logs..."
    local client_errors=$(grep -i "error\|failed\|panic" client.log | wc -l)
    if [[ $client_errors -gt 0 ]]; then
        log_warning "Found $client_errors error entries in client log"
        log_info "Recent client errors:"
        grep -i "error\|failed\|panic" client.log | tail -5 | sed 's/^/  /'
    else
        log_success "No errors found in client log"
    fi
    
    log_success "Log check completed"
}

# 生成测试报告
generate_report() {
    log_info "Generating test report..."
    
    local report_file="test_report_$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$report_file" << EOF
# 集成系统测试报告
生成时间: $(date)
测试持续时间: ${TEST_DURATION}秒

## 系统信息
操作系统: $(uname -s)
内核版本: $(uname -r)
Go版本: $(go version)

## 进程状态
Server PID: $SERVER_PID
Client PID: $CLIENT_PID

## TUN接口信息
$(ip addr show cyber-tun 2>/dev/null || echo "TUN接口不存在")

## 转发指标
$(cat metrics.json 2>/dev/null || echo "无法获取转发指标")

## 日志统计
Server日志行数: $(wc -l < server.log)
Client日志行数: $(wc -l < client.log)
Server错误数: $(grep -i "error\|failed\|panic" server.log | wc -l)
Client错误数: $(grep -i "error\|failed\|panic" client.log | wc -l)

## 测试结果
- TUN接口: $(ip link show cyber-tun &>/dev/null && echo "✓ 正常" || echo "✗ 异常")
- ICMP转发: $(ping -c 1 -W 2 ******* &>/dev/null && echo "✓ 正常" || echo "✗ 异常")
- 监控端点: $(curl -s localhost:8443/metrics/forwarding &>/dev/null && echo "✓ 正常" || echo "✗ 异常")

EOF
    
    log_success "Test report generated: $report_file"
}

# 主测试流程
main() {
    echo
    log_info "🚀 Starting Integrated System Test"
    log_info "=================================="
    echo
    
    # 1. 检查依赖
    check_dependencies
    echo
    
    # 2. 编译程序
    build_programs
    echo
    
    # 3. 启动服务
    start_server
    start_client
    echo
    
    # 4. 检查TUN接口
    check_tun_interface
    echo
    
    # 5. 测试基本连接性
    test_connectivity
    echo
    
    # 6. 测试HTTP/HTTPS连接
    test_http_connections
    echo
    
    # 7. 测试监控功能
    test_monitoring
    echo
    
    # 8. 性能压力测试
    test_performance
    echo
    
    # 9. 运行持续测试
    log_info "Running continuous test for $TEST_DURATION seconds..."
    sleep $TEST_DURATION
    echo
    
    # 10. 检查日志
    check_logs
    echo
    
    # 11. 生成报告
    generate_report
    echo
    
    log_success "🎉 Integrated System Test Completed!"
    log_info "Check the generated report and log files for detailed results."
    echo
    
    # 显示最终状态
    log_info "Final System Status:"
    echo "  Server: $(kill -0 $SERVER_PID 2>/dev/null && echo "Running" || echo "Stopped")"
    echo "  Client: $(kill -0 $CLIENT_PID 2>/dev/null && echo "Running" || echo "Stopped")"
    echo "  TUN Interface: $(ip link show cyber-tun &>/dev/null && echo "Up" || echo "Down")"
    echo
}

# 运行主函数
main "$@"
