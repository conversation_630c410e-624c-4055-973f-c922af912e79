#!/bin/bash

# TCP和UDP地址映射修复测试脚本
echo "=== TCP和UDP地址映射修复测试 ==="
echo "测试目标：验证TCP和UDP响应包的IP地址映射是否正确"
echo ""

# 设置日志级别为debug以获取详细信息
export LOG_LEVEL=debug

# 检查是否有现有的进程
echo "检查现有进程..."
pkill -f "bin/server" 2>/dev/null || true
pkill -f "bin/client" 2>/dev/null || true
sleep 2

# 启动服务器（后台运行）
echo "启动服务器..."
sudo ./bin/server -config configs/server.yaml > server_tcp_udp_test.log 2>&1 &
SERVER_PID=$!
echo "服务器PID: $SERVER_PID"

# 等待服务器启动
sleep 3

# 启动客户端（后台运行）
echo "启动客户端..."
sudo ./bin/client -config configs/client.yaml > client_tcp_udp_test.log 2>&1 &
CLIENT_PID=$!
echo "客户端PID: $CLIENT_PID"

# 等待客户端连接
sleep 5

echo ""
echo "=== 开始多协议测试 ==="
echo "测试目标："
echo "  1. ICMP: ping *******"
echo "  2. UDP: nslookup google.com *******"
echo "  3. TCP: curl -I http://httpbin.org/ip"
echo ""
echo "预期行为："
echo "  - 所有响应包的目标IP应该是客户端真实IP（从客户端ID提取）"
echo "  - 而不是服务器IP (********) 或 TUN接口IP (********)"
echo ""

# 通过TUN接口发送不同协议的测试
if ip link show cyber-tun >/dev/null 2>&1; then
    echo "通过TUN接口发送测试..."
    
    # ICMP测试
    echo "1. ICMP测试 (ping)..."
    ping -I cyber-tun -c 2 ******* &
    PING_PID=$!
    
    # 等待ping完成
    sleep 5
    kill $PING_PID 2>/dev/null || true
    
    # UDP测试 (DNS查询)
    echo "2. UDP测试 (DNS查询)..."
    # 使用dig通过TUN接口进行DNS查询
    timeout 10 dig @******* google.com &
    DIG_PID=$!
    sleep 8
    kill $DIG_PID 2>/dev/null || true
    
    # TCP测试 (HTTP请求)
    echo "3. TCP测试 (HTTP请求)..."
    # 使用curl通过TUN接口进行HTTP请求
    timeout 10 curl -I --interface cyber-tun http://httpbin.org/ip &
    CURL_PID=$!
    sleep 8
    kill $CURL_PID 2>/dev/null || true
    
else
    echo "TUN接口不存在，跳过网络测试"
fi

echo ""
echo "=== 分析日志 ==="

echo ""
echo "--- 服务器日志（地址映射相关）---"
grep -i "address mapping\|Created new\|Updated existing\|Using cached" server_tcp_udp_test.log | tail -15

echo ""
echo "--- 服务器日志（ICMP相关）---"
grep -i "icmp.*response.*constructed.*address mapping" server_tcp_udp_test.log | tail -5

echo ""
echo "--- 服务器日志（TCP相关）---"
grep -i "tcp.*response.*constructed.*address mapping" server_tcp_udp_test.log | tail -5

echo ""
echo "--- 服务器日志（UDP相关）---"
grep -i "udp.*response.*constructed.*address mapping" server_tcp_udp_test.log | tail -5

echo ""
echo "--- 客户端日志（响应包相关）---"
grep -i "received.*response packet" client_tcp_udp_test.log | tail -10

echo ""
echo "=== 清理进程 ==="
kill $SERVER_PID 2>/dev/null || true
kill $CLIENT_PID 2>/dev/null || true

echo ""
echo "=== 测试完成 ==="
echo "请检查上述日志，确认："
echo "1. 服务器日志中显示 'Using legacy forwarding mode'"
echo "2. 服务器日志中显示各协议的 'response packet constructed with address mapping'"
echo "3. 客户端日志中的响应包目标IP是客户端真实IP，而不是服务器IP"
echo ""
echo "日志文件保存在："
echo "  - server_tcp_udp_test.log"
echo "  - client_tcp_udp_test.log"
