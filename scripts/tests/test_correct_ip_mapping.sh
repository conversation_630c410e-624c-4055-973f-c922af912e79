#!/bin/bash

# 正确IP地址映射测试脚本
echo "=== 正确IP地址映射测试 ==="
echo "测试目标：验证响应包目标IP是数据包的原始源IP"
echo ""

# 设置日志级别为debug以获取详细信息
export LOG_LEVEL=debug

# 检查是否有现有的进程
echo "检查现有进程..."
pkill -f "bin/server" 2>/dev/null || true
pkill -f "bin/client" 2>/dev/null || true
sleep 2

# 启动服务器（后台运行）
echo "启动服务器..."
sudo ./bin/server -config configs/server.yaml > server_correct_ip_test.log 2>&1 &
SERVER_PID=$!
echo "服务器PID: $SERVER_PID"

# 等待服务器启动
sleep 3

# 启动客户端（后台运行）
echo "启动客户端..."
sudo ./bin/client -config configs/client.yaml > client_correct_ip_test.log 2>&1 &
CLIENT_PID=$!
echo "客户端PID: $CLIENT_PID"

# 等待客户端连接
sleep 5

echo ""
echo "=== 开始IP映射测试 ==="
echo "数据流程："
echo "  ************* -> tun:******** -> server:*************(lan:********) -> *************"
echo ""
echo "预期行为："
echo "  发送包：src_ip=*************, dst_ip=*************"
echo "  响应包：src_ip=*************, dst_ip=*************"
echo ""

# 通过TUN接口发送ICMP测试
if ip link show cyber-tun >/dev/null 2>&1; then
    echo "通过TUN接口发送ICMP测试..."
    
    # 发送ping测试
    ping -I cyber-tun -c 3 ************* &
    PING_PID=$!
    
    # 等待ping完成
    sleep 8
    kill $PING_PID 2>/dev/null || true
    
else
    echo "TUN接口不存在，跳过ping测试"
fi

echo ""
echo "=== 分析日志 ==="

echo ""
echo "--- 服务器日志（地址映射创建/更新）---"
grep -i "Created new address mapping\|Updated existing address mapping" server_correct_ip_test.log | tail -5

echo ""
echo "--- 服务器日志（ICMP响应包构造）---"
grep -i "ICMP response packet constructed with address mapping" server_correct_ip_test.log | tail -3

echo ""
echo "--- 客户端日志（发送的数据包）---"
echo "发送的数据包源IP："
grep -i "TUN packet forwarded to server" client_correct_ip_test.log | tail -3

echo ""
echo "--- 客户端日志（接收的响应包）---"
echo "接收的响应包目标IP："
grep -i "Received TUN response packet" client_correct_ip_test.log | tail -3

echo ""
echo "=== IP地址对比分析 ==="
echo ""

# 提取发送包的源IP
SENT_SRC_IP=$(grep -i "TUN packet forwarded to server" client_correct_ip_test.log | tail -1 | grep -o '"src_ip":"[^"]*"' | cut -d'"' -f4)
echo "发送包源IP: $SENT_SRC_IP"

# 提取响应包的目标IP
RECV_DST_IP=$(grep -i "Received TUN response packet" client_correct_ip_test.log | tail -1 | grep -o '"dst_ip":"[^"]*"' | cut -d'"' -f4)
echo "响应包目标IP: $RECV_DST_IP"

echo ""
if [ "$SENT_SRC_IP" = "$RECV_DST_IP" ]; then
    echo "✅ 成功！IP地址映射正确"
    echo "   发送包源IP ($SENT_SRC_IP) = 响应包目标IP ($RECV_DST_IP)"
else
    echo "❌ 失败！IP地址映射不正确"
    echo "   发送包源IP: $SENT_SRC_IP"
    echo "   响应包目标IP: $RECV_DST_IP"
    echo "   期望：响应包目标IP应该等于发送包源IP"
fi

echo ""
echo "=== 清理进程 ==="
kill $SERVER_PID 2>/dev/null || true
kill $CLIENT_PID 2>/dev/null || true

echo ""
echo "=== 测试完成 ==="
echo "日志文件保存在："
echo "  - server_correct_ip_test.log"
echo "  - client_correct_ip_test.log"
