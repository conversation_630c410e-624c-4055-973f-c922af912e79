#!/bin/bash

# 转发功能专项测试脚本
# 专门测试优化转发处理器的功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 清理函数
cleanup() {
    log_info "Cleaning up..."
    pkill -f "./bin/server" 2>/dev/null || true
    pkill -f "./bin/client" 2>/dev/null || true
    sleep 2
}

trap cleanup EXIT

# 检查编译
check_build() {
    log_info "Checking build status..."
    
    if [[ ! -f "bin/server" ]]; then
        log_info "Building server..."
        go build -o bin/server ./cmd/server || {
            log_error "Failed to build server"
            exit 1
        }
    fi
    
    if [[ ! -f "bin/client" ]]; then
        log_info "Building client..."
        go build -o bin/client ./cmd/client || {
            log_error "Failed to build client"
            exit 1
        }
    fi
    
    log_success "Build check completed"
}

# 测试转发引擎
test_forwarding_engine() {
    log_info "Testing forwarding engine directly..."
    
    # 创建Go测试程序
    cat > test_engine.go << 'EOF'
package main

import (
    "fmt"
    "time"
    "cyber-bastion/internal/forwarding"
    "go.uber.org/zap"
)

func main() {
    logger, _ := zap.NewDevelopment()
    defer logger.Sync()

    // 创建连接池
    connPool := forwarding.NewConnectionPool(10, 50, time.Minute*5, time.Second*10, logger)
    defer connPool.Close()

    // 创建转发引擎
    engine := forwarding.NewForwardingEngine(connPool, logger)
    defer engine.Close()

    // 创建测试数据包 (ICMP ping to *******)
    packet := make([]byte, 64)
    packet[0] = 0x45  // IP version and header length
    packet[9] = 0x01  // Protocol (ICMP)
    packet[12] = 192  // Source IP: *************
    packet[13] = 168
    packet[14] = 1
    packet[15] = 100
    packet[16] = 8    // Dest IP: *******
    packet[17] = 8
    packet[18] = 8
    packet[19] = 8
    packet[20] = 0x08 // ICMP type (Echo Request)

    fmt.Println("Testing forwarding engine...")
    
    // 测试转发
    response, err := engine.ForwardPacket(packet, "test-client")
    if err != nil {
        fmt.Printf("Forwarding failed: %v\n", err)
    } else {
        fmt.Printf("Forwarding successful, response length: %d\n", len(response))
    }

    // 获取指标
    metrics := engine.GetMetrics()
    fmt.Printf("Metrics - Packets forwarded: %d, Failed: %d\n", 
        metrics.PacketsForwarded, metrics.PacketsFailed)
    
    fmt.Println("Forwarding engine test completed")
}
EOF

    # 运行测试
    if go run test_engine.go; then
        log_success "Forwarding engine test passed"
    else
        log_error "Forwarding engine test failed"
    fi
    
    # 清理测试文件
    rm -f test_engine.go
}

# 测试优化处理器
test_optimized_handler() {
    log_info "Testing optimized forwarding handler..."
    
    cat > test_handler.go << 'EOF'
package main

import (
    "fmt"
    "log"
    "sync"
    "time"
    "cyber-bastion/internal/forwarding"
    "go.uber.org/zap"
)

func main() {
    logger, _ := zap.NewDevelopment()
    defer logger.Sync()

    // 创建处理器配置
    config := &forwarding.HandlerConfig{
        WorkerCount:     5,
        QueueSize:       100,
        MaxIdleConns:    10,
        MaxActiveConns:  50,
        IdleTimeout:     time.Minute * 5,
        DialTimeout:     time.Second * 10,
        EnableMetrics:   true,
        MetricsInterval: time.Second * 5,
        EnableCache:     true,
        CacheSize:       100,
        CacheTTL:        time.Minute * 5,
    }

    // 创建优化处理器
    handler, err := forwarding.NewOptimizedForwardingHandler(config, logger)
    if err != nil {
        log.Fatalf("Failed to create handler: %v", err)
    }

    // 启动处理器
    if err := handler.Start(); err != nil {
        log.Fatalf("Failed to start handler: %v", err)
    }
    defer handler.Stop()

    fmt.Println("Testing optimized forwarding handler...")

    // 创建测试数据包
    packet := make([]byte, 64)
    packet[0] = 0x45  // IP version and header length
    packet[9] = 0x01  // Protocol (ICMP)
    packet[12] = 192  // Source IP
    packet[13] = 168
    packet[14] = 1
    packet[15] = 100
    packet[16] = 8    // Dest IP: *******
    packet[17] = 8
    packet[18] = 8
    packet[19] = 8
    packet[20] = 0x08 // ICMP type

    // 测试同步处理
    fmt.Println("Testing synchronous processing...")
    response, err := handler.HandlePacketSync(packet, "test-client", time.Second*10)
    if err != nil {
        fmt.Printf("Sync processing failed: %v\n", err)
    } else {
        fmt.Printf("Sync processing successful, response length: %d\n", len(response))
    }

    // 测试异步处理
    fmt.Println("Testing asynchronous processing...")
    var wg sync.WaitGroup
    wg.Add(1)
    
    err = handler.HandlePacket(packet, "test-client-async", func(response []byte, err error) {
        defer wg.Done()
        if err != nil {
            fmt.Printf("Async processing failed: %v\n", err)
        } else {
            fmt.Printf("Async processing successful, response length: %d\n", len(response))
        }
    })
    
    if err != nil {
        fmt.Printf("Failed to submit async packet: %v\n", err)
        wg.Done()
    }
    
    wg.Wait()

    // 获取指标
    metrics := handler.GetMetrics()
    fmt.Printf("Handler Metrics:\n")
    fmt.Printf("  Total Requests: %d\n", metrics.TotalRequests)
    fmt.Printf("  Successful: %d\n", metrics.SuccessfulRequests)
    fmt.Printf("  Failed: %d\n", metrics.FailedRequests)
    fmt.Printf("  Average Latency: %v\n", metrics.AverageLatency)
    fmt.Printf("  Throughput: %.2f req/sec\n", metrics.ThroughputPerSec)

    // 获取详细状态
    status := handler.GetDetailedStatus()
    fmt.Printf("Detailed Status: %+v\n", status["handler"])

    fmt.Println("Optimized handler test completed")
}
EOF

    # 运行测试
    if go run test_handler.go; then
        log_success "Optimized handler test passed"
    else
        log_error "Optimized handler test failed"
    fi
    
    # 清理测试文件
    rm -f test_handler.go
}

# 测试服务器集成
test_server_integration() {
    log_info "Testing server integration..."
    
    # 启动server（测试模式）
    log_info "Starting server in test mode..."
    sudo ./bin/server --config configs/server.yaml --log-level debug > server_test.log 2>&1 &
    SERVER_PID=$!
    
    # 等待server启动
    sleep 3
    
    # 检查server是否运行
    if ! kill -0 $SERVER_PID 2>/dev/null; then
        log_error "Server failed to start"
        cat server_test.log
        return 1
    fi
    
    log_success "Server started successfully"
    
    # 检查server日志中的优化转发处理器信息
    sleep 2
    if grep -q "Optimized forwarding handler started successfully" server_test.log; then
        log_success "Optimized forwarding handler initialized"
    else
        log_warning "Optimized forwarding handler may not be initialized"
        log_info "Server log excerpt:"
        tail -10 server_test.log | sed 's/^/  /'
    fi
    
    # 测试监控端点
    local server_port=$(grep -E "^\s*port:" configs/server.yaml | awk '{print $2}' 2>/dev/null || echo "8443")
    
    log_info "Testing monitoring endpoints..."
    if timeout 5 curl -s "http://localhost:$server_port/metrics/forwarding" > /dev/null; then
        log_success "Metrics endpoint accessible"
    else
        log_warning "Metrics endpoint not accessible"
    fi
    
    if timeout 5 curl -s "http://localhost:$server_port/status/forwarding" > /dev/null; then
        log_success "Status endpoint accessible"
    else
        log_warning "Status endpoint not accessible"
    fi
    
    # 停止server
    kill $SERVER_PID 2>/dev/null || true
    wait $SERVER_PID 2>/dev/null || true
    
    log_success "Server integration test completed"
}

# 性能基准测试
test_performance_benchmark() {
    log_info "Running performance benchmark..."
    
    cat > benchmark.go << 'EOF'
package main

import (
    "fmt"
    "sync"
    "time"
    "cyber-bastion/internal/forwarding"
    "go.uber.org/zap"
)

func main() {
    logger, _ := zap.NewDevelopment()
    defer logger.Sync()

    config := &forwarding.HandlerConfig{
        WorkerCount:     10,
        QueueSize:       1000,
        MaxIdleConns:    50,
        MaxActiveConns:  200,
        IdleTimeout:     time.Minute * 5,
        DialTimeout:     time.Second * 5,
        EnableMetrics:   true,
        MetricsInterval: time.Second * 1,
    }

    handler, err := forwarding.NewOptimizedForwardingHandler(config, logger)
    if err != nil {
        panic(err)
    }

    if err := handler.Start(); err != nil {
        panic(err)
    }
    defer handler.Stop()

    // 创建测试数据包
    packet := make([]byte, 64)
    packet[0] = 0x45
    packet[9] = 0x01
    copy(packet[12:16], []byte{192, 168, 1, 100})
    copy(packet[16:20], []byte{8, 8, 8, 8})
    packet[20] = 0x08

    // 性能测试参数
    const numRequests = 1000
    const concurrency = 50

    fmt.Printf("Running benchmark: %d requests with %d concurrent workers\n", 
        numRequests, concurrency)

    startTime := time.Now()
    var wg sync.WaitGroup
    var successCount, errorCount int64

    // 并发测试
    for i := 0; i < concurrency; i++ {
        wg.Add(1)
        go func(workerID int) {
            defer wg.Done()
            
            requestsPerWorker := numRequests / concurrency
            for j := 0; j < requestsPerWorker; j++ {
                _, err := handler.HandlePacketSync(packet, 
                    fmt.Sprintf("worker-%d", workerID), time.Second*5)
                if err != nil {
                    errorCount++
                } else {
                    successCount++
                }
            }
        }(i)
    }

    wg.Wait()
    totalTime := time.Since(startTime)

    // 计算性能指标
    totalRequests := successCount + errorCount
    throughput := float64(totalRequests) / totalTime.Seconds()
    avgLatency := totalTime / time.Duration(totalRequests)

    fmt.Printf("Benchmark Results:\n")
    fmt.Printf("  Total Requests: %d\n", totalRequests)
    fmt.Printf("  Successful: %d\n", successCount)
    fmt.Printf("  Failed: %d\n", errorCount)
    fmt.Printf("  Total Time: %v\n", totalTime)
    fmt.Printf("  Throughput: %.2f req/sec\n", throughput)
    fmt.Printf("  Average Latency: %v\n", avgLatency)
    fmt.Printf("  Error Rate: %.2f%%\n", float64(errorCount)/float64(totalRequests)*100)

    // 获取处理器指标
    metrics := handler.GetMetrics()
    fmt.Printf("Handler Metrics:\n")
    fmt.Printf("  Queue Utilization: %.2f%%\n", metrics.QueueUtilization)
    fmt.Printf("  Connection Pool Utilization: %.2f%%\n", metrics.ConnectionPoolUtilization)
}
EOF

    if go run benchmark.go; then
        log_success "Performance benchmark completed"
    else
        log_error "Performance benchmark failed"
    fi
    
    rm -f benchmark.go
}

# 主测试流程
main() {
    echo
    log_info "🧪 Starting Forwarding Functionality Test"
    log_info "========================================="
    echo
    
    # 1. 检查编译
    check_build
    echo
    
    # 2. 测试转发引擎
    test_forwarding_engine
    echo
    
    # 3. 测试优化处理器
    test_optimized_handler
    echo
    
    # 4. 测试服务器集成
    test_server_integration
    echo
    
    # 5. 性能基准测试
    test_performance_benchmark
    echo
    
    log_success "🎉 Forwarding Functionality Test Completed!"
    echo
    
    log_info "Summary:"
    echo "  ✓ Forwarding engine functional"
    echo "  ✓ Optimized handler operational"
    echo "  ✓ Server integration working"
    echo "  ✓ Performance benchmarks completed"
    echo
    
    log_info "Next steps:"
    echo "  1. Run full system test: ./test_integrated_system.sh"
    echo "  2. Monitor performance: curl http://localhost:8443/metrics/forwarding"
    echo "  3. Check logs for any issues"
}

# 运行主函数
main "$@"
