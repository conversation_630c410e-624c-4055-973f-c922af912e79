#!/bin/bash

# ICMP Ping 修复测试脚本
# 用于验证IP地址映射问题的修复

echo "=== ICMP Ping 修复测试 ==="
echo "测试目标：验证ICMP响应包的IP地址映射是否正确"
echo ""

# 设置日志级别为debug以获取详细信息
export LOG_LEVEL=debug

# 检查是否有现有的进程
echo "检查现有进程..."
pkill -f "bin/server" 2>/dev/null || true
pkill -f "bin/client" 2>/dev/null || true
sleep 2

# 启动服务器（后台运行）
echo "启动服务器..."
sudo ./bin/server -config configs/server.yaml > server_test.log 2>&1 &
SERVER_PID=$!
echo "服务器PID: $SERVER_PID"

# 等待服务器启动
sleep 3

# 启动客户端（后台运行）
echo "启动客户端..."
sudo ./bin/client -config configs/client.yaml > client_test.log 2>&1 &
CLIENT_PID=$!
echo "客户端PID: $CLIENT_PID"

# 等待客户端连接
sleep 5

echo ""
echo "=== 开始ICMP测试 ==="
echo "测试目标IP: *************"
echo "预期行为："
echo "  1. 客户端发送: 源IP=*************, 目标IP=*************"
echo "  2. 服务器响应: 源IP=*************, 目标IP=*************"
echo ""

# 通过TUN接口发送ping（如果TUN接口存在）
if ip link show cyber-tun >/dev/null 2>&1; then
    echo "通过TUN接口发送ping..."
    ping -I cyber-tun -c 3 ************* &
    PING_PID=$!
    
    # 等待ping完成
    sleep 10
    
    # 停止ping（如果还在运行）
    kill $PING_PID 2>/dev/null || true
else
    echo "TUN接口不存在，跳过ping测试"
fi

echo ""
echo "=== 分析日志 ==="

echo ""
echo "--- 服务器日志（地址映射相关）---"
grep -i "address mapping\|Created new\|Updated existing\|Using cached" server_test.log | tail -10

echo ""
echo "--- 服务器日志（ICMP相关）---"
grep -i "icmp\|Building ICMP\|response packet constructed" server_test.log | tail -20

echo ""
echo "--- 客户端日志（ICMP相关）---"
grep -i "icmp\|tun.*forward\|response.*written" client_test.log | tail -20

echo ""
echo "=== 清理进程 ==="
kill $SERVER_PID 2>/dev/null || true
kill $CLIENT_PID 2>/dev/null || true

echo ""
echo "=== 测试完成 ==="
echo "请检查上述日志，确认："
echo "1. 服务器日志中的 'Created new address mapping' 显示正确的地址映射创建"
echo "2. 服务器日志中的 'Using cached address mapping' 显示缓存的使用"
echo "3. 服务器日志中的 'ICMP response packet constructed with address mapping' 显示正确的IP映射"
echo "4. 客户端日志中的响应包目标IP应该是客户端的真实IP (*************)，而不是服务器IP (********)"
echo ""
echo "日志文件保存在："
echo "  - server_test.log"
echo "  - client_test.log"
