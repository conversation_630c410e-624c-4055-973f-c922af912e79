#!/bin/bash

# 编译和基本功能测试脚本（不需要sudo）
# 专门测试编译、代码结构和基本功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 测试编译
test_compilation() {
    log_info "Testing compilation..."
    
    # 清理旧的二进制文件
    rm -f bin/server bin/client
    
    # 编译server
    log_info "Compiling server..."
    if go build -o bin/server ./cmd/server; then
        log_success "Server compiled successfully"
    else
        log_error "Server compilation failed"
        return 1
    fi
    
    # 编译client
    log_info "Compiling client..."
    if go build -o bin/client ./cmd/client; then
        log_success "Client compiled successfully"
    else
        log_error "Client compilation failed"
        return 1
    fi
    
    # 检查二进制文件
    if [[ -f "bin/server" && -f "bin/client" ]]; then
        log_success "Both binaries created successfully"
        ls -la bin/ | grep -E "(server|client)$"
    else
        log_error "Binary files not found"
        return 1
    fi
}

# 测试Go模块
test_go_modules() {
    log_info "Testing Go modules..."
    
    # 检查go.mod
    if [[ -f "go.mod" ]]; then
        log_success "go.mod file exists"
        log_info "Module information:"
        head -5 go.mod | sed 's/^/  /'
    else
        log_error "go.mod file not found"
        return 1
    fi
    
    # 检查依赖
    log_info "Checking dependencies..."
    if go mod verify; then
        log_success "All dependencies verified"
    else
        log_warning "Some dependencies may have issues"
    fi
    
    # 显示依赖列表
    log_info "Key dependencies:"
    go list -m all | grep -E "(zap|yaml|crypto)" | sed 's/^/  /' || true
}

# 测试代码结构
test_code_structure() {
    log_info "Testing code structure..."
    
    # 检查关键目录
    local dirs=("cmd" "internal" "pkg" "configs")
    for dir in "${dirs[@]}"; do
        if [[ -d "$dir" ]]; then
            log_success "$dir directory exists"
        else
            log_warning "$dir directory not found"
        fi
    done
    
    # 检查转发模块
    if [[ -d "internal/forwarding" ]]; then
        log_success "Forwarding module exists"
        log_info "Forwarding module files:"
        ls -la internal/forwarding/ | sed 's/^/  /'
    else
        log_error "Forwarding module not found"
        return 1
    fi
    
    # 检查配置文件
    if [[ -f "configs/server.yaml" && -f "configs/client.yaml" ]]; then
        log_success "Configuration files exist"
    else
        log_warning "Some configuration files may be missing"
    fi
}

# 测试转发模块编译
test_forwarding_module() {
    log_info "Testing forwarding module compilation..."
    
    # 创建简单的测试程序
    cat > test_forwarding_compile.go << 'EOF'
package main

import (
    "fmt"
    "time"
    "cyber-bastion/internal/forwarding"
    "go.uber.org/zap"
)

func main() {
    logger, _ := zap.NewDevelopment()
    defer logger.Sync()

    fmt.Println("Testing forwarding module compilation...")

    // 测试连接池创建
    connPool := forwarding.NewConnectionPool(10, 50, time.Minute*5, time.Second*10, logger)
    if connPool != nil {
        fmt.Println("✓ ConnectionPool creation successful")
    }
    connPool.Close()

    // 测试转发引擎创建
    engine := forwarding.NewForwardingEngine(connPool, logger)
    if engine != nil {
        fmt.Println("✓ ForwardingEngine creation successful")
    }
    engine.Close()

    // 测试优化处理器创建
    config := &forwarding.HandlerConfig{
        WorkerCount:     2,
        QueueSize:       10,
        MaxIdleConns:    5,
        MaxActiveConns:  20,
        IdleTimeout:     time.Minute,
        DialTimeout:     time.Second * 5,
        EnableMetrics:   true,
        MetricsInterval: time.Second * 10,
        EnableCache:     false,
    }

    handler, err := forwarding.NewOptimizedForwardingHandler(config, logger)
    if err != nil {
        fmt.Printf("✗ OptimizedForwardingHandler creation failed: %v\n", err)
    } else {
        fmt.Println("✓ OptimizedForwardingHandler creation successful")
        handler.Stop()
    }

    fmt.Println("Forwarding module compilation test completed successfully")
}
EOF

    # 编译并运行测试
    if go run test_forwarding_compile.go; then
        log_success "Forwarding module compilation test passed"
    else
        log_error "Forwarding module compilation test failed"
        return 1
    fi
    
    # 清理测试文件
    rm -f test_forwarding_compile.go
}

# 测试代码质量
test_code_quality() {
    log_info "Testing code quality..."
    
    # 运行go vet
    log_info "Running go vet..."
    if go vet ./...; then
        log_success "go vet passed"
    else
        log_warning "go vet found some issues"
    fi
    
    # 运行go fmt检查
    log_info "Checking code formatting..."
    local unformatted=$(go fmt ./...)
    if [[ -z "$unformatted" ]]; then
        log_success "Code is properly formatted"
    else
        log_warning "Some files need formatting:"
        echo "$unformatted" | sed 's/^/  /'
    fi
    
    # 检查导入
    log_info "Checking imports..."
    if command -v goimports &> /dev/null; then
        local import_issues=$(goimports -l . | grep -v vendor || true)
        if [[ -z "$import_issues" ]]; then
            log_success "Imports are properly organized"
        else
            log_warning "Some files have import issues"
        fi
    else
        log_info "goimports not available, skipping import check"
    fi
}

# 测试单元测试
test_unit_tests() {
    log_info "Testing unit tests..."
    
    # 查找测试文件
    local test_files=$(find . -name "*_test.go" | wc -l)
    log_info "Found $test_files test files"
    
    # 运行测试
    if go test -v ./... -timeout 30s; then
        log_success "All unit tests passed"
    else
        log_warning "Some unit tests failed or no tests found"
    fi
}

# 生成代码统计
generate_code_stats() {
    log_info "Generating code statistics..."
    
    echo
    log_info "Code Statistics:"
    echo "=================="
    
    # Go文件统计
    local go_files=$(find . -name "*.go" -not -path "./vendor/*" | wc -l)
    echo "  Go files: $go_files"
    
    # 代码行数统计
    local total_lines=$(find . -name "*.go" -not -path "./vendor/*" -exec wc -l {} + | tail -1 | awk '{print $1}')
    echo "  Total lines: $total_lines"
    
    # 包统计
    local packages=$(go list ./... | wc -l)
    echo "  Packages: $packages"
    
    # 关键模块大小
    echo
    echo "  Key modules:"
    if [[ -d "internal/server" ]]; then
        local server_lines=$(find internal/server -name "*.go" -exec wc -l {} + | tail -1 | awk '{print $1}' 2>/dev/null || echo "0")
        echo "    Server: $server_lines lines"
    fi
    
    if [[ -d "internal/client" ]]; then
        local client_lines=$(find internal/client -name "*.go" -exec wc -l {} + | tail -1 | awk '{print $1}' 2>/dev/null || echo "0")
        echo "    Client: $client_lines lines"
    fi
    
    if [[ -d "internal/forwarding" ]]; then
        local forwarding_lines=$(find internal/forwarding -name "*.go" -exec wc -l {} + | tail -1 | awk '{print $1}' 2>/dev/null || echo "0")
        echo "    Forwarding: $forwarding_lines lines"
    fi
    
    # 二进制文件大小
    echo
    echo "  Binary sizes:"
    if [[ -f "bin/server" ]]; then
        local server_size=$(ls -lh bin/server | awk '{print $5}')
        echo "    Server: $server_size"
    fi
    
    if [[ -f "bin/client" ]]; then
        local client_size=$(ls -lh bin/client | awk '{print $5}')
        echo "    Client: $client_size"
    fi
}

# 主测试流程
main() {
    echo
    log_info "🔧 Starting Compilation and Code Quality Test"
    log_info "=============================================="
    echo
    
    # 1. 测试Go模块
    test_go_modules
    echo
    
    # 2. 测试代码结构
    test_code_structure
    echo
    
    # 3. 测试编译
    test_compilation
    echo
    
    # 4. 测试转发模块
    test_forwarding_module
    echo
    
    # 5. 测试代码质量
    test_code_quality
    echo
    
    # 6. 测试单元测试
    test_unit_tests
    echo
    
    # 7. 生成代码统计
    generate_code_stats
    echo
    
    log_success "🎉 Compilation and Code Quality Test Completed!"
    echo
    
    log_info "Test Summary:"
    echo "  ✓ Go modules verified"
    echo "  ✓ Code structure validated"
    echo "  ✓ Compilation successful"
    echo "  ✓ Forwarding module functional"
    echo "  ✓ Code quality checked"
    echo "  ✓ Unit tests executed"
    echo "  ✓ Code statistics generated"
    echo
    
    log_info "Next Steps:"
    echo "  1. Run with sudo for full system testing"
    echo "  2. Test with actual network traffic"
    echo "  3. Performance benchmarking"
    echo "  4. Integration testing with TUN interface"
}

# 运行主函数
main "$@"
