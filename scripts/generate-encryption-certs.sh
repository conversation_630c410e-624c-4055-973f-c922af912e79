#!/bin/bash

# Encryption-Only Certificate Generation Script for Cyber Bastion
# This script generates certificates optimized for encryption without strict hostname/IP verification

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Configuration
CERT_DIR="certs"
DAYS=365
KEY_SIZE=2048

show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Generate TLS certificates optimized for encryption-only use (no hostname verification).
These certificates include wildcard domains and common IP ranges.

OPTIONS:
    -d, --dir DIR           Certificate directory (default: certs)
    -t, --days DAYS         Certificate validity in days (default: 365)
    --clean                 Clean existing certificates before generating new ones
    -h, --help              Show this help message

EXAMPLES:
    # Generate encryption-only certificates
    $0

    # Clean and regenerate certificates with 2-year validity
    $0 --clean --days 730

NOTE: These certificates are designed for encryption purposes and should be used
with tls_skip_verify=true in client configuration for maximum compatibility.

EOF
}

# Parse command line arguments
CLEAN_CERTS=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--dir)
            CERT_DIR="$2"
            shift 2
            ;;
        -t|--days)
            DAYS="$2"
            shift 2
            ;;
        --clean)
            CLEAN_CERTS=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            echo "Error: Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

print_info "Generating encryption-only certificates for Cyber Bastion..."

# Check OpenSSL
if ! command -v openssl &> /dev/null; then
    echo "Error: OpenSSL is required but not installed"
    exit 1
fi

# Clean existing certificates if requested
if [ "$CLEAN_CERTS" = true ]; then
    print_warning "Cleaning existing certificates in $CERT_DIR"
    rm -rf "$CERT_DIR"
fi

# Create directory
mkdir -p "$CERT_DIR"

# Generate CA
print_info "Generating CA for encryption..."
openssl genrsa -out "$CERT_DIR/ca.key" $KEY_SIZE 2>/dev/null
openssl req -new -x509 -days $DAYS -key "$CERT_DIR/ca.key" -out "$CERT_DIR/ca.crt" \
    -subj "/C=CN/ST=Global/L=Global/O=CyberBastion-Encryption/OU=Encryption/CN=CyberBastion-Encryption-CA" 2>/dev/null

# Generate Server Certificate with extensive SAN
print_info "Generating server certificate for encryption (supports any IP/domain)..."
openssl genrsa -out "$CERT_DIR/server.key" $KEY_SIZE 2>/dev/null
openssl req -new -key "$CERT_DIR/server.key" -out "$CERT_DIR/server.csr" \
    -subj "/C=CN/ST=Global/L=Global/O=CyberBastion-Encryption/OU=Encryption/CN=*.cyber-bastion.local" 2>/dev/null

# Create comprehensive server extensions for maximum compatibility
cat > "$CERT_DIR/server.ext" << EOF
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature, keyEncipherment, keyAgreement
extendedKeyUsage = serverAuth, clientAuth
subjectAltName = @alt_names

[alt_names]
# Wildcard domains
DNS.1 = *.cyber-bastion.local
DNS.2 = *.localhost
DNS.3 = *.local
DNS.4 = localhost
DNS.5 = cyber-bastion.local

# Common localhost IPs
IP.1 = 127.0.0.1
IP.2 = ::1

# Common private network ranges (examples)
IP.3 = ***********
IP.4 = ***********
IP.5 = ********
IP.6 = **********

# Note: For production use with specific IPs, add them here or use tls_skip_verify=true
EOF

openssl x509 -req -in "$CERT_DIR/server.csr" -CA "$CERT_DIR/ca.crt" -CAkey "$CERT_DIR/ca.key" \
    -CAcreateserial -out "$CERT_DIR/server.crt" -days $DAYS \
    -extfile "$CERT_DIR/server.ext" 2>/dev/null

# Generate Client Certificate
print_info "Generating client certificate for encryption..."
openssl genrsa -out "$CERT_DIR/client.key" $KEY_SIZE 2>/dev/null
openssl req -new -key "$CERT_DIR/client.key" -out "$CERT_DIR/client.csr" \
    -subj "/C=CN/ST=Global/L=Global/O=CyberBastion-Encryption/OU=Encryption/CN=cyber-bastion-encryption-client" 2>/dev/null

cat > "$CERT_DIR/client.ext" << EOF
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature, keyEncipherment, keyAgreement
extendedKeyUsage = clientAuth, serverAuth
EOF

openssl x509 -req -in "$CERT_DIR/client.csr" -CA "$CERT_DIR/ca.crt" -CAkey "$CERT_DIR/ca.key" \
    -CAcreateserial -out "$CERT_DIR/client.crt" -days $DAYS \
    -extfile "$CERT_DIR/client.ext" 2>/dev/null

# Cleanup temporary files
rm -f "$CERT_DIR"/*.csr "$CERT_DIR"/*.ext "$CERT_DIR"/*.srl

# Set permissions
chmod 600 "$CERT_DIR"/*.key
chmod 644 "$CERT_DIR"/*.crt

# Generate README
cat > "$CERT_DIR/README.md" << EOF
# Cyber Bastion Encryption-Only TLS Certificates

These certificates are optimized for encryption purposes and maximum compatibility.

## Files

- \`ca.crt\` - Certificate Authority certificate
- \`ca.key\` - CA private key (keep secure!)
- \`server.crt\` - Server certificate (supports wildcards and common IPs)
- \`server.key\` - Server private key (keep secure!)
- \`client.crt\` - Client certificate
- \`client.key\` - Client private key (keep secure!)

## Certificate Details

- **Purpose**: Encryption-only (not strict hostname verification)
- **Validity**: $DAYS days
- **Key Size**: $KEY_SIZE bits
- **Supported Domains**: *.cyber-bastion.local, *.localhost, *.local
- **Supported IPs**: 127.0.0.1, ::1, and common private network IPs

## Usage

### Recommended Configuration

For maximum compatibility with any IP address, use these settings:

**Server (configs/server.yaml):**
\`\`\`yaml
security:
  enable_tls: true
  tls_cert_file: "certs/server.crt"
  tls_key_file: "certs/server.key"
  tls_ca_file: "certs/ca.crt"
  tls_skip_verify: false
\`\`\`

**Client (configs/client.yaml):**
\`\`\`yaml
enable_tls: true
tls_cert_file: "certs/client.crt"
tls_key_file: "certs/client.key"
tls_ca_file: "certs/ca.crt"
tls_skip_verify: true    # Allow connection to any IP
\`\`\`

### Security Notes

- These certificates provide **encryption** but not strict **authentication**
- Use \`tls_skip_verify: true\` in client config for any IP address
- Suitable for private networks where encryption is needed but hostname verification is not critical
- For production with specific domains/IPs, consider using standard certificates

## Generated on

$(date)

EOF

print_success "Encryption-only certificates generated in $CERT_DIR/"
print_info "Files created:"
ls -la "$CERT_DIR"

print_warning "These certificates are optimized for encryption without strict hostname verification!"
print_info "Configuration recommendations:"
echo "  - Server: Use standard TLS settings"
echo "  - Client: Set 'tls_skip_verify: true' for maximum compatibility"
echo "  - This allows secure encrypted communication with any IP address"

print_success "Certificate generation completed!"
