#!/bin/bash

# Certificate Verification Script for Cyber Bastion
# This script verifies the generated certificates and shows their details

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default certificate directory
CERT_DIR="certs"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--dir)
            CERT_DIR="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Verify TLS certificates for Cyber Bastion."
            echo ""
            echo "OPTIONS:"
            echo "    -d, --dir DIR    Certificate directory (default: certs)"
            echo "    -h, --help       Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Check if certificate directory exists
if [ ! -d "$CERT_DIR" ]; then
    print_error "Certificate directory '$CERT_DIR' does not exist"
    print_info "Run './scripts/generate-certs.sh' or './scripts/quick-certs.sh' first"
    exit 1
fi

print_info "Verifying certificates in directory: $CERT_DIR"
echo

# Function to check if file exists
check_file() {
    local file="$1"
    local description="$2"
    
    if [ -f "$file" ]; then
        print_success "$description exists: $file"
        return 0
    else
        print_error "$description missing: $file"
        return 1
    fi
}

# Function to verify certificate
verify_cert() {
    local cert_file="$1"
    local description="$2"
    
    print_info "Verifying $description..."
    
    if ! openssl x509 -in "$cert_file" -text -noout > /dev/null 2>&1; then
        print_error "$description is invalid or corrupted"
        return 1
    fi
    
    # Get certificate details
    local subject=$(openssl x509 -in "$cert_file" -subject -noout | sed 's/subject=//')
    local issuer=$(openssl x509 -in "$cert_file" -issuer -noout | sed 's/issuer=//')
    local not_before=$(openssl x509 -in "$cert_file" -startdate -noout | sed 's/notBefore=//')
    local not_after=$(openssl x509 -in "$cert_file" -enddate -noout | sed 's/notAfter=//')
    
    echo "  Subject: $subject"
    echo "  Issuer: $issuer"
    echo "  Valid from: $not_before"
    echo "  Valid until: $not_after"
    
    # Check if certificate is expired
    if ! openssl x509 -in "$cert_file" -checkend 0 > /dev/null 2>&1; then
        print_warning "$description has expired!"
    else
        print_success "$description is valid"
    fi
    
    # Check if certificate expires soon (within 30 days)
    if ! openssl x509 -in "$cert_file" -checkend 2592000 > /dev/null 2>&1; then
        print_warning "$description expires within 30 days"
    fi
    
    echo
}

# Function to verify private key
verify_key() {
    local key_file="$1"
    local description="$2"
    
    print_info "Verifying $description..."
    
    if ! openssl rsa -in "$key_file" -check -noout > /dev/null 2>&1; then
        print_error "$description is invalid or corrupted"
        return 1
    fi
    
    print_success "$description is valid"
    
    # Check key permissions
    local perms=$(stat -f "%A" "$key_file" 2>/dev/null || stat -c "%a" "$key_file" 2>/dev/null)
    if [ "$perms" != "600" ]; then
        print_warning "$description has insecure permissions ($perms). Should be 600."
    else
        print_success "$description has secure permissions (600)"
    fi
    
    echo
}

# Function to verify certificate chain
verify_chain() {
    local cert_file="$1"
    local ca_file="$2"
    local description="$3"
    
    print_info "Verifying $description certificate chain..."
    
    if openssl verify -CAfile "$ca_file" "$cert_file" > /dev/null 2>&1; then
        print_success "$description certificate chain is valid"
    else
        print_error "$description certificate chain verification failed"
        return 1
    fi
    
    echo
}

# Function to check certificate and key match
verify_cert_key_match() {
    local cert_file="$1"
    local key_file="$2"
    local description="$3"
    
    print_info "Verifying $description certificate and key match..."
    
    local cert_modulus=$(openssl x509 -in "$cert_file" -modulus -noout 2>/dev/null)
    local key_modulus=$(openssl rsa -in "$key_file" -modulus -noout 2>/dev/null)
    
    if [ "$cert_modulus" = "$key_modulus" ]; then
        print_success "$description certificate and key match"
    else
        print_error "$description certificate and key do not match"
        return 1
    fi
    
    echo
}

# Function to show certificate extensions
show_cert_extensions() {
    local cert_file="$1"
    local description="$2"
    
    print_info "$description extensions:"
    
    # Show Subject Alternative Names
    local san=$(openssl x509 -in "$cert_file" -text -noout | grep -A1 "Subject Alternative Name" | tail -1 | sed 's/^[[:space:]]*//')
    if [ -n "$san" ]; then
        echo "  Subject Alternative Names: $san"
    fi
    
    # Show Key Usage
    local key_usage=$(openssl x509 -in "$cert_file" -text -noout | grep -A1 "Key Usage" | tail -1 | sed 's/^[[:space:]]*//')
    if [ -n "$key_usage" ]; then
        echo "  Key Usage: $key_usage"
    fi
    
    # Show Extended Key Usage
    local ext_key_usage=$(openssl x509 -in "$cert_file" -text -noout | grep -A1 "Extended Key Usage" | tail -1 | sed 's/^[[:space:]]*//')
    if [ -n "$ext_key_usage" ]; then
        echo "  Extended Key Usage: $ext_key_usage"
    fi
    
    echo
}

# Main verification process
main() {
    local errors=0
    
    print_info "Starting certificate verification..."
    echo
    
    # Check if all required files exist
    print_info "Checking file existence..."
    check_file "$CERT_DIR/ca.crt" "CA certificate" || ((errors++))
    check_file "$CERT_DIR/ca.key" "CA private key" || ((errors++))
    check_file "$CERT_DIR/server.crt" "Server certificate" || ((errors++))
    check_file "$CERT_DIR/server.key" "Server private key" || ((errors++))
    check_file "$CERT_DIR/client.crt" "Client certificate" || ((errors++))
    check_file "$CERT_DIR/client.key" "Client private key" || ((errors++))
    echo
    
    if [ $errors -gt 0 ]; then
        print_error "Some certificate files are missing. Cannot proceed with verification."
        exit 1
    fi
    
    # Verify certificates
    verify_cert "$CERT_DIR/ca.crt" "CA certificate" || ((errors++))
    verify_cert "$CERT_DIR/server.crt" "Server certificate" || ((errors++))
    verify_cert "$CERT_DIR/client.crt" "Client certificate" || ((errors++))
    
    # Verify private keys
    verify_key "$CERT_DIR/ca.key" "CA private key" || ((errors++))
    verify_key "$CERT_DIR/server.key" "Server private key" || ((errors++))
    verify_key "$CERT_DIR/client.key" "Client private key" || ((errors++))
    
    # Verify certificate chains
    verify_chain "$CERT_DIR/server.crt" "$CERT_DIR/ca.crt" "Server" || ((errors++))
    verify_chain "$CERT_DIR/client.crt" "$CERT_DIR/ca.crt" "Client" || ((errors++))
    
    # Verify certificate and key matches
    verify_cert_key_match "$CERT_DIR/server.crt" "$CERT_DIR/server.key" "Server" || ((errors++))
    verify_cert_key_match "$CERT_DIR/client.crt" "$CERT_DIR/client.key" "Client" || ((errors++))
    
    # Show certificate extensions
    show_cert_extensions "$CERT_DIR/server.crt" "Server certificate"
    show_cert_extensions "$CERT_DIR/client.crt" "Client certificate"
    
    # Summary
    if [ $errors -eq 0 ]; then
        print_success "All certificates are valid and properly configured!"
        print_info "You can now enable TLS in your configuration files:"
        echo "  - Set 'enable_tls: true' in configs/server.yaml"
        echo "  - Set 'enable_tls: true' in configs/client.yaml"
    else
        print_error "Certificate verification completed with $errors error(s)"
        print_info "Please regenerate certificates using:"
        echo "  ./scripts/generate-certs.sh --clean"
        exit 1
    fi
}

# Check if OpenSSL is available
if ! command -v openssl &> /dev/null; then
    print_error "OpenSSL is not installed or not in PATH"
    exit 1
fi

# Run main function
main
