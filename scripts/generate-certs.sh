#!/bin/bash

# Cyber Bastion Certificate Generation Script
# This script generates self-signed certificates for TLS communication
# between the server and client components.

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
CERT_DIR="certs"
DAYS=365
KEY_SIZE=2048
COUNTRY="CN"
STATE="Beijing"
CITY="Beijing"
ORG="Cyber Bastion"
ORG_UNIT="Security"
SERVER_CN="localhost"
CLIENT_CN="cyber-bastion-client"

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Generate TLS certificates for Cyber Bastion server and client.

OPTIONS:
    -d, --dir DIR           Certificate directory (default: certs)
    -t, --days DAYS         Certificate validity in days (default: 365)
    -k, --key-size SIZE     RSA key size (default: 2048)
    -c, --country CODE      Country code (default: CN)
    -s, --state STATE       State/Province (default: Beijing)
    -l, --city CITY         City/Locality (default: Beijing)
    -o, --org ORG           Organization (default: Cyber Bastion)
    -u, --org-unit UNIT     Organizational Unit (default: Security)
    --server-cn CN          Server Common Name (default: localhost)
    --client-cn CN          Client Common Name (default: cyber-bastion-client)
    --server-ips IPS        Additional server IP addresses (comma-separated)
    --clean                 Clean existing certificates before generating new ones
    -h, --help              Show this help message

EXAMPLES:
    # Generate certificates with default settings
    $0

    # Generate certificates for production with custom domain
    $0 --server-cn "bastion.example.com" --days 730

    # Clean and regenerate certificates
    $0 --clean

EOF
}

# Function to clean existing certificates
clean_certs() {
    if [ -d "$CERT_DIR" ]; then
        print_warning "Cleaning existing certificates in $CERT_DIR"
        rm -rf "$CERT_DIR"
    fi
}

# Function to create certificate directory
create_cert_dir() {
    if [ ! -d "$CERT_DIR" ]; then
        print_info "Creating certificate directory: $CERT_DIR"
        mkdir -p "$CERT_DIR"
    fi
}

# Function to generate CA certificate
generate_ca() {
    print_info "Generating Certificate Authority (CA)..."
    
    # Generate CA private key
    openssl genrsa -out "$CERT_DIR/ca.key" $KEY_SIZE
    
    # Generate CA certificate
    openssl req -new -x509 -days $DAYS -key "$CERT_DIR/ca.key" -out "$CERT_DIR/ca.crt" \
        -subj "/C=$COUNTRY/ST=$STATE/L=$CITY/O=$ORG/OU=$ORG_UNIT/CN=Cyber Bastion CA"
    
    print_success "CA certificate generated: $CERT_DIR/ca.crt"
}

# Function to generate server certificate
generate_server_cert() {
    print_info "Generating server certificate..."
    
    # Generate server private key
    openssl genrsa -out "$CERT_DIR/server.key" $KEY_SIZE
    
    # Generate server certificate signing request
    openssl req -new -key "$CERT_DIR/server.key" -out "$CERT_DIR/server.csr" \
        -subj "/C=$COUNTRY/ST=$STATE/L=$CITY/O=$ORG/OU=$ORG_UNIT/CN=$SERVER_CN"
    
    # Create server certificate extensions
    cat > "$CERT_DIR/server.ext" << EOF
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature, nonRepudiation, keyEncipherment, dataEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = $SERVER_CN
DNS.2 = localhost
DNS.3 = *.localhost
IP.1 = 127.0.0.1
IP.2 = ::1
EOF

    # Add additional IPs if SERVER_ADDITIONAL_IPS is set
    if [ -n "$SERVER_ADDITIONAL_IPS" ]; then
        local ip_count=3
        IFS=',' read -ra IPS <<< "$SERVER_ADDITIONAL_IPS"
        for ip in "${IPS[@]}"; do
            ip=$(echo "$ip" | xargs)  # trim whitespace
            echo "IP.$ip_count = $ip" >> "$CERT_DIR/server.ext"
            ((ip_count++))
        done
    fi
    
    # Generate server certificate signed by CA
    openssl x509 -req -in "$CERT_DIR/server.csr" -CA "$CERT_DIR/ca.crt" -CAkey "$CERT_DIR/ca.key" \
        -CAcreateserial -out "$CERT_DIR/server.crt" -days $DAYS \
        -extfile "$CERT_DIR/server.ext"
    
    # Clean up temporary files
    rm "$CERT_DIR/server.csr" "$CERT_DIR/server.ext"
    
    print_success "Server certificate generated: $CERT_DIR/server.crt"
}

# Function to generate client certificate
generate_client_cert() {
    print_info "Generating client certificate..."
    
    # Generate client private key
    openssl genrsa -out "$CERT_DIR/client.key" $KEY_SIZE
    
    # Generate client certificate signing request
    openssl req -new -key "$CERT_DIR/client.key" -out "$CERT_DIR/client.csr" \
        -subj "/C=$COUNTRY/ST=$STATE/L=$CITY/O=$ORG/OU=$ORG_UNIT/CN=$CLIENT_CN"
    
    # Create client certificate extensions
    cat > "$CERT_DIR/client.ext" << EOF
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature, nonRepudiation, keyEncipherment, dataEncipherment
extendedKeyUsage = clientAuth
EOF
    
    # Generate client certificate signed by CA
    openssl x509 -req -in "$CERT_DIR/client.csr" -CA "$CERT_DIR/ca.crt" -CAkey "$CERT_DIR/ca.key" \
        -CAcreateserial -out "$CERT_DIR/client.crt" -days $DAYS \
        -extfile "$CERT_DIR/client.ext"
    
    # Clean up temporary files
    rm "$CERT_DIR/client.csr" "$CERT_DIR/client.ext"
    
    print_success "Client certificate generated: $CERT_DIR/client.crt"
}

# Function to set proper permissions
set_permissions() {
    print_info "Setting proper file permissions..."
    
    # Set restrictive permissions for private keys
    chmod 600 "$CERT_DIR"/*.key
    
    # Set readable permissions for certificates
    chmod 644 "$CERT_DIR"/*.crt
    
    print_success "File permissions set"
}

# Function to display certificate information
show_cert_info() {
    print_info "Certificate Information:"
    echo
    
    print_info "CA Certificate:"
    openssl x509 -in "$CERT_DIR/ca.crt" -text -noout | grep -E "(Subject:|Not Before|Not After)"
    echo
    
    print_info "Server Certificate:"
    openssl x509 -in "$CERT_DIR/server.crt" -text -noout | grep -E "(Subject:|Not Before|Not After|DNS:|IP Address:)"
    echo
    
    print_info "Client Certificate:"
    openssl x509 -in "$CERT_DIR/client.crt" -text -noout | grep -E "(Subject:|Not Before|Not After)"
    echo
}

# Function to generate certificate summary
generate_summary() {
    cat > "$CERT_DIR/README.md" << EOF
# Cyber Bastion TLS Certificates

This directory contains TLS certificates generated for Cyber Bastion secure communication.

## Files

- \`ca.crt\` - Certificate Authority (CA) certificate
- \`ca.key\` - CA private key (keep secure!)
- \`server.crt\` - Server certificate
- \`server.key\` - Server private key (keep secure!)
- \`client.crt\` - Client certificate
- \`client.key\` - Client private key (keep secure!)

## Certificate Details

- **Validity**: $DAYS days
- **Key Size**: $KEY_SIZE bits
- **Server CN**: $SERVER_CN
- **Client CN**: $CLIENT_CN

## Usage

1. Enable TLS in your configuration files:
   - Set \`enable_tls: true\` in server.yaml and client.yaml
   - Ensure certificate paths are correct

2. For production use:
   - Replace self-signed certificates with certificates from a trusted CA
   - Use proper domain names instead of localhost
   - Store private keys securely

## Security Notes

- Private key files (.key) should have restricted permissions (600)
- Never share private keys
- Regenerate certificates before expiration
- Use strong passwords for production environments

Generated on: $(date)
EOF
    
    print_success "Certificate summary created: $CERT_DIR/README.md"
}

# Parse command line arguments
CLEAN_CERTS=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--dir)
            CERT_DIR="$2"
            shift 2
            ;;
        -t|--days)
            DAYS="$2"
            shift 2
            ;;
        -k|--key-size)
            KEY_SIZE="$2"
            shift 2
            ;;
        -c|--country)
            COUNTRY="$2"
            shift 2
            ;;
        -s|--state)
            STATE="$2"
            shift 2
            ;;
        -l|--city)
            CITY="$2"
            shift 2
            ;;
        -o|--org)
            ORG="$2"
            shift 2
            ;;
        -u|--org-unit)
            ORG_UNIT="$2"
            shift 2
            ;;
        --server-cn)
            SERVER_CN="$2"
            shift 2
            ;;
        --client-cn)
            CLIENT_CN="$2"
            shift 2
            ;;
        --clean)
            CLEAN_CERTS=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    print_info "Starting Cyber Bastion certificate generation..."
    print_info "Certificate directory: $CERT_DIR"
    print_info "Validity period: $DAYS days"
    print_info "Key size: $KEY_SIZE bits"
    print_info "Server CN: $SERVER_CN"
    print_info "Client CN: $CLIENT_CN"
    echo
    
    # Check if OpenSSL is available
    if ! command -v openssl &> /dev/null; then
        print_error "OpenSSL is not installed or not in PATH"
        exit 1
    fi
    
    # Clean existing certificates if requested
    if [ "$CLEAN_CERTS" = true ]; then
        clean_certs
    fi
    
    # Create certificate directory
    create_cert_dir
    
    # Generate certificates
    generate_ca
    generate_server_cert
    generate_client_cert
    
    # Set proper permissions
    set_permissions
    
    # Generate summary
    generate_summary
    
    # Show certificate information
    show_cert_info
    
    print_success "Certificate generation completed successfully!"
    print_info "Certificates are stored in: $CERT_DIR"
    print_warning "Remember to update your configuration files to enable TLS"
}

# Run main function
main
