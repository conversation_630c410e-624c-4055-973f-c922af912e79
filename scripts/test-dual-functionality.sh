#!/bin/bash

# 双重功能测试脚本
# 测试HTTP伪装和客户端连接功能可以同时正常工作

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 全局变量
SERVER_PID=""
CLIENT_PID=""

# 清理函数
cleanup() {
    print_info "清理资源..."
    
    if [ ! -z "$CLIENT_PID" ] && kill -0 $CLIENT_PID 2>/dev/null; then
        print_info "停止客户端..."
        kill $CLIENT_PID
        wait $CLIENT_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$SERVER_PID" ] && kill -0 $SERVER_PID 2>/dev/null; then
        print_info "停止服务器..."
        kill $SERVER_PID
        wait $SERVER_PID 2>/dev/null || true
    fi
    
    # 清理日志文件
    rm -f server_dual_test.log client_dual_test.log
    
    print_success "清理完成"
}

# 构建项目
build_project() {
    print_info "构建项目..."
    
    if ! go build -o bin/server ./cmd/server; then
        print_error "服务器构建失败"
        exit 1
    fi
    
    if ! go build -o bin/client ./cmd/client; then
        print_error "客户端构建失败"
        exit 1
    fi
    
    print_success "项目构建完成"
}

# 启动服务器
start_server() {
    print_info "启动服务器..."
    
    # 检查端口是否被占用
    if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_warning "端口8080已被占用，尝试停止现有进程..."
        pkill -f "bin/server" || true
        sleep 2
    fi
    
    # 启动服务器
    ./bin/server --config configs/server.yaml --log-level info > server_dual_test.log 2>&1 &
    SERVER_PID=$!
    
    # 等待服务器启动
    sleep 3
    
    # 检查服务器是否正在运行
    if ! kill -0 $SERVER_PID 2>/dev/null; then
        print_error "服务器启动失败"
        cat server_dual_test.log
        exit 1
    fi
    
    print_success "服务器已启动 (PID: $SERVER_PID)"
}

# 启动客户端
start_client() {
    print_info "启动客户端..."
    
    # 启动客户端（后台运行）
    ./bin/client --config configs/client.yaml --log-level info > client_dual_test.log 2>&1 &
    CLIENT_PID=$!
    
    # 等待客户端连接
    sleep 5
    
    # 检查客户端是否正在运行
    if ! kill -0 $CLIENT_PID 2>/dev/null; then
        print_error "客户端启动失败"
        cat client_dual_test.log
        exit 1
    fi
    
    print_success "客户端已启动 (PID: $CLIENT_PID)"
}

# 测试HTTP伪装功能
test_http_masquerading() {
    print_info "测试HTTP伪装功能..."
    
    # 测试HTTP请求
    HTTP_RESPONSE=$(curl -s -w "HTTPCODE:%{http_code}" http://localhost:8080 2>/dev/null || echo "FAILED")
    
    if [[ $HTTP_RESPONSE == *"FAILED"* ]]; then
        print_error "HTTP请求失败"
        return 1
    fi
    
    HTTP_CODE=$(echo "$HTTP_RESPONSE" | grep -o "HTTPCODE:[0-9]*" | cut -d: -f2)
    
    if [ "$HTTP_CODE" = "200" ]; then
        print_success "HTTP伪装功能正常 (状态码: $HTTP_CODE)"
    else
        print_error "HTTP伪装功能异常 (状态码: $HTTP_CODE)"
        return 1
    fi
    
    # 测试HTTPS请求
    HTTPS_RESPONSE=$(curl -s -k -w "HTTPCODE:%{http_code}" https://localhost:8080 2>/dev/null || echo "FAILED")
    
    if [[ $HTTPS_RESPONSE == *"FAILED"* ]]; then
        print_error "HTTPS请求失败"
        return 1
    fi
    
    HTTPS_CODE=$(echo "$HTTPS_RESPONSE" | grep -o "HTTPCODE:[0-9]*" | cut -d: -f2)
    
    if [ "$HTTPS_CODE" = "200" ]; then
        print_success "HTTPS伪装功能正常 (状态码: $HTTPS_CODE)"
    else
        print_error "HTTPS伪装功能异常 (状态码: $HTTPS_CODE)"
        return 1
    fi
    
    return 0
}

# 测试客户端连接功能
test_client_connectivity() {
    print_info "测试客户端连接功能..."
    
    # 检查客户端日志中的成功连接信息
    if grep -q "Client started successfully" client_dual_test.log; then
        print_success "客户端连接成功"
    else
        print_error "客户端连接失败"
        print_info "客户端日志:"
        cat client_dual_test.log
        return 1
    fi
    
    # 检查认证是否成功
    if grep -q "Authentication successful" client_dual_test.log; then
        print_success "客户端认证成功"
    else
        print_error "客户端认证失败"
        return 1
    fi
    
    # 检查是否有数据传输
    sleep 5  # 等待一些数据传输
    
    if grep -q "Test message sent" client_dual_test.log; then
        print_success "客户端数据传输正常"
    else
        print_warning "客户端数据传输可能异常"
    fi
    
    return 0
}

# 测试服务器日志
test_server_logs() {
    print_info "检查服务器日志..."
    
    # 检查HTTP请求记录
    if grep -q "HTTP request detected" server_dual_test.log; then
        print_success "服务器正确记录HTTP请求"
    else
        print_warning "服务器未记录HTTP请求"
    fi
    
    # 检查HTTPS请求记录
    if grep -q "HTTPS request detected" server_dual_test.log; then
        print_success "服务器正确记录HTTPS请求"
    else
        print_warning "服务器未记录HTTPS请求"
    fi
    
    # 检查客户端认证记录
    if grep -q "Client authenticated" server_dual_test.log; then
        print_success "服务器正确记录客户端认证"
    else
        print_error "服务器未记录客户端认证"
        return 1
    fi
    
    # 检查自定义协议检测
    if grep -q "Custom protocol message detected in TLS connection" server_dual_test.log; then
        print_success "服务器正确检测自定义协议"
    else
        print_warning "服务器未记录自定义协议检测（可能使用info级别日志）"
        # 检查是否有客户端认证成功，这也表明自定义协议工作正常
        if grep -q "Client authenticated" server_dual_test.log; then
            print_success "通过客户端认证确认自定义协议正常工作"
        else
            print_error "服务器未正确处理自定义协议"
            return 1
        fi
    fi
    
    return 0
}

# 并发测试
test_concurrent_operations() {
    print_info "测试并发操作..."
    
    # 同时发送多个HTTP请求
    for i in {1..3}; do
        curl -s http://localhost:8080 > /dev/null &
    done
    
    # 等待HTTP请求完成
    wait
    
    # 检查客户端是否仍然正常工作
    sleep 2
    
    if kill -0 $CLIENT_PID 2>/dev/null; then
        print_success "并发HTTP请求不影响客户端连接"
    else
        print_error "并发HTTP请求影响了客户端连接"
        return 1
    fi
    
    return 0
}

# 主函数
main() {
    print_info "开始双重功能测试..."
    
    # 设置清理函数
    trap cleanup EXIT
    
    # 执行测试步骤
    build_project
    start_server
    start_client
    
    # 等待系统稳定
    sleep 3
    
    # 执行功能测试
    local test_results=0
    
    if test_http_masquerading; then
        print_success "HTTP伪装功能测试通过"
    else
        print_error "HTTP伪装功能测试失败"
        ((test_results++))
    fi
    
    if test_client_connectivity; then
        print_success "客户端连接功能测试通过"
    else
        print_error "客户端连接功能测试失败"
        ((test_results++))
    fi
    
    if test_server_logs; then
        print_success "服务器日志检查通过"
    else
        print_error "服务器日志检查失败"
        ((test_results++))
    fi
    
    if test_concurrent_operations; then
        print_success "并发操作测试通过"
    else
        print_error "并发操作测试失败"
        ((test_results++))
    fi
    
    # 显示统计信息
    print_info "测试统计:"
    echo "  - HTTP请求数: $(grep -c "HTTP request detected" server_dual_test.log 2>/dev/null || echo 0)"
    echo "  - HTTPS请求数: $(grep -c "HTTPS request detected" server_dual_test.log 2>/dev/null || echo 0)"
    echo "  - 客户端消息数: $(grep -c "Test message sent" client_dual_test.log 2>/dev/null || echo 0)"
    echo "  - 安全事件数: $(grep -c "Security Event" server_dual_test.log 2>/dev/null || echo 0)"
    
    # 总结
    if [ $test_results -eq 0 ]; then
        print_success "所有双重功能测试通过！"
        print_info "服务器成功同时支持HTTP伪装和客户端连接功能"
        exit 0
    else
        print_error "有 $test_results 个测试失败"
        exit 1
    fi
}

# 运行主函数
main "$@"
