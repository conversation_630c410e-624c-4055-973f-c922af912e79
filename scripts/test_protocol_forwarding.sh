#!/bin/bash

# 🚀 协议转发测试脚本
# 验证透明代理对各种协议的处理能力

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试透明代理数据包解析
test_packet_parsing() {
    log_info "Testing packet parsing capabilities..."

    # 直接运行现有的数据包解析测试
    if go test ./internal/server/transparent -run TestPacketParsing -v > /tmp/packet_test_results.log 2>&1; then
        log_success "Packet parsing test completed"
        cat /tmp/packet_test_results.log
    else
        log_error "Packet parsing test failed"
        cat /tmp/packet_test_results.log
        return 1
    fi
}

# 测试协议识别
test_protocol_identification() {
    log_info "Testing protocol identification..."
    
    # 运行协议识别测试
    go test ./internal/server/transparent -run TestPacketParsing -v > /tmp/protocol_test_results.log 2>&1
    
    if [ $? -eq 0 ]; then
        log_success "Protocol identification tests passed"
        grep -E "(ICMP|TCP|UDP)" /tmp/protocol_test_results.log || log_info "No protocol-specific output found"
    else
        log_error "Protocol identification tests failed"
        cat /tmp/protocol_test_results.log
        return 1
    fi
}

# 测试地址映射
test_address_mapping() {
    log_info "Testing address mapping functionality..."
    
    # 运行地址映射测试
    go test ./internal/server/transparent -run TestAddressMapping -v > /tmp/address_mapping_results.log 2>&1
    
    if [ $? -eq 0 ]; then
        log_success "Address mapping tests passed"
        grep -E "(mapping|address)" /tmp/address_mapping_results.log || log_info "No mapping-specific output found"
    else
        log_error "Address mapping tests failed"
        cat /tmp/address_mapping_results.log
        return 1
    fi
}

# 验证透明代理架构
verify_transparent_architecture() {
    log_info "Verifying transparent proxy architecture..."
    
    # 检查是否所有协议都使用统一的透明代理
    if grep -r "transparent.*proxy" internal/server/server.go > /dev/null; then
        log_success "Server uses unified transparent proxy architecture"
    else
        log_warning "Transparent proxy integration not found in server code"
    fi
    
    # 检查是否移除了应用层代理逻辑
    if ! grep -r "application.*layer.*proxy" internal/server/ > /dev/null; then
        log_success "Application layer proxy logic removed"
    else
        log_warning "Application layer proxy logic may still exist"
    fi
    
    # 检查原始套接字使用
    if grep -r "raw.*socket\|syscall.*socket" internal/server/transparent/ > /dev/null; then
        log_success "Raw socket forwarding implemented"
    else
        log_warning "Raw socket usage not clearly identified"
    fi
}

# 主测试流程
main() {
    log_info "🚀 Starting Protocol Forwarding Tests"
    log_info "====================================="
    
    # 测试数据包解析
    test_packet_parsing
    
    # 测试协议识别
    test_protocol_identification
    
    # 测试地址映射
    test_address_mapping
    
    # 验证透明代理架构
    verify_transparent_architecture
    
    log_info "====================================="
    log_success "🎉 Protocol forwarding tests completed!"
    
    # 清理临时文件
    rm -rf /tmp/test_packets
    rm -f /tmp/packet_test_results.log
    rm -f /tmp/protocol_test_results.log
    rm -f /tmp/address_mapping_results.log
}

# 运行主函数
main "$@"
