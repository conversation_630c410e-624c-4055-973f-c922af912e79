#!/bin/bash

# Quick Certificate Generation Script for Cyber Bastion
# This is a simplified version for rapid development and testing

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Configuration
CERT_DIR="certs"
DAYS=365
KEY_SIZE=2048

print_info "Quick certificate generation for Cyber Bastion..."

# Check OpenSSL
if ! command -v openssl &> /dev/null; then
    echo "Error: OpenSSL is required but not installed"
    exit 1
fi

# Create directory
mkdir -p "$CERT_DIR"

# Generate CA
print_info "Generating CA..."
openssl genrsa -out "$CERT_DIR/ca.key" $KEY_SIZE 2>/dev/null
openssl req -new -x509 -days $DAYS -key "$CERT_DIR/ca.key" -out "$CERT_DIR/ca.crt" \
    -subj "/C=CN/ST=Beijing/L=Beijing/O=CyberBastion/OU=Dev/CN=CyberBastion-CA" 2>/dev/null

# Generate Server Certificate
print_info "Generating server certificate..."
openssl genrsa -out "$CERT_DIR/server.key" $KEY_SIZE 2>/dev/null
openssl req -new -key "$CERT_DIR/server.key" -out "$CERT_DIR/server.csr" \
    -subj "/C=CN/ST=Beijing/L=Beijing/O=CyberBastion/OU=Dev/CN=localhost" 2>/dev/null

# Server extensions
cat > "$CERT_DIR/server.ext" << EOF
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = *.localhost
IP.1 = 127.0.0.1
IP.2 = ::1
EOF

openssl x509 -req -in "$CERT_DIR/server.csr" -CA "$CERT_DIR/ca.crt" -CAkey "$CERT_DIR/ca.key" \
    -CAcreateserial -out "$CERT_DIR/server.crt" -days $DAYS \
    -extfile "$CERT_DIR/server.ext" 2>/dev/null

# Generate Client Certificate
print_info "Generating client certificate..."
openssl genrsa -out "$CERT_DIR/client.key" $KEY_SIZE 2>/dev/null
openssl req -new -key "$CERT_DIR/client.key" -out "$CERT_DIR/client.csr" \
    -subj "/C=CN/ST=Beijing/L=Beijing/O=CyberBastion/OU=Dev/CN=cyber-bastion-client" 2>/dev/null

cat > "$CERT_DIR/client.ext" << EOF
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature, keyEncipherment
extendedKeyUsage = clientAuth
EOF

openssl x509 -req -in "$CERT_DIR/client.csr" -CA "$CERT_DIR/ca.crt" -CAkey "$CERT_DIR/ca.key" \
    -CAcreateserial -out "$CERT_DIR/client.crt" -days $DAYS \
    -extfile "$CERT_DIR/client.ext" 2>/dev/null

# Cleanup
rm -f "$CERT_DIR"/*.csr "$CERT_DIR"/*.ext "$CERT_DIR"/*.srl

# Set permissions
chmod 600 "$CERT_DIR"/*.key
chmod 644 "$CERT_DIR"/*.crt

print_success "Certificates generated in $CERT_DIR/"
print_info "Files created:"
ls -la "$CERT_DIR"

print_warning "These are self-signed certificates for development only!"
print_info "To enable TLS, set 'enable_tls: true' in your config files"
