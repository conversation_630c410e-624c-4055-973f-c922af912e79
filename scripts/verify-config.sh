#!/bin/bash

# 配置验证脚本
# 验证单服务器环境配置是否正确

set -e

echo "🔍 Cyber Bastion 配置验证脚本"
echo "服务器IP: *************"
echo "================================"

# 检查是否在项目根目录
if [ ! -f "go.mod" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 验证配置文件
echo "📋 验证配置文件..."

# 检查客户端配置
if [ -f "configs/client.yaml" ]; then
    echo "✅ 客户端配置文件存在"
    
    # 检查服务器IP配置
    if grep -q "*************" configs/client.yaml; then
        echo "✅ 服务器IP配置正确"
    else
        echo "⚠️  警告: 客户端配置中未找到正确的服务器IP"
    fi
    
    # 检查端口配置
    if grep -q "server_port: 8080" configs/client.yaml; then
        echo "✅ 主端口配置正确"
    else
        echo "⚠️  警告: 主端口配置可能不正确"
    fi

    # 检查加密配置
    if grep -q "enable_encryption: true" configs/client.yaml; then
        echo "✅ 客户端加密已启用"
    else
        echo "⚠️  警告: 客户端加密未启用"
    fi
else
    echo "❌ 客户端配置文件不存在: configs/client.yaml"
fi

# 检查服务端配置
if [ -f "configs/server.yaml" ]; then
    echo "✅ 服务端配置文件存在"
    
    # 检查监听配置
    if grep -q "host: \"0.0.0.0\"" configs/server.yaml; then
        echo "✅ 服务端监听配置正确"
    else
        echo "⚠️  警告: 服务端监听配置可能不正确"
    fi

    # 检查加密配置
    if grep -q "enable_encryption: true" configs/server.yaml; then
        echo "✅ 服务端加密已启用"
    else
        echo "⚠️  警告: 服务端加密未启用"
    fi
else
    echo "❌ 服务端配置文件不存在: configs/server.yaml"
fi

# 检查加密密钥一致性
echo ""
echo "🔐 检查加密密钥一致性..."

if [ -f "configs/client.yaml" ] && [ -f "configs/server.yaml" ]; then
    CLIENT_KEY=$(grep "encryption_key:" configs/client.yaml | head -1 | cut -d'"' -f2)
    SERVER_KEY=$(grep "encryption_key:" configs/server.yaml | head -1 | cut -d'"' -f2)

    if [ "$CLIENT_KEY" = "$SERVER_KEY" ] && [ -n "$CLIENT_KEY" ]; then
        echo "✅ 客户端和服务端加密密钥一致"
        if [ "$CLIENT_KEY" = "your-encryption-key-change-in-production" ]; then
            echo "⚠️  警告: 使用默认加密密钥，请更改为随机密钥"
        fi
    else
        echo "❌ 客户端和服务端加密密钥不一致或为空"
        echo "   客户端密钥: $CLIENT_KEY"
        echo "   服务端密钥: $SERVER_KEY"
    fi
else
    echo "⚠️  无法检查加密密钥一致性：配置文件缺失"
fi

# 验证证书文件
echo ""
echo "🔐 验证证书文件..."

cert_files=("certs/ca.crt" "certs/server.crt" "certs/server.key" "certs/client.crt" "certs/client.key")
all_certs_exist=true

for cert_file in "${cert_files[@]}"; do
    if [ -f "$cert_file" ]; then
        echo "✅ $cert_file 存在"
    else
        echo "❌ $cert_file 不存在"
        all_certs_exist=false
    fi
done

if [ "$all_certs_exist" = true ]; then
    echo "✅ 所有证书文件都存在"
    
    # 验证证书有效性
    echo ""
    echo "🔍 验证证书有效性..."

    # 检查服务器证书
    if openssl x509 -in certs/server.crt -text -noout | grep -q "*************"; then
        echo "✅ 服务器证书包含正确的IP地址"
    else
        echo "❌ 关键问题: 服务器证书不包含IP *************"
        echo "   这会导致TLS连接失败: x509: certificate is valid for 127.0.0.1, ::1, not *************"
        echo "   解决方案: 运行 ./scripts/verify-certificates.sh 检查详情"
    fi

    # 检查证书有效期
    if openssl x509 -in certs/server.crt -checkend 86400 > /dev/null; then
        echo "✅ 服务器证书在有效期内"
    else
        echo "❌ 服务器证书已过期或即将过期"
    fi

    # 检查证书链
    if openssl verify -CAfile certs/ca.crt certs/server.crt > /dev/null 2>&1; then
        echo "✅ 服务器证书链验证成功"
    else
        echo "❌ 服务器证书链验证失败"
    fi
else
    echo "❌ 部分证书文件缺失，请运行 ./scripts/setup-single-server.sh 生成证书"
fi

# 验证编译文件
echo ""
echo "🔨 验证编译文件..."

if [ -f "bin/cyber-bastion-server" ]; then
    echo "✅ 服务端可执行文件存在"
else
    echo "❌ 服务端可执行文件不存在: bin/cyber-bastion-server"
fi

if [ -f "bin/cyber-bastion-client" ]; then
    echo "✅ 客户端可执行文件存在"
else
    echo "❌ 客户端可执行文件不存在: bin/cyber-bastion-client"
fi

# 验证启动脚本
echo ""
echo "📝 验证启动脚本..."

if [ -f "scripts/start-server.sh" ] && [ -x "scripts/start-server.sh" ]; then
    echo "✅ 服务端启动脚本存在且可执行"
else
    echo "❌ 服务端启动脚本不存在或不可执行"
fi

if [ -f "scripts/start-client.sh" ] && [ -x "scripts/start-client.sh" ]; then
    echo "✅ 客户端启动脚本存在且可执行"
else
    echo "❌ 客户端启动脚本不存在或不可执行"
fi

# 网络连通性测试
echo ""
echo "🌐 网络连通性测试..."

if ping -c 1 ************* > /dev/null 2>&1; then
    echo "✅ 服务器IP可达"
else
    echo "⚠️  警告: 无法ping通服务器IP *************"
fi

# 端口测试（如果nc可用）
if command -v nc > /dev/null; then
    echo "🔌 测试端口连通性..."
    
    if timeout 3 nc -z ************* 8080 2>/dev/null; then
        echo "✅ 端口 8080 可达"
    else
        echo "⚠️  警告: 端口 8080 不可达（服务可能未启动）"
    fi
    
    if timeout 3 nc -z ************* 8081 2>/dev/null; then
        echo "✅ 端口 8081 可达"
    else
        echo "⚠️  警告: 端口 8081 不可达（服务可能未启动）"
    fi
else
    echo "⚠️  nc命令不可用，跳过端口测试"
fi

# 配置语法验证
echo ""
echo "📝 配置语法验证..."

# 验证YAML语法（如果有yaml工具）
if command -v python3 > /dev/null; then
    echo "🐍 使用Python验证YAML语法..."
    
    if python3 -c "import yaml; yaml.safe_load(open('configs/client.yaml'))" 2>/dev/null; then
        echo "✅ 客户端配置YAML语法正确"
    else
        echo "❌ 客户端配置YAML语法错误"
    fi
    
    if python3 -c "import yaml; yaml.safe_load(open('configs/server.yaml'))" 2>/dev/null; then
        echo "✅ 服务端配置YAML语法正确"
    else
        echo "❌ 服务端配置YAML语法错误"
    fi
else
    echo "⚠️  Python3不可用，跳过YAML语法验证"
fi

# 总结
echo ""
echo "📊 验证总结"
echo "================================"
echo "配置文件: $([ -f "configs/client.yaml" ] && [ -f "configs/server.yaml" ] && echo "✅ 完整" || echo "❌ 不完整")"
echo "证书文件: $([ "$all_certs_exist" = true ] && echo "✅ 完整" || echo "❌ 不完整")"
echo "编译文件: $([ -f "bin/cyber-bastion-server" ] && [ -f "bin/cyber-bastion-client" ] && echo "✅ 完整" || echo "❌ 不完整")"
echo "启动脚本: $([ -x "scripts/start-server.sh" ] && [ -x "scripts/start-client.sh" ] && echo "✅ 完整" || echo "❌ 不完整")"
echo ""

if [ -f "configs/client.yaml" ] && [ -f "configs/server.yaml" ] && [ "$all_certs_exist" = true ] && [ -f "bin/cyber-bastion-server" ] && [ -f "bin/cyber-bastion-client" ]; then
    echo "🎉 验证完成！系统已准备就绪"
    echo ""
    echo "🚀 启动命令:"
    echo "服务端: ./scripts/start-server.sh"
    echo "客户端: ./scripts/start-client.sh"
else
    echo "⚠️  验证发现问题，请运行 ./scripts/setup-single-server.sh 进行配置"
fi

echo ""
echo "📚 更多信息请查看: configs/README.md"
