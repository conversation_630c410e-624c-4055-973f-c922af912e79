#!/bin/bash

# 🚀 透明代理集成测试脚本
# 验证统一透明代理架构的功能和性能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
SERVER_HOST="127.0.0.1"
SERVER_PORT="8080"
TEST_USERNAME="testuser"
TEST_PASSWORD="testpass"
SERVER_PID=""
CLIENT_PID=""

# 清理函数
cleanup() {
    log_info "Cleaning up test processes..."
    
    if [ ! -z "$SERVER_PID" ]; then
        kill $SERVER_PID 2>/dev/null || true
        wait $SERVER_PID 2>/dev/null || true
        log_info "Server process stopped"
    fi
    
    if [ ! -z "$CLIENT_PID" ]; then
        kill $CLIENT_PID 2>/dev/null || true
        wait $CLIENT_PID 2>/dev/null || true
        log_info "Client process stopped"
    fi
    
    # 清理临时文件
    rm -f /tmp/cyber-bastion-server.log
    rm -f /tmp/cyber-bastion-client.log
    rm -f /tmp/server-config.yaml
    rm -f /tmp/client-config.yaml
}

# 设置信号处理
trap cleanup EXIT INT TERM

# 检查二进制文件
check_binaries() {
    log_info "Checking binary files..."
    
    if [ ! -f "bin/server-$(uname -s | tr '[:upper:]' '[:lower:]')-$(uname -m)" ]; then
        log_error "Server binary not found. Please run 'make build-all' first."
        exit 1
    fi
    
    if [ ! -f "bin/client-$(uname -s | tr '[:upper:]' '[:lower:]')-$(uname -m)" ]; then
        log_error "Client binary not found. Please run 'make build-all' first."
        exit 1
    fi
    
    log_success "Binary files found"
}

# 创建测试配置
create_test_configs() {
    log_info "Creating test configurations..."
    
    # 服务器配置
    cat > /tmp/server-config.yaml << EOF
host: "${SERVER_HOST}"
port: ${SERVER_PORT}
log_level: "debug"
auth:
  username: "${TEST_USERNAME}"
  password: "${TEST_PASSWORD}"
security:
  allowed_ips:
    - "127.0.0.1"
    - "::1"
  rate_limit: 1000
encryption:
  enabled: false
EOF

    # 客户端配置
    cat > /tmp/client-config.yaml << EOF
server_host: "${SERVER_HOST}"
server_port: ${SERVER_PORT}
log_level: "debug"
connect_timeout: 10
auth:
  username: "${TEST_USERNAME}"
  password: "${TEST_PASSWORD}"
tun:
  enabled: false  # 在测试中禁用TUN接口
  name: "test-tun"
  ip: "********"
  netmask: "*************"
  mtu: 1500
encryption:
  enabled: false
EOF

    log_success "Test configurations created"
}

# 启动服务器
start_server() {
    log_info "Starting server..."
    
    local server_binary="bin/server-$(uname -s | tr '[:upper:]' '[:lower:]')-$(uname -m)"
    
    # 启动服务器并记录PID
    $server_binary --config /tmp/server-config.yaml > /tmp/cyber-bastion-server.log 2>&1 &
    SERVER_PID=$!
    
    # 等待服务器启动
    sleep 2
    
    # 检查服务器是否正在运行
    if ! kill -0 $SERVER_PID 2>/dev/null; then
        log_error "Failed to start server"
        cat /tmp/cyber-bastion-server.log
        exit 1
    fi
    
    log_success "Server started with PID: $SERVER_PID"
}

# 启动客户端
start_client() {
    log_info "Starting client..."
    
    local client_binary="bin/client-$(uname -s | tr '[:upper:]' '[:lower:]')-$(uname -m)"
    
    # 启动客户端并记录PID
    $client_binary --config /tmp/client-config.yaml > /tmp/cyber-bastion-client.log 2>&1 &
    CLIENT_PID=$!
    
    # 等待客户端连接
    sleep 3
    
    # 检查客户端是否正在运行
    if ! kill -0 $CLIENT_PID 2>/dev/null; then
        log_error "Failed to start client"
        cat /tmp/cyber-bastion-client.log
        exit 1
    fi
    
    log_success "Client started with PID: $CLIENT_PID"
}

# 验证连接
verify_connection() {
    log_info "Verifying client-server connection..."
    
    # 检查服务器日志中的连接信息
    if grep -q "Client connected" /tmp/cyber-bastion-server.log; then
        log_success "Client successfully connected to server"
    else
        log_warning "Connection verification inconclusive, checking logs..."
        log_info "Server log:"
        tail -10 /tmp/cyber-bastion-server.log
        log_info "Client log:"
        tail -10 /tmp/cyber-bastion-client.log
    fi
}

# 测试透明代理架构
test_transparent_proxy_architecture() {
    log_info "Testing transparent proxy architecture..."
    
    # 检查服务器日志中的透明代理初始化
    if grep -q "🚀.*[Tt]ransparent.*[Pp]roxy" /tmp/cyber-bastion-server.log; then
        log_success "Transparent proxy architecture initialized"
    else
        log_warning "Transparent proxy initialization not found in logs"
    fi
    
    # 检查统一转发逻辑
    if grep -q "unified transparent proxy" /tmp/cyber-bastion-server.log; then
        log_success "Unified transparent proxy forwarding active"
    else
        log_info "Unified forwarding logs not found (may be normal if no packets forwarded)"
    fi
}

# 运行单元测试
run_unit_tests() {
    log_info "Running unit tests..."
    
    # 运行透明代理核心模块测试
    if go test ./internal/server/transparent/... -v > /tmp/unit-test-results.log 2>&1; then
        log_success "Transparent proxy unit tests passed"
    else
        log_error "Transparent proxy unit tests failed"
        cat /tmp/unit-test-results.log
        return 1
    fi
    
    # 运行客户端测试
    if go test ./internal/client -run TestClientTunIntegration -v >> /tmp/unit-test-results.log 2>&1; then
        log_success "Client integration tests passed"
    else
        log_error "Client integration tests failed"
        tail -20 /tmp/unit-test-results.log
        return 1
    fi
}

# 性能基准测试
run_performance_test() {
    log_info "Running performance benchmarks..."
    
    # 运行基准测试
    if go test ./internal/server/transparent/... -bench=. -benchmem > /tmp/benchmark-results.log 2>&1; then
        log_success "Performance benchmarks completed"
        log_info "Benchmark results:"
        grep "Benchmark" /tmp/benchmark-results.log || log_info "No benchmark results found"
    else
        log_warning "Performance benchmarks failed or not available"
    fi
}

# 主测试流程
main() {
    log_info "🚀 Starting Cyber Bastion Transparent Proxy Integration Tests"
    log_info "=================================================="
    
    # 检查前置条件
    check_binaries
    
    # 创建测试配置
    create_test_configs
    
    # 启动服务器和客户端
    start_server
    start_client
    
    # 验证连接
    verify_connection
    
    # 测试透明代理架构
    test_transparent_proxy_architecture
    
    # 运行单元测试
    run_unit_tests
    
    # 运行性能测试
    run_performance_test
    
    log_info "=================================================="
    log_success "🎉 Integration tests completed successfully!"
    
    # 显示测试总结
    log_info "Test Summary:"
    log_info "- Server PID: $SERVER_PID"
    log_info "- Client PID: $CLIENT_PID"
    log_info "- Server log: /tmp/cyber-bastion-server.log"
    log_info "- Client log: /tmp/cyber-bastion-client.log"
    log_info "- Unit test results: /tmp/unit-test-results.log"
    log_info "- Benchmark results: /tmp/benchmark-results.log"
}

# 运行主函数
main "$@"
