#!/bin/bash

# TLS Testing Script for Cyber Bastion
# This script tests TLS functionality by starting server and client with TLS enabled

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
SERVER_PID=""
CLIENT_PID=""
TEST_DURATION=10

# Cleanup function
cleanup() {
    print_info "Cleaning up..."
    
    if [ -n "$SERVER_PID" ]; then
        kill $SERVER_PID 2>/dev/null || true
        wait $SERVER_PID 2>/dev/null || true
        print_info "Server stopped"
    fi
    
    if [ -n "$CLIENT_PID" ]; then
        kill $CLIENT_PID 2>/dev/null || true
        wait $CLIENT_PID 2>/dev/null || true
        print_info "Client stopped"
    fi
    
    # Restore original configs
    if [ -f configs/server.yaml.bak ]; then
        mv configs/server.yaml.bak configs/server.yaml
        print_info "Server config restored"
    fi
    
    if [ -f configs/client.yaml.bak ]; then
        mv configs/client.yaml.bak configs/client.yaml
        print_info "Client config restored"
    fi
}

# Set trap for cleanup
trap cleanup EXIT INT TERM

# Function to check if binaries exist
check_binaries() {
    if [ ! -f "bin/server" ]; then
        print_error "Server binary not found. Run 'make build' first."
        exit 1
    fi
    
    if [ ! -f "bin/client" ]; then
        print_error "Client binary not found. Run 'make build' first."
        exit 1
    fi
}

# Function to generate certificates if needed
ensure_certificates() {
    if [ ! -d "certs" ] || [ ! -f "certs/server.crt" ]; then
        print_info "Generating TLS certificates..."
        ./scripts/quick-certs.sh
    else
        print_info "Using existing certificates"
    fi
}

# Function to enable TLS in configs
enable_tls_configs() {
    print_info "Enabling TLS in configuration files..."
    
    # Backup original configs
    cp configs/server.yaml configs/server.yaml.bak
    cp configs/client.yaml configs/client.yaml.bak
    
    # Enable TLS
    sed -i.tmp 's/enable_tls: false/enable_tls: true/' configs/server.yaml
    sed -i.tmp 's/enable_tls: false/enable_tls: true/' configs/client.yaml
    
    # Remove temporary files
    rm -f configs/server.yaml.tmp configs/client.yaml.tmp
    
    print_success "TLS enabled in configuration files"
}

# Function to start server
start_server() {
    print_info "Starting server with TLS..."
    
    ./bin/server --config configs/server.yaml --log-level info > server.log 2>&1 &
    SERVER_PID=$!
    
    # Wait for server to start
    sleep 2
    
    if kill -0 $SERVER_PID 2>/dev/null; then
        print_success "Server started with PID $SERVER_PID"
    else
        print_error "Failed to start server"
        cat server.log
        exit 1
    fi
}

# Function to start client
start_client() {
    print_info "Starting client with TLS..."
    
    ./bin/client --config configs/client.yaml --log-level info > client.log 2>&1 &
    CLIENT_PID=$!
    
    # Wait for client to connect
    sleep 3
    
    if kill -0 $CLIENT_PID 2>/dev/null; then
        print_success "Client started with PID $CLIENT_PID"
    else
        print_error "Failed to start client"
        cat client.log
        exit 1
    fi
}

# Function to test TLS connection
test_tls_connection() {
    print_info "Testing TLS connection for $TEST_DURATION seconds..."
    
    # Let the connection run for a while
    sleep $TEST_DURATION
    
    # Check if both processes are still running
    if kill -0 $SERVER_PID 2>/dev/null && kill -0 $CLIENT_PID 2>/dev/null; then
        print_success "TLS connection test passed - both server and client are running"
        
        # Check logs for TLS-related messages
        if grep -q "TLS" server.log; then
            print_success "Server logs show TLS activity"
        fi
        
        if grep -q "TLS\|tls\|SSL\|ssl" client.log; then
            print_success "Client logs show TLS activity"
        fi
        
        return 0
    else
        print_error "TLS connection test failed - one or both processes stopped"
        
        print_info "Server log:"
        cat server.log
        
        print_info "Client log:"
        cat client.log
        
        return 1
    fi
}

# Function to verify certificates
verify_certificates() {
    print_info "Verifying certificates..."
    
    if ./scripts/verify-certs.sh > /dev/null 2>&1; then
        print_success "Certificate verification passed"
    else
        print_error "Certificate verification failed"
        ./scripts/verify-certs.sh
        exit 1
    fi
}

# Function to test certificate validation
test_certificate_validation() {
    print_info "Testing certificate validation with OpenSSL..."
    
    # Test server certificate
    if openssl verify -CAfile certs/ca.crt certs/server.crt > /dev/null 2>&1; then
        print_success "Server certificate validation passed"
    else
        print_error "Server certificate validation failed"
        return 1
    fi
    
    # Test client certificate
    if openssl verify -CAfile certs/ca.crt certs/client.crt > /dev/null 2>&1; then
        print_success "Client certificate validation passed"
    else
        print_error "Client certificate validation failed"
        return 1
    fi
}

# Main test function
main() {
    print_info "Starting Cyber Bastion TLS test..."
    echo
    
    # Check prerequisites
    check_binaries
    ensure_certificates
    verify_certificates
    test_certificate_validation
    
    # Enable TLS in configs
    enable_tls_configs
    
    # Start server and client
    start_server
    start_client
    
    # Test the connection
    if test_tls_connection; then
        print_success "TLS test completed successfully!"
        echo
        print_info "Test Summary:"
        echo "  ✅ Certificates generated and verified"
        echo "  ✅ Server started with TLS"
        echo "  ✅ Client connected with TLS"
        echo "  ✅ Connection maintained for $TEST_DURATION seconds"
        echo
        print_info "Log files:"
        echo "  - Server log: server.log"
        echo "  - Client log: client.log"
    else
        print_error "TLS test failed!"
        exit 1
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--duration)
            TEST_DURATION="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Test TLS functionality of Cyber Bastion."
            echo ""
            echo "OPTIONS:"
            echo "    -t, --duration SECONDS    Test duration in seconds (default: 10)"
            echo "    -h, --help               Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Run main function
main
