#!/bin/bash

# 🚀 透明转发功能测试脚本
# 测试优化后的透明代理转发实现

set -e

echo "🚀 透明转发功能测试开始..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Go环境
check_go_environment() {
    log_info "检查Go环境..."
    if ! command -v go &> /dev/null; then
        log_error "Go未安装或不在PATH中"
        exit 1
    fi
    
    GO_VERSION=$(go version | awk '{print $3}')
    log_success "Go版本: $GO_VERSION"
}

# 编译项目
build_project() {
    log_info "编译项目..."
    if make build-all > /dev/null 2>&1; then
        log_success "项目编译成功"
    else
        log_error "项目编译失败"
        exit 1
    fi
}

# 测试透明代理单元测试
test_transparent_proxy_units() {
    log_info "运行透明代理单元测试..."
    
    if go test ./internal/server/transparent/... -v > test_results.log 2>&1; then
        log_success "透明代理单元测试通过"
        
        # 显示测试统计
        TESTS_RUN=$(grep -c "=== RUN" test_results.log || echo "0")
        TESTS_PASSED=$(grep -c "--- PASS:" test_results.log || echo "0")
        
        log_info "测试统计: $TESTS_RUN 个测试运行, $TESTS_PASSED 个测试通过"
    else
        log_error "透明代理单元测试失败"
        cat test_results.log
        exit 1
    fi
}

# 测试数据包解析功能
test_packet_parsing() {
    log_info "测试数据包解析功能..."
    
    cat > test_packet_parsing.go << 'EOF'
package main

import (
    "fmt"
    "net"
    "cyber-bastion/internal/server/transparent"
)

func main() {
    // 创建测试配置
    config := &transparent.TransparentProxyConfig{
        MaxPacketSize: 9216,
        LogPacketInfo: true,
    }
    
    // 创建数据包解析器
    parser := transparent.NewPacketParser(config)
    
    // 测试IPv4 ICMP数据包解析
    icmpPacket := []byte{
        0x45, 0x00, 0x00, 0x1c, // IP头部
        0x00, 0x00, 0x40, 0x00,
        0x40, 0x01, 0x00, 0x00,
        0xc0, 0xa8, 0x01, 0x01, // 源IP: ***********
        0x08, 0x08, 0x08, 0x08, // 目标IP: *******
        0x08, 0x00, 0xf7, 0xfc, // ICMP头部
        0x00, 0x00, 0x00, 0x00,
    }
    
    packetInfo, err := parser.ParsePacket(icmpPacket)
    if err != nil {
        fmt.Printf("❌ ICMP数据包解析失败: %v\n", err)
        return
    }
    
    fmt.Printf("✅ ICMP数据包解析成功:\n")
    fmt.Printf("   源IP: %s\n", packetInfo.SrcIP.String())
    fmt.Printf("   目标IP: %s\n", packetInfo.DstIP.String())
    fmt.Printf("   协议: %d (ICMP)\n", packetInfo.Protocol)
    fmt.Printf("   数据包大小: %d 字节\n", packetInfo.PacketSize)
    
    // 测试IPv4 TCP数据包解析
    tcpPacket := []byte{
        0x45, 0x00, 0x00, 0x28, // IP头部
        0x00, 0x00, 0x40, 0x00,
        0x40, 0x06, 0x00, 0x00,
        0xc0, 0xa8, 0x01, 0x01, // 源IP: ***********
        0x24, 0x61, 0xe4, 0xc6, // 目标IP: *************
        0x04, 0xd2, 0x01, 0xbb, // TCP头部: 源端口1234, 目标端口443
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
        0x50, 0x02, 0x20, 0x00,
        0x00, 0x00, 0x00, 0x00,
    }
    
    packetInfo, err = parser.ParsePacket(tcpPacket)
    if err != nil {
        fmt.Printf("❌ TCP数据包解析失败: %v\n", err)
        return
    }
    
    fmt.Printf("✅ TCP数据包解析成功:\n")
    fmt.Printf("   源IP: %s\n", packetInfo.SrcIP.String())
    fmt.Printf("   目标IP: %s\n", packetInfo.DstIP.String())
    fmt.Printf("   协议: %d (TCP)\n", packetInfo.Protocol)
    fmt.Printf("   源端口: %d\n", packetInfo.SrcPort)
    fmt.Printf("   目标端口: %d\n", packetInfo.DstPort)
    fmt.Printf("   数据包大小: %d 字节\n", packetInfo.PacketSize)
}
EOF

    if go run test_packet_parsing.go > packet_test_results.log 2>&1; then
        log_success "数据包解析测试通过"
        cat packet_test_results.log
    else
        log_error "数据包解析测试失败"
        cat packet_test_results.log
    fi
    
    # 清理测试文件
    rm -f test_packet_parsing.go packet_test_results.log
}

# 测试地址映射功能
test_address_mapping() {
    log_info "测试地址映射功能..."
    
    cat > test_address_mapping.go << 'EOF'
package main

import (
    "fmt"
    "time"
    "cyber-bastion/internal/server/transparent"
)

func main() {
    // 创建地址映射
    mapping := transparent.NewAddressMapping()
    
    // 测试基本映射操作
    mapping.SetMapping("*************", "client-001", 300*time.Second)
    mapping.SetMapping("***********01", "client-002", 300*time.Second)
    
    // 测试获取映射
    if clientID, exists := mapping.GetMapping("*************"); exists {
        fmt.Printf("✅ 地址映射测试通过: ************* -> %s\n", clientID)
    } else {
        fmt.Printf("❌ 地址映射测试失败: 无法找到映射\n")
        return
    }
    
    // 测试统计信息
    stats := mapping.GetStats()
    fmt.Printf("✅ 地址映射统计:\n")
    for key, value := range stats {
        fmt.Printf("   %s: %v\n", key, value)
    }
}
EOF

    if go run test_address_mapping.go > mapping_test_results.log 2>&1; then
        log_success "地址映射测试通过"
        cat mapping_test_results.log
    else
        log_error "地址映射测试失败"
        cat mapping_test_results.log
    fi
    
    # 清理测试文件
    rm -f test_address_mapping.go mapping_test_results.log
}

# 主测试流程
main() {
    log_info "开始透明转发功能测试..."
    
    # 检查环境
    check_go_environment
    
    # 编译项目
    build_project
    
    # 运行各项测试
    test_transparent_proxy_units
    test_packet_parsing
    test_address_mapping
    
    # 清理测试文件
    rm -f test_results.log
    
    log_success "🎉 所有透明转发功能测试完成！"
    log_info "优化后的透明代理现在支持:"
    log_info "  ✅ 真正的数据包级别转发"
    log_info "  ✅ TCP/UDP/ICMP协议智能处理"
    log_info "  ✅ IPv6多播地址特殊处理"
    log_info "  ✅ 增强的错误处理和日志记录"
    log_info "  ✅ 更宽松的心跳超时机制"
    
    log_warning "建议下一步:"
    log_warning "  1. 部署新的二进制文件到服务器"
    log_warning "  2. 重启服务器和客户端"
    log_warning "  3. 测试实际的网络连接"
    log_warning "  4. 观察日志中的改进"
}

# 运行主函数
main "$@"
