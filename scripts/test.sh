#!/bin/bash

# Cyber Bastion Test Script
# This script demonstrates how to test the client-server communication

set -e

echo "=== Cyber Bastion Test Script ==="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if binaries exist
if [ ! -f "bin/server" ]; then
    print_error "Server binary not found. Please run 'make build' first."
    exit 1
fi

if [ ! -f "bin/client" ]; then
    print_error "Client binary not found. Please run 'make build' first."
    exit 1
fi

# Start server in background
print_status "Starting server..."
./bin/server --config configs/server.yaml --log-level info &
SERVER_PID=$!

# Wait for server to start
sleep 2

# Check if server is running
if ! kill -0 $SERVER_PID 2>/dev/null; then
    print_error "Server failed to start"
    exit 1
fi

print_status "Server started with PID: $SERVER_PID"

# Function to cleanup
cleanup() {
    print_status "Cleaning up..."
    if kill -0 $SERVER_PID 2>/dev/null; then
        kill $SERVER_PID
        wait $SERVER_PID 2>/dev/null || true
    fi
    print_status "Cleanup complete"
}

# Set trap to cleanup on exit
trap cleanup EXIT

# Test client connection
print_status "Testing client connection..."
timeout 10s ./bin/client --config configs/client.yaml --log-level info &
CLIENT_PID=$!

# Wait for client to run
sleep 5

# Check if client is still running
if kill -0 $CLIENT_PID 2>/dev/null; then
    print_status "Client is running successfully"
    kill $CLIENT_PID 2>/dev/null || true
else
    print_warning "Client has already exited (this is normal for test mode)"
fi

print_status "Basic connectivity test completed"

# Test interactive mode (optional)
if [ "$1" = "--interactive" ]; then
    print_status "Starting interactive client..."
    print_warning "You can now type messages. Type 'quit' to exit."
    ./bin/client --config configs/client.yaml --interactive
fi

print_status "Test completed successfully!"
