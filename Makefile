# Cyber Bastion Makefile

.PHONY: all build clean test run-server run-client certs certs-quick certs-encryption certs-verify certs-clean test-tls help

# Variables
BINARY_DIR=bin
SERVER_BINARY=$(BINARY_DIR)/server
CLIENT_BINARY=$(BINARY_DIR)/client
GO_FILES=$(shell find . -name "*.go" -type f)
# Disable VCS stamping to avoid Git status issues in CI/CD or incomplete repos
BUILD_FLAGS=-buildvcs=false

# Default target
all: build

# Build all binaries
build: $(SERVER_BINARY) $(CLIENT_BINARY)

# Build server binary
$(SERVER_BINARY): $(GO_FILES)
	@echo "Building server..."
	@mkdir -p $(BINARY_DIR)
	go build $(BUILD_FLAGS) -o $(SERVER_BINARY) ./cmd/server

# Build client binary
$(CLIENT_BINARY): $(GO_FILES)
	@echo "Building client..."
	@mkdir -p $(BINARY_DIR)
	go build $(BUILD_FLAGS) -o $(CLIENT_BINARY) ./cmd/client

# Clean build artifacts
clean:
	@echo "Cleaning..."
	rm -rf $(BINARY_DIR)
	go clean

# Run tests
test:
	@echo "Running tests..."
	go test -v ./...

# Run tests with coverage
test-coverage:
	@echo "Running tests with coverage..."
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# Show coverage summary
test-coverage-summary:
	@echo "Generating coverage summary..."
	go test -coverprofile=coverage.out ./... > /dev/null 2>&1
	go tool cover -func=coverage.out

# Format code
fmt:
	@echo "Formatting code..."
	go fmt ./...

# Lint code
lint:
	@echo "Linting code..."
	golangci-lint run

# Tidy dependencies
tidy:
	@echo "Tidying dependencies..."
	go mod tidy

# Run server with default config
run-server: $(SERVER_BINARY)
	@echo "Starting server..."
	./$(SERVER_BINARY) --config configs/server.yaml

# Run server in development mode
run-server-dev: $(SERVER_BINARY)
	@echo "Starting server in development mode..."
	./$(SERVER_BINARY) --config configs/server.yaml --log-level debug

# Run client with default config
run-client: $(CLIENT_BINARY)
	@echo "Starting client..."
	./$(CLIENT_BINARY) --config configs/client.yaml

# Run client in interactive mode
run-client-interactive: $(CLIENT_BINARY)
	@echo "Starting client in interactive mode..."
	./$(CLIENT_BINARY) --config configs/client.yaml --interactive

# Run client in development mode
run-client-dev: $(CLIENT_BINARY)
	@echo "Starting client in development mode..."
	./$(CLIENT_BINARY) --config configs/client.yaml --log-level debug

# Install dependencies
deps:
	@echo "Installing dependencies..."
	go mod download

# Build for multiple platforms
build-all: clean
	@echo "Building for multiple platforms..."
	@mkdir -p $(BINARY_DIR)
	GOOS=linux GOARCH=amd64 go build $(BUILD_FLAGS) -o $(BINARY_DIR)/server-linux-amd64 ./cmd/server
	GOOS=linux GOARCH=amd64 go build $(BUILD_FLAGS) -o $(BINARY_DIR)/client-linux-amd64 ./cmd/client
	GOOS=linux GOARCH=arm64 go build $(BUILD_FLAGS) -o $(BINARY_DIR)/server-linux-arm64 ./cmd/server
	GOOS=linux GOARCH=arm64 go build $(BUILD_FLAGS) -o $(BINARY_DIR)/client-linux-arm64 ./cmd/client
	GOOS=darwin GOARCH=amd64 go build $(BUILD_FLAGS) -o $(BINARY_DIR)/server-darwin-amd64 ./cmd/server
	GOOS=darwin GOARCH=amd64 go build $(BUILD_FLAGS) -o $(BINARY_DIR)/client-darwin-amd64 ./cmd/client
	GOOS=darwin GOARCH=arm64 go build $(BUILD_FLAGS) -o $(BINARY_DIR)/server-darwin-arm64 ./cmd/server
	GOOS=darwin GOARCH=arm64 go build $(BUILD_FLAGS) -o $(BINARY_DIR)/client-darwin-arm64 ./cmd/client
	GOOS=windows GOARCH=amd64 go build $(BUILD_FLAGS) -o $(BINARY_DIR)/server-windows-amd64.exe ./cmd/server
	GOOS=windows GOARCH=amd64 go build $(BUILD_FLAGS) -o $(BINARY_DIR)/client-windows-amd64.exe ./cmd/client

# Build with VCS info (requires clean Git repo)
build-with-vcs: clean
	@echo "Building with VCS information..."
	@mkdir -p $(BINARY_DIR)
	go build -o $(SERVER_BINARY) ./cmd/server
	go build -o $(CLIENT_BINARY) ./cmd/client

# Build all platforms with VCS info (requires clean Git repo)
build-all-with-vcs: clean
	@echo "Building for multiple platforms with VCS information..."
	@mkdir -p $(BINARY_DIR)
	GOOS=linux GOARCH=amd64 go build -o $(BINARY_DIR)/server-linux-amd64 ./cmd/server
	GOOS=linux GOARCH=amd64 go build -o $(BINARY_DIR)/client-linux-amd64 ./cmd/client
	GOOS=linux GOARCH=arm64 go build -o $(BINARY_DIR)/server-linux-arm64 ./cmd/server
	GOOS=linux GOARCH=arm64 go build -o $(BINARY_DIR)/client-linux-arm64 ./cmd/client
	GOOS=darwin GOARCH=amd64 go build -o $(BINARY_DIR)/server-darwin-amd64 ./cmd/server
	GOOS=darwin GOARCH=amd64 go build -o $(BINARY_DIR)/client-darwin-amd64 ./cmd/client
	GOOS=darwin GOARCH=arm64 go build -o $(BINARY_DIR)/server-darwin-arm64 ./cmd/server
	GOOS=darwin GOARCH=arm64 go build -o $(BINARY_DIR)/client-darwin-arm64 ./cmd/client
	GOOS=windows GOARCH=amd64 go build -o $(BINARY_DIR)/server-windows-amd64.exe ./cmd/server
	GOOS=windows GOARCH=amd64 go build -o $(BINARY_DIR)/client-windows-amd64.exe ./cmd/client

# Build for specific platforms
build-linux: clean
	@echo "Building for Linux platforms..."
	@mkdir -p $(BINARY_DIR)
	GOOS=linux GOARCH=amd64 go build $(BUILD_FLAGS) -o $(BINARY_DIR)/server-linux-amd64 ./cmd/server
	GOOS=linux GOARCH=amd64 go build $(BUILD_FLAGS) -o $(BINARY_DIR)/client-linux-amd64 ./cmd/client
	GOOS=linux GOARCH=arm64 go build $(BUILD_FLAGS) -o $(BINARY_DIR)/server-linux-arm64 ./cmd/server
	GOOS=linux GOARCH=arm64 go build $(BUILD_FLAGS) -o $(BINARY_DIR)/client-linux-arm64 ./cmd/client

build-darwin: clean
	@echo "Building for macOS platforms..."
	@mkdir -p $(BINARY_DIR)
	GOOS=darwin GOARCH=amd64 go build $(BUILD_FLAGS) -o $(BINARY_DIR)/server-darwin-amd64 ./cmd/server
	GOOS=darwin GOARCH=amd64 go build $(BUILD_FLAGS) -o $(BINARY_DIR)/client-darwin-amd64 ./cmd/client
	GOOS=darwin GOARCH=arm64 go build $(BUILD_FLAGS) -o $(BINARY_DIR)/server-darwin-arm64 ./cmd/server
	GOOS=darwin GOARCH=arm64 go build $(BUILD_FLAGS) -o $(BINARY_DIR)/client-darwin-arm64 ./cmd/client

build-windows: clean
	@echo "Building for Windows platforms..."
	@mkdir -p $(BINARY_DIR)
	GOOS=windows GOARCH=amd64 go build $(BUILD_FLAGS) -o $(BINARY_DIR)/server-windows-amd64.exe ./cmd/server
	GOOS=windows GOARCH=amd64 go build $(BUILD_FLAGS) -o $(BINARY_DIR)/client-windows-amd64.exe ./cmd/client

# Docker build
docker-build:
	@echo "Building Docker images..."
	docker build -t cyber-bastion-server -f docker/Dockerfile.server .
	docker build -t cyber-bastion-client -f docker/Dockerfile.client .

# Diagnose Git build issues
diagnose-git:
	@echo "Running Git build diagnosis..."
	./scripts/fix-git-build.sh

# Show build information
show-build-info:
	@echo "Showing build information..."
	./scripts/show-build-info.sh

# Package release archives
package-release:
	@echo "Packaging release archives..."
	./scripts/package-release.sh $(VERSION)

# Build and package release (complete release workflow)
release: build-all package-release
	@echo "Release build complete!"

# Certificate management
certs:
	@echo "Generating TLS certificates..."
	./scripts/generate-certs.sh

# Quick certificate generation for development
certs-quick:
	@echo "Generating quick TLS certificates for development..."
	./scripts/quick-certs.sh

# Generate encryption-only certificates (supports any IP)
certs-encryption:
	@echo "Generating encryption-only TLS certificates..."
	./scripts/generate-encryption-certs.sh

# Verify existing certificates
certs-verify:
	@echo "Verifying TLS certificates..."
	./scripts/verify-certs.sh

# Clean certificates
certs-clean:
	@echo "Cleaning certificates..."
	rm -rf certs/

# Generate certificates and enable TLS in configs
certs-enable: certs-quick
	@echo "Enabling TLS in configuration files..."
	@if [ -f configs/server.yaml ]; then \
		sed -i.bak 's/enable_tls: false/enable_tls: true/' configs/server.yaml && \
		echo "TLS enabled in configs/server.yaml"; \
	fi
	@if [ -f configs/client.yaml ]; then \
		sed -i.bak 's/enable_tls: false/enable_tls: true/' configs/client.yaml && \
		echo "TLS enabled in configs/client.yaml"; \
	fi
	@echo "TLS certificates generated and enabled!"

# Generate encryption-only certificates and configure for any IP
certs-enable-encryption: certs-encryption
	@echo "Enabling encryption-only TLS in configuration files..."
	@if [ -f configs/server.yaml ]; then \
		sed -i.bak 's/enable_tls: false/enable_tls: true/' configs/server.yaml && \
		echo "TLS enabled in configs/server.yaml"; \
	fi
	@if [ -f configs/client.yaml ]; then \
		sed -i.bak -e 's/enable_tls: false/enable_tls: true/' -e 's/tls_skip_verify: false/tls_skip_verify: true/' configs/client.yaml && \
		echo "TLS enabled with skip_verify=true in configs/client.yaml"; \
	fi
	@echo "Encryption-only TLS certificates generated and configured!"
	@echo "Client can now connect to any IP address with TLS encryption."

# Run server with TLS enabled
run-server-tls: $(SERVER_BINARY) certs-quick
	@echo "Starting server with TLS..."
	@sed 's/enable_tls: false/enable_tls: true/' configs/server.yaml > configs/server-tls.yaml
	./$(SERVER_BINARY) --config configs/server-tls.yaml

# Run client with TLS enabled
run-client-tls: $(CLIENT_BINARY) certs-quick
	@echo "Starting client with TLS..."
	@sed 's/enable_tls: false/enable_tls: true/' configs/client.yaml > configs/client-tls.yaml
	./$(CLIENT_BINARY) --config configs/client-tls.yaml

# Test TLS functionality
test-tls: build
	@echo "Testing TLS functionality..."
	./scripts/test-tls.sh

# Help
help:
	@echo "Available targets:"
	@echo "  all              - Build all binaries (default)"
	@echo "  build            - Build all binaries"
	@echo "  clean            - Clean build artifacts"
	@echo "  test             - Run tests"
	@echo "  test-coverage    - Run tests with coverage"
	@echo "  test-coverage-summary - Show coverage summary"
	@echo "  fmt              - Format code"
	@echo "  lint             - Lint code"
	@echo "  tidy             - Tidy dependencies"
	@echo "  run-server       - Run server with default config"
	@echo "  run-server-dev   - Run server in development mode"
	@echo "  run-client       - Run client with default config"
	@echo "  run-client-interactive - Run client in interactive mode"
	@echo "  run-client-dev   - Run client in development mode"
	@echo "  deps             - Install dependencies"
	@echo "  build-all        - Build for all platforms (no VCS info)"
	@echo "  build-linux      - Build for Linux platforms (amd64, arm64)"
	@echo "  build-darwin     - Build for macOS platforms (amd64, arm64)"
	@echo "  build-windows    - Build for Windows platforms (amd64)"
	@echo "  build-with-vcs   - Build with VCS information (requires clean Git repo)"
	@echo "  build-all-with-vcs - Build all platforms with VCS info (requires clean Git repo)"
	@echo "  docker-build     - Build Docker images"
	@echo "  diagnose-git     - Diagnose Git-related build issues"
	@echo "  show-build-info  - Show information about built binaries"
	@echo "  package-release  - Package release archives (VERSION=x.x.x)"
	@echo "  release          - Complete release workflow (build + package)"
	@echo "  certs            - Generate TLS certificates with full options"
	@echo "  certs-quick      - Generate TLS certificates quickly for development"
	@echo "  certs-encryption - Generate encryption-only certificates (supports any IP)"
	@echo "  certs-verify     - Verify existing TLS certificates"
	@echo "  certs-clean      - Clean/remove all certificates"
	@echo "  certs-enable     - Generate certificates and enable TLS in configs"
	@echo "  certs-enable-encryption - Generate encryption-only certs (supports any IP)"
	@echo "  run-server-tls   - Run server with TLS enabled"
	@echo "  run-client-tls   - Run client with TLS enabled"
	@echo "  test-tls         - Test TLS functionality"
	@echo "  help             - Show this help message"
