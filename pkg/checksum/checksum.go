// Package checksum provides optimized checksum calculation functions for network protocols
package checksum

import (
	"net"
)

// CalculateIPv4Checksum 计算IPv4头部校验和
// 使用标准的Internet校验和算法
func CalculateIPv4Checksum(header []byte) uint16 {
	if len(header) < 20 || len(header)%2 != 0 {
		// IPv4头部必须是20字节的倍数，且长度为偶数
		return 0
	}

	var sum uint32

	// 将头部按16位分组求和
	for i := 0; i < len(header); i += 2 {
		sum += uint32(header[i])<<8 + uint32(header[i+1])
	}

	// 处理进位
	for sum>>16 != 0 {
		sum = (sum & 0xFFFF) + (sum >> 16)
	}

	// 取反
	return uint16(^sum)
}

// CalculateICMPChecksum 计算ICMP校验和
func CalculateICMPChecksum(icmpData []byte) uint16 {
	if len(icmpData) < 4 {
		return 0
	}

	var sum uint32

	// 将ICMP数据按16位分组求和
	for i := 0; i < len(icmpData); i += 2 {
		if i+1 < len(icmpData) {
			sum += uint32(icmpData[i])<<8 + uint32(icmpData[i+1])
		} else {
			sum += uint32(icmpData[i]) << 8
		}
	}

	// 处理进位
	for sum>>16 != 0 {
		sum = (sum & 0xFFFF) + (sum >> 16)
	}

	// 取反
	return uint16(^sum)
}

// CalculateUDPChecksum 计算UDP校验和（包含IPv4伪头部）
func CalculateUDPChecksum(srcIP, dstIP net.IP, udpData []byte) uint16 {
	if len(udpData) < 8 {
		return 0
	}

	srcIPv4 := srcIP.To4()
	dstIPv4 := dstIP.To4()
	if srcIPv4 == nil || dstIPv4 == nil {
		return 0
	}

	var sum uint32

	// IPv4伪头部
	// 源IP
	sum += uint32(srcIPv4[0])<<8 + uint32(srcIPv4[1])
	sum += uint32(srcIPv4[2])<<8 + uint32(srcIPv4[3])
	// 目标IP
	sum += uint32(dstIPv4[0])<<8 + uint32(dstIPv4[1])
	sum += uint32(dstIPv4[2])<<8 + uint32(dstIPv4[3])
	// 协议号 (17 for UDP)
	sum += 17
	// UDP长度
	sum += uint32(len(udpData))

	// UDP数据
	for i := 0; i < len(udpData); i += 2 {
		if i+1 < len(udpData) {
			sum += uint32(udpData[i])<<8 + uint32(udpData[i+1])
		} else {
			sum += uint32(udpData[i]) << 8
		}
	}

	// 处理进位
	for sum>>16 != 0 {
		sum = (sum & 0xFFFF) + (sum >> 16)
	}

	// 取反
	return uint16(^sum)
}

// CalculateTCPChecksum 计算TCP校验和（包含IPv4伪头部）
func CalculateTCPChecksum(srcIP, dstIP net.IP, tcpData []byte) uint16 {
	if len(tcpData) < 20 {
		return 0
	}

	srcIPv4 := srcIP.To4()
	dstIPv4 := dstIP.To4()
	if srcIPv4 == nil || dstIPv4 == nil {
		return 0
	}

	var sum uint32

	// IPv4伪头部
	// 源IP
	sum += uint32(srcIPv4[0])<<8 + uint32(srcIPv4[1])
	sum += uint32(srcIPv4[2])<<8 + uint32(srcIPv4[3])
	// 目标IP
	sum += uint32(dstIPv4[0])<<8 + uint32(dstIPv4[1])
	sum += uint32(dstIPv4[2])<<8 + uint32(dstIPv4[3])
	// 协议号 (6 for TCP)
	sum += 6
	// TCP长度
	sum += uint32(len(tcpData))

	// TCP数据
	for i := 0; i < len(tcpData); i += 2 {
		if i+1 < len(tcpData) {
			sum += uint32(tcpData[i])<<8 + uint32(tcpData[i+1])
		} else {
			sum += uint32(tcpData[i]) << 8
		}
	}

	// 处理进位
	for sum>>16 != 0 {
		sum = (sum & 0xFFFF) + (sum >> 16)
	}

	// 取反
	return uint16(^sum)
}

// CalculateUDPv6Checksum 计算UDP over IPv6校验和（包含IPv6伪头部）
func CalculateUDPv6Checksum(srcIP, dstIP net.IP, udpData []byte) uint16 {
	if len(udpData) < 8 {
		return 0
	}

	srcIPv6 := srcIP.To16()
	dstIPv6 := dstIP.To16()
	if srcIPv6 == nil || dstIPv6 == nil {
		return 0
	}

	var sum uint32

	// IPv6伪头部
	// 源IP (16字节)
	for i := 0; i < 16; i += 2 {
		sum += uint32(srcIPv6[i])<<8 + uint32(srcIPv6[i+1])
	}
	// 目标IP (16字节)
	for i := 0; i < 16; i += 2 {
		sum += uint32(dstIPv6[i])<<8 + uint32(dstIPv6[i+1])
	}
	// 上层协议长度
	sum += uint32(len(udpData))
	// 下一个头部 (17 for UDP)
	sum += 17

	// UDP数据
	for i := 0; i < len(udpData); i += 2 {
		if i+1 < len(udpData) {
			sum += uint32(udpData[i])<<8 + uint32(udpData[i+1])
		} else {
			sum += uint32(udpData[i]) << 8
		}
	}

	// 处理进位
	for sum>>16 != 0 {
		sum = (sum & 0xFFFF) + (sum >> 16)
	}

	// 取反
	return uint16(^sum)
}

// CalculateTCPv6Checksum 计算TCP over IPv6校验和（包含IPv6伪头部）
func CalculateTCPv6Checksum(srcIP, dstIP net.IP, tcpData []byte) uint16 {
	if len(tcpData) < 20 {
		return 0
	}

	srcIPv6 := srcIP.To16()
	dstIPv6 := dstIP.To16()
	if srcIPv6 == nil || dstIPv6 == nil {
		return 0
	}

	var sum uint32

	// IPv6伪头部
	// 源IP (16字节)
	for i := 0; i < 16; i += 2 {
		sum += uint32(srcIPv6[i])<<8 + uint32(srcIPv6[i+1])
	}
	// 目标IP (16字节)
	for i := 0; i < 16; i += 2 {
		sum += uint32(dstIPv6[i])<<8 + uint32(dstIPv6[i+1])
	}
	// 上层协议长度
	sum += uint32(len(tcpData))
	// 下一个头部 (6 for TCP)
	sum += 6

	// TCP数据
	for i := 0; i < len(tcpData); i += 2 {
		if i+1 < len(tcpData) {
			sum += uint32(tcpData[i])<<8 + uint32(tcpData[i+1])
		} else {
			sum += uint32(tcpData[i]) << 8
		}
	}

	// 处理进位
	for sum>>16 != 0 {
		sum = (sum & 0xFFFF) + (sum >> 16)
	}

	// 取反
	return uint16(^sum)
}

// CalculateICMPv6Checksum 计算ICMPv6校验和（包含IPv6伪头部）
func CalculateICMPv6Checksum(srcIP, dstIP net.IP, icmpData []byte) uint16 {
	if len(icmpData) < 4 {
		return 0
	}

	srcIPv6 := srcIP.To16()
	dstIPv6 := dstIP.To16()
	if srcIPv6 == nil || dstIPv6 == nil {
		return 0
	}

	var sum uint32

	// IPv6伪头部
	// 源IP (16字节)
	for i := 0; i < 16; i += 2 {
		sum += uint32(srcIPv6[i])<<8 + uint32(srcIPv6[i+1])
	}
	// 目标IP (16字节)
	for i := 0; i < 16; i += 2 {
		sum += uint32(dstIPv6[i])<<8 + uint32(dstIPv6[i+1])
	}
	// 上层协议长度
	sum += uint32(len(icmpData))
	// 下一个头部 (58 for ICMPv6)
	sum += 58

	// ICMPv6数据
	for i := 0; i < len(icmpData); i += 2 {
		if i+1 < len(icmpData) {
			sum += uint32(icmpData[i])<<8 + uint32(icmpData[i+1])
		} else {
			sum += uint32(icmpData[i]) << 8
		}
	}

	// 处理进位
	for sum>>16 != 0 {
		sum = (sum & 0xFFFF) + (sum >> 16)
	}

	// 取反
	return uint16(^sum)
}

// ValidateChecksum 验证数据包校验和是否正确
func ValidateChecksum(data []byte, expectedChecksum uint16, checksumOffset int) bool {
	if len(data) < checksumOffset+2 {
		return false
	}

	// 临时清零校验和字段
	originalChecksum := uint16(data[checksumOffset])<<8 | uint16(data[checksumOffset+1])
	data[checksumOffset] = 0
	data[checksumOffset+1] = 0

	// 计算校验和
	var sum uint32
	for i := 0; i < len(data); i += 2 {
		if i+1 < len(data) {
			sum += uint32(data[i])<<8 + uint32(data[i+1])
		} else {
			sum += uint32(data[i]) << 8
		}
	}

	// 处理进位
	for sum>>16 != 0 {
		sum = (sum & 0xFFFF) + (sum >> 16)
	}

	calculatedChecksum := uint16(^sum)

	// 恢复原始校验和
	data[checksumOffset] = byte(originalChecksum >> 8)
	data[checksumOffset+1] = byte(originalChecksum & 0xFF)

	return calculatedChecksum == expectedChecksum
}
