package checksum

import (
	"net"
	"testing"
)

func TestCalculateIPv4Checksum(t *testing.T) {
	// 测试标准IPv4头部校验和计算
	// 这是一个真实的IPv4头部示例
	header := []byte{
		0x45, 0x00, // 版本和头部长度，服务类型
		0x00, 0x3c, // 总长度
		0x1c, 0x46, // 标识
		0x40, 0x00, // 标志和片偏移
		0x40, 0x06, // TTL和协议
		0x00, 0x00, // 校验和（设为0）
		0xac, 0x10, 0x0a, 0x63, // 源IP
		0xac, 0x10, 0x0a, 0x0c, // 目标IP
	}

	checksum := CalculateIPv4Checksum(header)
	
	// 验证校验和不为0
	if checksum == 0 {
		t.Error("IPv4 checksum should not be 0")
	}

	// 设置计算出的校验和
	header[10] = byte(checksum >> 8)
	header[11] = byte(checksum & 0xFF)

	// 重新计算应该得到0（因为包含了正确的校验和）
	verifyChecksum := CalculateIPv4Checksum(header)
	if verifyChecksum != 0 {
		t.<PERSON>rro<PERSON>("IPv4 checksum verification failed, expected 0, got %d", verifyChecksum)
	}
}

func TestCalculateICMPChecksum(t *testing.T) {
	// 测试ICMP Echo Request
	icmpData := []byte{
		0x08, 0x00, // 类型和代码
		0x00, 0x00, // 校验和（设为0）
		0x00, 0x00, // 标识符
		0x00, 0x01, // 序列号
		0x61, 0x62, 0x63, 0x64, // 数据 "abcd"
	}

	checksum := CalculateICMPChecksum(icmpData)
	
	if checksum == 0 {
		t.Error("ICMP checksum should not be 0")
	}

	// 设置校验和并验证
	icmpData[2] = byte(checksum >> 8)
	icmpData[3] = byte(checksum & 0xFF)

	verifyChecksum := CalculateICMPChecksum(icmpData)
	if verifyChecksum != 0 {
		t.Errorf("ICMP checksum verification failed, expected 0, got %d", verifyChecksum)
	}
}

func TestCalculateUDPChecksum(t *testing.T) {
	srcIP := net.ParseIP("***********")
	dstIP := net.ParseIP("***********")
	
	// UDP头部 + 数据
	udpData := []byte{
		0x04, 0xd2, // 源端口 1234
		0x00, 0x35, // 目标端口 53 (DNS)
		0x00, 0x0c, // 长度 12
		0x00, 0x00, // 校验和（设为0）
		0x74, 0x65, 0x73, 0x74, // 数据 "test"
	}

	checksum := CalculateUDPChecksum(srcIP, dstIP, udpData)
	
	if checksum == 0 {
		t.Error("UDP checksum should not be 0")
	}

	// 设置校验和并验证
	udpData[6] = byte(checksum >> 8)
	udpData[7] = byte(checksum & 0xFF)

	verifyChecksum := CalculateUDPChecksum(srcIP, dstIP, udpData)
	if verifyChecksum != 0 {
		t.Errorf("UDP checksum verification failed, expected 0, got %d", verifyChecksum)
	}
}

func TestCalculateTCPChecksum(t *testing.T) {
	srcIP := net.ParseIP("***********")
	dstIP := net.ParseIP("***********")
	
	// 最小TCP头部
	tcpData := []byte{
		0x04, 0xd2, // 源端口 1234
		0x00, 0x50, // 目标端口 80 (HTTP)
		0x00, 0x00, 0x00, 0x01, // 序列号
		0x00, 0x00, 0x00, 0x00, // 确认号
		0x50, 0x02, // 头部长度和标志
		0x20, 0x00, // 窗口大小
		0x00, 0x00, // 校验和（设为0）
		0x00, 0x00, // 紧急指针
	}

	checksum := CalculateTCPChecksum(srcIP, dstIP, tcpData)
	
	if checksum == 0 {
		t.Error("TCP checksum should not be 0")
	}

	// 设置校验和并验证
	tcpData[16] = byte(checksum >> 8)
	tcpData[17] = byte(checksum & 0xFF)

	verifyChecksum := CalculateTCPChecksum(srcIP, dstIP, tcpData)
	if verifyChecksum != 0 {
		t.Errorf("TCP checksum verification failed, expected 0, got %d", verifyChecksum)
	}
}

func TestCalculateUDPv6Checksum(t *testing.T) {
	srcIP := net.ParseIP("2001:db8::1")
	dstIP := net.ParseIP("2001:db8::2")
	
	// UDP头部 + 数据
	udpData := []byte{
		0x04, 0xd2, // 源端口 1234
		0x00, 0x35, // 目标端口 53 (DNS)
		0x00, 0x0c, // 长度 12
		0x00, 0x00, // 校验和（设为0）
		0x74, 0x65, 0x73, 0x74, // 数据 "test"
	}

	checksum := CalculateUDPv6Checksum(srcIP, dstIP, udpData)
	
	if checksum == 0 {
		t.Error("UDPv6 checksum should not be 0")
	}

	// 设置校验和并验证
	udpData[6] = byte(checksum >> 8)
	udpData[7] = byte(checksum & 0xFF)

	verifyChecksum := CalculateUDPv6Checksum(srcIP, dstIP, udpData)
	if verifyChecksum != 0 {
		t.Errorf("UDPv6 checksum verification failed, expected 0, got %d", verifyChecksum)
	}
}

func TestCalculateTCPv6Checksum(t *testing.T) {
	srcIP := net.ParseIP("2001:db8::1")
	dstIP := net.ParseIP("2001:db8::2")
	
	// 最小TCP头部
	tcpData := []byte{
		0x04, 0xd2, // 源端口 1234
		0x00, 0x50, // 目标端口 80 (HTTP)
		0x00, 0x00, 0x00, 0x01, // 序列号
		0x00, 0x00, 0x00, 0x00, // 确认号
		0x50, 0x02, // 头部长度和标志
		0x20, 0x00, // 窗口大小
		0x00, 0x00, // 校验和（设为0）
		0x00, 0x00, // 紧急指针
	}

	checksum := CalculateTCPv6Checksum(srcIP, dstIP, tcpData)
	
	if checksum == 0 {
		t.Error("TCPv6 checksum should not be 0")
	}

	// 设置校验和并验证
	tcpData[16] = byte(checksum >> 8)
	tcpData[17] = byte(checksum & 0xFF)

	verifyChecksum := CalculateTCPv6Checksum(srcIP, dstIP, tcpData)
	if verifyChecksum != 0 {
		t.Errorf("TCPv6 checksum verification failed, expected 0, got %d", verifyChecksum)
	}
}

func TestCalculateICMPv6Checksum(t *testing.T) {
	srcIP := net.ParseIP("2001:db8::1")
	dstIP := net.ParseIP("2001:db8::2")
	
	// ICMPv6 Echo Request
	icmpData := []byte{
		0x80, 0x00, // 类型和代码
		0x00, 0x00, // 校验和（设为0）
		0x00, 0x00, // 标识符
		0x00, 0x01, // 序列号
		0x61, 0x62, 0x63, 0x64, // 数据 "abcd"
	}

	checksum := CalculateICMPv6Checksum(srcIP, dstIP, icmpData)
	
	if checksum == 0 {
		t.Error("ICMPv6 checksum should not be 0")
	}

	// 设置校验和并验证
	icmpData[2] = byte(checksum >> 8)
	icmpData[3] = byte(checksum & 0xFF)

	verifyChecksum := CalculateICMPv6Checksum(srcIP, dstIP, icmpData)
	if verifyChecksum != 0 {
		t.Errorf("ICMPv6 checksum verification failed, expected 0, got %d", verifyChecksum)
	}
}

func TestValidateChecksum(t *testing.T) {
	// 测试校验和验证功能
	data := []byte{0x45, 0x00, 0x00, 0x3c, 0x1c, 0x46, 0x40, 0x00, 0x40, 0x06, 0xb1, 0xe6, 0xac, 0x10, 0x0a, 0x63, 0xac, 0x10, 0x0a, 0x0c}
	expectedChecksum := uint16(0xb1e6)
	
	// 验证正确的校验和
	if !ValidateChecksum(data, expectedChecksum, 10) {
		t.Error("Valid checksum should pass validation")
	}
	
	// 验证错误的校验和
	if ValidateChecksum(data, expectedChecksum+1, 10) {
		t.Error("Invalid checksum should fail validation")
	}
}

// 基准测试
func BenchmarkCalculateIPv4Checksum(b *testing.B) {
	header := []byte{0x45, 0x00, 0x00, 0x3c, 0x1c, 0x46, 0x40, 0x00, 0x40, 0x06, 0x00, 0x00, 0xac, 0x10, 0x0a, 0x63, 0xac, 0x10, 0x0a, 0x0c}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		CalculateIPv4Checksum(header)
	}
}

func BenchmarkCalculateUDPChecksum(b *testing.B) {
	srcIP := net.ParseIP("***********")
	dstIP := net.ParseIP("***********")
	udpData := []byte{0x04, 0xd2, 0x00, 0x35, 0x00, 0x0c, 0x00, 0x00, 0x74, 0x65, 0x73, 0x74}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		CalculateUDPChecksum(srcIP, dstIP, udpData)
	}
}

func BenchmarkCalculateTCPChecksum(b *testing.B) {
	srcIP := net.ParseIP("***********")
	dstIP := net.ParseIP("***********")
	tcpData := []byte{0x04, 0xd2, 0x00, 0x50, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x50, 0x02, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		CalculateTCPChecksum(srcIP, dstIP, tcpData)
	}
}
