package protocol

import (
	"encoding/binary"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"strings"
	"time"
)

// MessageType 定义消息类型
type MessageType uint8

const (
	// MessageTypeHeartbeat 心跳消息
	MessageTypeHeartbeat MessageType = iota + 1
	// MessageTypeData 数据消息
	MessageTypeData
	// MessageTypeAuth 认证消息
	MessageTypeAuth
	// MessageTypeResponse 响应消息
	MessageTypeResponse
	// MessageTypeError 错误消息
	MessageTypeError
	// MessageTypeTunData TUN接口数据包消息
	MessageTypeTunData
	// MessageTypeTunResponse TUN接口响应数据包消息
	MessageTypeTunResponse
)

// Message 定义通信协议的消息结构
type Message struct {
	Type      MessageType `json:"type"`
	ID        string      `json:"id"`
	Timestamp int64       `json:"timestamp"`
	Data      []byte      `json:"data"`
}

// NewMessage 创建新消息
func NewMessage(msgType MessageType, id string, data []byte) *Message {
	return &Message{
		Type:      msgType,
		ID:        id,
		Timestamp: time.Now().Unix(),
		Data:      data,
	}
}

// NewHeartbeat 创建心跳消息
func NewHeartbeat() *Message {
	return NewMessage(MessageTypeHeartbeat, "heartbeat", nil)
}

// NewDataMessage 创建数据消息
func NewDataMessage(id string, data []byte) *Message {
	return NewMessage(MessageTypeData, id, data)
}

// NewAuthMessage 创建认证消息
func NewAuthMessage(token string) *Message {
	return NewMessage(MessageTypeAuth, "auth", []byte(token))
}

// NewResponse 创建响应消息
func NewResponse(id string, data []byte) *Message {
	return NewMessage(MessageTypeResponse, id, data)
}

// NewError 创建错误消息
func NewError(id string, err error) *Message {
	return NewMessage(MessageTypeError, id, []byte(err.Error()))
}

// NewTunDataMessage 创建TUN数据包消息
func NewTunDataMessage(id string, packet []byte) *Message {
	return NewMessage(MessageTypeTunData, id, packet)
}

// NewTunResponseMessage 创建TUN响应数据包消息
func NewTunResponseMessage(id string, packet []byte) *Message {
	return NewMessage(MessageTypeTunResponse, id, packet)
}

// Serialize 序列化消息为字节流
// 协议格式: [4字节长度][消息内容JSON]
func (m *Message) Serialize() ([]byte, error) {
	data, err := json.Marshal(m)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal message: %w", err)
	}

	// 创建包含长度前缀的完整消息
	length := uint32(len(data))
	result := make([]byte, 4+len(data))
	binary.BigEndian.PutUint32(result[:4], length)
	copy(result[4:], data)

	return result, nil
}

// Deserialize 从字节流反序列化消息
func Deserialize(data []byte) (*Message, error) {
	var msg Message
	if err := json.Unmarshal(data, &msg); err != nil {
		return nil, fmt.Errorf("failed to unmarshal message: %w", err)
	}
	return &msg, nil
}

// SendMessage 通过连接发送消息
func SendMessage(conn net.Conn, msg *Message) error {
	return SendMessageWithEncryption(conn, msg, nil)
}

// SendMessageWithEncryption 发送加密消息到连接
func SendMessageWithEncryption(conn net.Conn, msg *Message, encryptor interface{}) error {
	data, err := msg.Serialize()
	if err != nil {
		return fmt.Errorf("failed to serialize message: %w", err)
	}

	// 如果启用了加密，加密消息数据
	if encryptor != nil {
		// 这里暂时跳过加密，稍后实现
		// data, err = encryptor.Encrypt(data)
		// if err != nil {
		//     return fmt.Errorf("failed to encrypt message: %w", err)
		// }
	}

	_, err = conn.Write(data)
	if err != nil {
		return fmt.Errorf("failed to write message: %w", err)
	}

	return nil
}

// ReceiveMessage 从连接接收消息
func ReceiveMessage(conn net.Conn) (*Message, error) {
	return ReceiveMessageWithDecryption(conn, nil)
}

// ReceiveMessageWithDecryption 从连接接收并解密消息
func ReceiveMessageWithDecryption(conn net.Conn, decryptor interface{}) (*Message, error) {
	// 读取消息长度
	lengthBuf := make([]byte, 4)
	_, err := io.ReadFull(conn, lengthBuf)
	if err != nil {
		return nil, fmt.Errorf("failed to read message length: %w", err)
	}

	length := binary.BigEndian.Uint32(lengthBuf)

	// 增强的长度验证
	if length == 0 {
		return nil, fmt.Errorf("invalid message: zero length")
	}
	if length > 1024*1024 { // 1MB 限制
		return nil, fmt.Errorf("message too large: %d bytes", length)
	}
	// 检查是否为明显的攻击模式（超大数值）
	if length > 100*1024*1024 { // 100MB - 明显的攻击
		return nil, fmt.Errorf("potential attack detected")
	}

	// 读取消息内容
	msgBuf := make([]byte, length)
	_, err = io.ReadFull(conn, msgBuf)
	if err != nil {
		return nil, fmt.Errorf("failed to read message data: %w", err)
	}

	// 如果启用了解密，解密消息数据
	if decryptor != nil {
		// 这里暂时跳过解密，稍后实现
		// msgBuf, err = decryptor.Decrypt(msgBuf)
		// if err != nil {
		//     return nil, fmt.Errorf("failed to decrypt message: %w", err)
		// }
	}

	// 反序列化并验证消息
	msg, err := Deserialize(msgBuf)
	if err != nil {
		return nil, fmt.Errorf("invalid message format: %w", err)
	}

	// 验证消息内容
	if err := ValidateMessage(msg); err != nil {
		return nil, fmt.Errorf("message validation failed: %w", err)
	}

	return msg, nil
}

// ValidateMessage 验证消息内容的合法性
func ValidateMessage(msg *Message) error {
	// 验证消息类型
	if msg.Type < MessageTypeHeartbeat || msg.Type > MessageTypeTunResponse {
		return fmt.Errorf("invalid message type: %d", msg.Type)
	}

	// 验证消息ID
	if msg.ID == "" {
		return fmt.Errorf("message ID cannot be empty")
	}
	if len(msg.ID) > 256 {
		return fmt.Errorf("message ID too long: %d characters", len(msg.ID))
	}

	// 验证时间戳（不能是未来时间，不能太旧）
	now := time.Now().Unix()
	if msg.Timestamp > now+300 { // 允许5分钟的时钟偏差
		return fmt.Errorf("message timestamp too far in future")
	}
	if msg.Timestamp < now-3600 { // 不允许超过1小时的旧消息
		return fmt.Errorf("message timestamp too old")
	}

	// 验证数据长度
	if len(msg.Data) > 512*1024 { // 512KB数据限制
		return fmt.Errorf("message data too large: %d bytes", len(msg.Data))
	}

	// 根据消息类型进行特定验证
	switch msg.Type {
	case MessageTypeAuth:
		if len(msg.Data) == 0 {
			return fmt.Errorf("auth message cannot have empty data")
		}
		if len(msg.Data) > 1024 { // 认证令牌不应超过1KB
			return fmt.Errorf("auth token too large")
		}
	case MessageTypeHeartbeat:
		// 心跳消息通常不需要数据，但允许小量数据
		if len(msg.Data) > 64 {
			return fmt.Errorf("heartbeat data too large")
		}
	case MessageTypeTunData:
		// TUN数据包消息必须有数据
		if len(msg.Data) == 0 {
			return fmt.Errorf("tun data message cannot have empty data")
		}
		// TUN数据包大小限制（MTU通常为1500字节，加上一些余量）
		if len(msg.Data) > 9200 {
			return fmt.Errorf("tun data packet too large: %d bytes", len(msg.Data))
		}
	case MessageTypeTunResponse:
		// TUN响应数据包消息必须有数据
		if len(msg.Data) == 0 {
			return fmt.Errorf("tun response message cannot have empty data")
		}
		// TUN响应数据包大小限制 - 增加到8KB以支持大的TCP响应包
		// TCP响应包可能包含大量应用层数据（如HTTPS响应、文件下载等）
		if len(msg.Data) > 8192 {
			return fmt.Errorf("tun response packet too large: %d bytes", len(msg.Data))
		}
	}

	return nil
}

// IsSuspiciousError 检查错误是否可疑（可能是攻击）
func IsSuspiciousError(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()
	suspiciousPatterns := []string{
		"message too large",
		"potential attack detected",
		"invalid message format",
		"message validation failed",
		"excessive message size",
	}

	for _, pattern := range suspiciousPatterns {
		if strings.Contains(errStr, pattern) {
			return true
		}
	}
	return false
}

// SanitizeError 清理错误信息，避免泄露敏感信息
func SanitizeError(err error) error {
	if err == nil {
		return nil
	}

	errStr := err.Error()

	// 替换可能泄露信息的错误消息
	if strings.Contains(errStr, "message too large") {
		return fmt.Errorf("invalid message format")
	}
	if strings.Contains(errStr, "potential attack detected") {
		return fmt.Errorf("invalid message format")
	}
	if strings.Contains(errStr, "message validation failed") {
		return fmt.Errorf("invalid message format")
	}

	return fmt.Errorf("protocol error")
}

// String 返回消息的字符串表示
func (m *Message) String() string {
	return fmt.Sprintf("Message{Type: %d, ID: %s, Timestamp: %d, DataLen: %d}",
		m.Type, m.ID, m.Timestamp, len(m.Data))
}
