package protocol

import (
	"bytes"
	"encoding/binary"
	"net"
	"testing"
	"time"
)

func TestNewMessage(t *testing.T) {
	data := []byte("test data")
	msg := NewMessage(MessageTypeData, "test-id", data)

	if msg.Type != MessageTypeData {
		t.<PERSON><PERSON>("Expected type %d, got %d", MessageTypeData, msg.Type)
	}
	if msg.ID != "test-id" {
		t.<PERSON>("Expected ID 'test-id', got '%s'", msg.ID)
	}
	if !bytes.Equal(msg.Data, data) {
		t.<PERSON>("Expected data %v, got %v", data, msg.Data)
	}
	if msg.Timestamp == 0 {
		t.<PERSON><PERSON>("Expected non-zero timestamp")
	}
}

func TestNewHeartbeat(t *testing.T) {
	msg := NewHeartbeat()

	if msg.Type != MessageTypeHeartbeat {
		t.<PERSON>("Expected type %d, got %d", MessageTypeHeartbeat, msg.Type)
	}
	if msg.ID != "heartbeat" {
		t.<PERSON><PERSON>("Expected ID 'heartbeat', got '%s'", msg.ID)
	}
	if msg.Data != nil {
		t.<PERSON>("Expected nil data, got %v", msg.Data)
	}
}

func TestNewDataMessage(t *testing.T) {
	data := []byte("test data")
	msg := NewDataMessage("data-123", data)

	if msg.Type != MessageTypeData {
		t.Errorf("Expected type %d, got %d", MessageTypeData, msg.Type)
	}
	if msg.ID != "data-123" {
		t.Errorf("Expected ID 'data-123', got '%s'", msg.ID)
	}
	if !bytes.Equal(msg.Data, data) {
		t.Errorf("Expected data %v, got %v", data, msg.Data)
	}
}

func TestNewAuthMessage(t *testing.T) {
	token := "secret-token"
	msg := NewAuthMessage(token)

	if msg.Type != MessageTypeAuth {
		t.Errorf("Expected type %d, got %d", MessageTypeAuth, msg.Type)
	}
	if msg.ID != "auth" {
		t.Errorf("Expected ID 'auth', got '%s'", msg.ID)
	}
	if string(msg.Data) != token {
		t.Errorf("Expected data '%s', got '%s'", token, string(msg.Data))
	}
}

func TestNewResponse(t *testing.T) {
	data := []byte("response data")
	msg := NewResponse("resp-123", data)

	if msg.Type != MessageTypeResponse {
		t.Errorf("Expected type %d, got %d", MessageTypeResponse, msg.Type)
	}
	if msg.ID != "resp-123" {
		t.Errorf("Expected ID 'resp-123', got '%s'", msg.ID)
	}
	if !bytes.Equal(msg.Data, data) {
		t.Errorf("Expected data %v, got %v", data, msg.Data)
	}
}

func TestNewError(t *testing.T) {
	err := &testError{"test error"}
	msg := NewError("err-123", err)

	if msg.Type != MessageTypeError {
		t.Errorf("Expected type %d, got %d", MessageTypeError, msg.Type)
	}
	if msg.ID != "err-123" {
		t.Errorf("Expected ID 'err-123', got '%s'", msg.ID)
	}
	if string(msg.Data) != err.Error() {
		t.Errorf("Expected data '%s', got '%s'", err.Error(), string(msg.Data))
	}
}

func TestNewTunDataMessage(t *testing.T) {
	packet := []byte{0x45, 0x00, 0x00, 0x1c} // IPv4 packet header start
	msg := NewTunDataMessage("tun-123", packet)

	if msg.Type != MessageTypeTunData {
		t.Errorf("Expected type %d, got %d", MessageTypeTunData, msg.Type)
	}
	if msg.ID != "tun-123" {
		t.Errorf("Expected ID 'tun-123', got '%s'", msg.ID)
	}
	if !bytes.Equal(msg.Data, packet) {
		t.Errorf("Expected data %v, got %v", packet, msg.Data)
	}
}

func TestNewTunResponseMessage(t *testing.T) {
	packet := []byte{0x45, 0x00, 0x00, 0x1c, 0x00, 0x01} // IPv4 response packet
	msg := NewTunResponseMessage("tun-resp-123", packet)

	if msg.Type != MessageTypeTunResponse {
		t.Errorf("Expected type %d, got %d", MessageTypeTunResponse, msg.Type)
	}
	if msg.ID != "tun-resp-123" {
		t.Errorf("Expected ID 'tun-resp-123', got '%s'", msg.ID)
	}
	if !bytes.Equal(msg.Data, packet) {
		t.Errorf("Expected data %v, got %v", packet, msg.Data)
	}
}

func TestMessageSerialize(t *testing.T) {
	msg := NewDataMessage("test-123", []byte("test data"))

	data, err := msg.Serialize()
	if err != nil {
		t.Fatalf("Failed to serialize message: %v", err)
	}

	// Check length prefix
	if len(data) < 4 {
		t.Fatal("Serialized data too short")
	}

	length := binary.BigEndian.Uint32(data[:4])
	if int(length) != len(data)-4 {
		t.Errorf("Expected length %d, got %d", len(data)-4, length)
	}
}

func TestDeserialize(t *testing.T) {
	original := NewDataMessage("test-123", []byte("test data"))

	// Serialize first
	serialized, err := original.Serialize()
	if err != nil {
		t.Fatalf("Failed to serialize: %v", err)
	}

	// Deserialize the message content (without length prefix)
	deserialized, err := Deserialize(serialized[4:])
	if err != nil {
		t.Fatalf("Failed to deserialize: %v", err)
	}

	// Compare
	if deserialized.Type != original.Type {
		t.Errorf("Expected type %d, got %d", original.Type, deserialized.Type)
	}
	if deserialized.ID != original.ID {
		t.Errorf("Expected ID '%s', got '%s'", original.ID, deserialized.ID)
	}
	if !bytes.Equal(deserialized.Data, original.Data) {
		t.Errorf("Expected data %v, got %v", original.Data, deserialized.Data)
	}
}

func TestMessageString(t *testing.T) {
	msg := NewDataMessage("test-123", []byte("test data"))
	str := msg.String()

	if str == "" {
		t.Error("String representation should not be empty")
	}

	// Should contain key information
	if !contains(str, "test-123") {
		t.Error("String should contain message ID")
	}
}

func TestSerializeDeserializeRoundTrip(t *testing.T) {
	testCases := []struct {
		name string
		msg  *Message
	}{
		{
			name: "Heartbeat",
			msg:  NewHeartbeat(),
		},
		{
			name: "Auth",
			msg:  NewAuthMessage("secret-token"),
		},
		{
			name: "Data",
			msg:  NewDataMessage("data-123", []byte("test data")),
		},
		{
			name: "Response",
			msg:  NewResponse("resp-123", []byte("response data")),
		},
		{
			name: "Error",
			msg:  NewError("err-123", &testError{"test error"}),
		},
		{
			name: "Empty Data",
			msg:  NewDataMessage("empty", []byte{}),
		},
		{
			name: "Large Data",
			msg:  NewDataMessage("large", make([]byte, 1024)),
		},
		{
			name: "TUN Data",
			msg:  NewTunDataMessage("tun-123", []byte{0x45, 0x00, 0x00, 0x1c}),
		},
		{
			name: "TUN Response",
			msg:  NewTunResponseMessage("tun-resp-123", []byte{0x45, 0x00, 0x00, 0x1c, 0x00, 0x01}),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Serialize
			serialized, err := tc.msg.Serialize()
			if err != nil {
				t.Fatalf("Failed to serialize: %v", err)
			}

			// Deserialize
			deserialized, err := Deserialize(serialized[4:])
			if err != nil {
				t.Fatalf("Failed to deserialize: %v", err)
			}

			// Compare
			if deserialized.Type != tc.msg.Type {
				t.Errorf("Type mismatch: expected %d, got %d", tc.msg.Type, deserialized.Type)
			}
			if deserialized.ID != tc.msg.ID {
				t.Errorf("ID mismatch: expected '%s', got '%s'", tc.msg.ID, deserialized.ID)
			}
			if !bytes.Equal(deserialized.Data, tc.msg.Data) {
				t.Errorf("Data mismatch: expected %v, got %v", tc.msg.Data, deserialized.Data)
			}
		})
	}
}

func TestSendReceiveMessage(t *testing.T) {
	// Create a pipe for testing
	server, client := net.Pipe()
	defer server.Close()
	defer client.Close()

	msg := NewDataMessage("test-123", []byte("test data"))

	// Send message in goroutine
	go func() {
		defer client.Close()
		if err := SendMessage(client, msg); err != nil {
			t.Errorf("Failed to send message: %v", err)
		}
	}()

	// Receive message
	received, err := ReceiveMessage(server)
	if err != nil {
		t.Fatalf("Failed to receive message: %v", err)
	}

	// Compare
	if received.Type != msg.Type {
		t.Errorf("Type mismatch: expected %d, got %d", msg.Type, received.Type)
	}
	if received.ID != msg.ID {
		t.Errorf("ID mismatch: expected '%s', got '%s'", msg.ID, received.ID)
	}
	if !bytes.Equal(received.Data, msg.Data) {
		t.Errorf("Data mismatch: expected %v, got %v", msg.Data, received.Data)
	}
}

func TestReceiveMessageSizeLimit(t *testing.T) {
	server, client := net.Pipe()
	defer server.Close()
	defer client.Close()

	// Create a message that exceeds the size limit
	largeData := make([]byte, 2*1024*1024) // 2MB
	msg := NewDataMessage("large", largeData)

	go func() {
		defer client.Close()
		SendMessage(client, msg)
	}()

	// Should fail due to size limit
	_, err := ReceiveMessage(server)
	if err == nil {
		t.Error("Expected error for oversized message")
	}
}

// Helper types and functions for testing
type testError struct {
	message string
}

func (e *testError) Error() string {
	return e.message
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && s[len(s)-len(substr):] == substr ||
		len(s) > len(substr) && findSubstring(s, substr)
}

func findSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

func TestValidateMessage_TunData(t *testing.T) {
	testCases := []struct {
		name        string
		msg         *Message
		expectError bool
	}{
		{
			name:        "Valid TUN data message",
			msg:         NewTunDataMessage("tun-1", []byte{0x45, 0x00, 0x00, 0x1c}),
			expectError: false,
		},
		{
			name: "Empty TUN data",
			msg: &Message{
				Type:      MessageTypeTunData,
				ID:        "test",
				Timestamp: time.Now().Unix(),
				Data:      []byte{},
			},
			expectError: true,
		},
		{
			name: "Too large TUN data",
			msg: &Message{
				Type:      MessageTypeTunData,
				ID:        "test",
				Timestamp: time.Now().Unix(),
				Data:      make([]byte, 3000),
			},
			expectError: true,
		},
		{
			name:        "Valid TUN response message",
			msg:         NewTunResponseMessage("tun-resp-1", []byte{0x45, 0x00, 0x00, 0x1c}),
			expectError: false,
		},
		{
			name: "Empty TUN response data",
			msg: &Message{
				Type:      MessageTypeTunResponse,
				ID:        "test",
				Timestamp: time.Now().Unix(),
				Data:      []byte{},
			},
			expectError: true,
		},
		{
			name: "Too large TUN response data",
			msg: &Message{
				Type:      MessageTypeTunResponse,
				ID:        "test",
				Timestamp: time.Now().Unix(),
				Data:      make([]byte, 3000),
			},
			expectError: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := ValidateMessage(tc.msg)
			if tc.expectError && err == nil {
				t.Error("Expected error but got none")
			}
			if !tc.expectError && err != nil {
				t.Errorf("Unexpected error: %v", err)
			}
		})
	}
}
