package config

import (
	"fmt"
	"strings"

	"cyber-bastion/pkg/logger"

	"github.com/spf13/viper"
)

// ServerConfig 服务器配置
type ServerConfig struct {
	Host         string            `mapstructure:"host" yaml:"host"`
	Port         int               `mapstructure:"port" yaml:"port"`
	ReadTimeout  int               `mapstructure:"read_timeout" yaml:"read_timeout"`
	WriteTimeout int               `mapstructure:"write_timeout" yaml:"write_timeout"`
	MaxClients   int               `mapstructure:"max_clients" yaml:"max_clients"`
	AuthToken    string            `mapstructure:"auth_token" yaml:"auth_token"`
	Security     *SecurityConfig   `mapstructure:"security" yaml:"security"`
	Forwarding   *ForwardingConfig `mapstructure:"forwarding" yaml:"forwarding"`
	Logger       *logger.Config    `mapstructure:"logger" yaml:"logger"`
}

// ForwardingConfig 转发配置
type ForwardingConfig struct {
	// 是否启用透明转发
	Enabled bool `mapstructure:"enabled" yaml:"enabled"`
	// 是否绑定到特定接口
	BindInterface bool `mapstructure:"bind_interface" yaml:"bind_interface"`
	// 绑定的接口名称
	InterfaceName string `mapstructure:"interface_name" yaml:"interface_name"`
	// 允许转发的目标网络（CIDR格式）
	AllowedNetworks []string `mapstructure:"allowed_networks" yaml:"allowed_networks"`
	// 禁止转发的目标网络（CIDR格式）
	BlockedNetworks []string `mapstructure:"blocked_networks" yaml:"blocked_networks"`
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	EnableIPFilter   bool     `mapstructure:"enable_ip_filter" yaml:"enable_ip_filter"`
	AllowedIPs       []string `mapstructure:"allowed_ips" yaml:"allowed_ips"`
	BlockedIPs       []string `mapstructure:"blocked_ips" yaml:"blocked_ips"`
	MaxConnPerIP     int      `mapstructure:"max_conn_per_ip" yaml:"max_conn_per_ip"`
	RateLimitEnabled bool     `mapstructure:"rate_limit_enabled" yaml:"rate_limit_enabled"`
	RateLimitPerMin  int      `mapstructure:"rate_limit_per_min" yaml:"rate_limit_per_min"`
	EnableTLS        bool     `mapstructure:"enable_tls" yaml:"enable_tls"`
	TLSCertFile      string   `mapstructure:"tls_cert_file" yaml:"tls_cert_file"`
	TLSKeyFile       string   `mapstructure:"tls_key_file" yaml:"tls_key_file"`
	TLSCAFile        string   `mapstructure:"tls_ca_file" yaml:"tls_ca_file"`
	TLSSkipVerify    bool     `mapstructure:"tls_skip_verify" yaml:"tls_skip_verify"`
	EnableEncryption bool     `mapstructure:"enable_encryption" yaml:"enable_encryption"`
	EncryptionKey    string   `mapstructure:"encryption_key" yaml:"encryption_key"`
}

// TunConfig TUN接口配置
type TunConfig struct {
	// 是否启用透明转发
	Enabled bool `mapstructure:"enabled" yaml:"enabled"`
	// 接口名称
	Name string `mapstructure:"name" yaml:"name"`
	// IP地址
	IP string `mapstructure:"ip" yaml:"ip"`
	// 子网掩码
	Netmask string `mapstructure:"netmask" yaml:"netmask"`
	// MTU大小
	MTU int `mapstructure:"mtu" yaml:"mtu"`
}

// ClientConfig 客户端配置
type ClientConfig struct {
	// 兼容性配置 (单隧道模式)
	ServerHost        string         `mapstructure:"server_host" yaml:"server_host"`
	ServerPort        int            `mapstructure:"server_port" yaml:"server_port"`
	ConnectTimeout    int            `mapstructure:"connect_timeout" yaml:"connect_timeout"`
	HeartbeatInterval int            `mapstructure:"heartbeat_interval" yaml:"heartbeat_interval"`
	ReconnectDelay    int            `mapstructure:"reconnect_delay" yaml:"reconnect_delay"`
	MaxReconnectDelay int            `mapstructure:"max_reconnect_delay" yaml:"max_reconnect_delay"`
	MaxReconnectTries int            `mapstructure:"max_reconnect_tries" yaml:"max_reconnect_tries"`
	AuthToken         string         `mapstructure:"auth_token" yaml:"auth_token"`
	EnableTLS         bool           `mapstructure:"enable_tls" yaml:"enable_tls"`
	TLSCertFile       string         `mapstructure:"tls_cert_file" yaml:"tls_cert_file"`
	TLSKeyFile        string         `mapstructure:"tls_key_file" yaml:"tls_key_file"`
	TLSCAFile         string         `mapstructure:"tls_ca_file" yaml:"tls_ca_file"`
	TLSSkipVerify     bool           `mapstructure:"tls_skip_verify" yaml:"tls_skip_verify"`
	EnableEncryption  bool           `mapstructure:"enable_encryption" yaml:"enable_encryption"`
	EncryptionKey     string         `mapstructure:"encryption_key" yaml:"encryption_key"`
	Tun               *TunConfig     `mapstructure:"tun" yaml:"tun"`
	Logger            *logger.Config `mapstructure:"logger" yaml:"logger"`

	// 新架构配置 (多隧道模式)
	Tunnels    []*TunnelConfig   `mapstructure:"tunnels" yaml:"tunnels"`
	Routing    *RoutingConfig    `mapstructure:"routing" yaml:"routing"`
	Controller *ControllerConfig `mapstructure:"controller" yaml:"controller"`
}

// DefaultForwardingConfig 返回默认转发配置
func DefaultForwardingConfig() *ForwardingConfig {
	return &ForwardingConfig{
		Enabled:         false,
		BindInterface:   false,
		InterfaceName:   "",
		AllowedNetworks: []string{},
		BlockedNetworks: []string{"*********/8", "***********/16"}, // 默认阻止本地回环和链路本地地址
	}
}

// DefaultServerConfig 返回默认服务器配置
func DefaultServerConfig() *ServerConfig {
	return &ServerConfig{
		Host:         "0.0.0.0",
		Port:         8080,
		ReadTimeout:  30,
		WriteTimeout: 30,
		MaxClients:   100,
		AuthToken:    "default-token",
		Security: &SecurityConfig{
			EnableIPFilter:   false,
			AllowedIPs:       []string{},
			BlockedIPs:       []string{},
			MaxConnPerIP:     10,
			RateLimitEnabled: true,
			RateLimitPerMin:  60,
			EnableTLS:        false,
			TLSCertFile:      "certs/server.crt",
			TLSKeyFile:       "certs/server.key",
			TLSCAFile:        "certs/ca.crt",
			TLSSkipVerify:    false,
			EnableEncryption: false,
			EncryptionKey:    "default-encryption-key-change-in-production",
		},
		Forwarding: DefaultForwardingConfig(),
		Logger:     logger.DefaultConfig(),
	}
}

// DefaultTunConfig 返回默认TUN配置
func DefaultTunConfig() *TunConfig {
	return &TunConfig{
		Enabled: false,
		Name:    "cyber-tun",
		IP:      "********",
		Netmask: "*************",
		MTU:     1500,
	}
}

// DefaultClientConfig 返回默认客户端配置
func DefaultClientConfig() *ClientConfig {
	return &ClientConfig{
		ServerHost:        "localhost",
		ServerPort:        8080,
		ConnectTimeout:    10,
		HeartbeatInterval: 30,
		ReconnectDelay:    5,
		MaxReconnectDelay: 60,
		MaxReconnectTries: 10,
		AuthToken:         "default-token",
		EnableTLS:         false,
		TLSCertFile:       "certs/client.crt",
		TLSKeyFile:        "certs/client.key",
		TLSCAFile:         "certs/ca.crt",
		TLSSkipVerify:     false,
		EnableEncryption:  false,
		EncryptionKey:     "default-encryption-key-change-in-production",
		Tun:               DefaultTunConfig(),
		Logger:            logger.DefaultConfig(),
	}
}

// LoadServerConfig 加载服务器配置
func LoadServerConfig(configFile string) (*ServerConfig, error) {
	config := DefaultServerConfig()

	v := viper.New()
	v.SetConfigType("yaml")

	if configFile != "" {
		v.SetConfigFile(configFile)
	} else {
		v.SetConfigName("server")
		v.AddConfigPath("./configs")
		v.AddConfigPath(".")
	}

	// 设置环境变量前缀
	v.SetEnvPrefix("CYBER_BASTION_SERVER")
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	v.AutomaticEnv()

	// 设置默认值
	setServerDefaults(v)

	// 读取配置文件
	if err := v.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
		// 配置文件不存在时使用默认配置
	}

	// 解析配置
	if err := v.Unmarshal(config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return config, nil
}

// LoadClientConfig 加载客户端配置
func LoadClientConfig(configFile string) (*ClientConfig, error) {
	config := DefaultClientConfig()

	v := viper.New()
	v.SetConfigType("yaml")

	if configFile != "" {
		v.SetConfigFile(configFile)
	} else {
		v.SetConfigName("client")
		v.AddConfigPath("./configs")
		v.AddConfigPath(".")
	}

	// 设置环境变量前缀
	v.SetEnvPrefix("CYBER_BASTION_CLIENT")
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	v.AutomaticEnv()

	// 设置默认值
	setClientDefaults(v)

	// 读取配置文件
	if err := v.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
		// 配置文件不存在时使用默认配置
	}

	// 解析配置
	if err := v.Unmarshal(config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return config, nil
}

// setServerDefaults 设置服务器默认配置
func setServerDefaults(v *viper.Viper) {
	v.SetDefault("host", "0.0.0.0")
	v.SetDefault("port", 8080)
	v.SetDefault("read_timeout", 30)
	v.SetDefault("write_timeout", 30)
	v.SetDefault("max_clients", 100)
	v.SetDefault("auth_token", "default-token")
	v.SetDefault("security.enable_ip_filter", false)
	v.SetDefault("security.allowed_ips", []string{})
	v.SetDefault("security.blocked_ips", []string{})
	v.SetDefault("security.max_conn_per_ip", 10)
	v.SetDefault("security.rate_limit_enabled", true)
	v.SetDefault("security.rate_limit_per_min", 60)
	v.SetDefault("forwarding.enabled", false)
	v.SetDefault("forwarding.bind_interface", false)
	v.SetDefault("forwarding.interface_name", "")
	v.SetDefault("forwarding.allowed_networks", []string{})
	v.SetDefault("forwarding.blocked_networks", []string{"*********/8", "***********/16"})
	v.SetDefault("logger.level", "info")
	v.SetDefault("logger.format", "json")
	v.SetDefault("logger.output", "stdout")
}

// setClientDefaults 设置客户端默认配置
func setClientDefaults(v *viper.Viper) {
	v.SetDefault("server_host", "localhost")
	v.SetDefault("server_port", 8080)
	v.SetDefault("connect_timeout", 10)
	v.SetDefault("heartbeat_interval", 30)
	v.SetDefault("reconnect_delay", 5)
	v.SetDefault("max_reconnect_delay", 60)
	v.SetDefault("max_reconnect_tries", 10)
	v.SetDefault("auth_token", "default-token")
	v.SetDefault("enable_tls", false)
	v.SetDefault("tls_cert_file", "certs/client.crt")
	v.SetDefault("tls_key_file", "certs/client.key")
	v.SetDefault("tls_ca_file", "certs/ca.crt")
	v.SetDefault("tls_skip_verify", false)
	v.SetDefault("enable_encryption", false)
	v.SetDefault("encryption_key", "default-encryption-key-change-in-production")
	v.SetDefault("tun.enabled", false)
	v.SetDefault("tun.name", "cyber-tun")
	v.SetDefault("tun.ip", "********")
	v.SetDefault("tun.netmask", "*************")
	v.SetDefault("tun.mtu", 1500)
	v.SetDefault("logger.level", "info")
	v.SetDefault("logger.format", "json")
	v.SetDefault("logger.output", "stdout")
}

// TunnelConfig 隧道配置
type TunnelConfig struct {
	Name     string `mapstructure:"name" yaml:"name"`
	Server   string `mapstructure:"server" yaml:"server"`
	Port     int    `mapstructure:"port" yaml:"port"`
	Protocol string `mapstructure:"protocol" yaml:"protocol"` // tcp, websocket, quic
	Priority int    `mapstructure:"priority" yaml:"priority"`
	Enabled  bool   `mapstructure:"enabled" yaml:"enabled"`

	// 认证配置
	AuthToken string `mapstructure:"auth_token" yaml:"auth_token"`

	// TLS配置
	EnableTLS     bool   `mapstructure:"enable_tls" yaml:"enable_tls"`
	TLSCertFile   string `mapstructure:"tls_cert_file" yaml:"tls_cert_file"`
	TLSKeyFile    string `mapstructure:"tls_key_file" yaml:"tls_key_file"`
	TLSCAFile     string `mapstructure:"tls_ca_file" yaml:"tls_ca_file"`
	TLSSkipVerify bool   `mapstructure:"tls_skip_verify" yaml:"tls_skip_verify"`

	// 加密配置
	EnableEncryption bool   `mapstructure:"enable_encryption" yaml:"enable_encryption"`
	EncryptionKey    string `mapstructure:"encryption_key" yaml:"encryption_key"`

	// 连接配置
	ConnectTimeout    int `mapstructure:"connect_timeout" yaml:"connect_timeout"`
	HeartbeatInterval int `mapstructure:"heartbeat_interval" yaml:"heartbeat_interval"`
	ReconnectDelay    int `mapstructure:"reconnect_delay" yaml:"reconnect_delay"`
	MaxReconnectDelay int `mapstructure:"max_reconnect_delay" yaml:"max_reconnect_delay"`
	MaxReconnectTries int `mapstructure:"max_reconnect_tries" yaml:"max_reconnect_tries"`
}

// RoutingConfig 路由配置
type RoutingConfig struct {
	Strategy string         `mapstructure:"strategy" yaml:"strategy"` // round_robin, failover, load_balance, priority
	Rules    []*RoutingRule `mapstructure:"rules" yaml:"rules"`
}

// RoutingRule 路由规则
type RoutingRule struct {
	Name        string `mapstructure:"name" yaml:"name"`
	Destination string `mapstructure:"destination" yaml:"destination"` // CIDR格式
	Action      string `mapstructure:"action" yaml:"action"`           // tunnel, direct, block
	Tunnel      string `mapstructure:"tunnel" yaml:"tunnel"`           // 指定隧道名称
	Priority    int    `mapstructure:"priority" yaml:"priority"`
}

// ControllerConfig 控制器配置
type ControllerConfig struct {
	// 健康检查配置
	HealthCheckInterval int `mapstructure:"health_check_interval" yaml:"health_check_interval"`
	HealthCheckTimeout  int `mapstructure:"health_check_timeout" yaml:"health_check_timeout"`

	// 故障转移配置
	FailoverEnabled   bool `mapstructure:"failover_enabled" yaml:"failover_enabled"`
	FailoverThreshold int  `mapstructure:"failover_threshold" yaml:"failover_threshold"`
	FailoverCooldown  int  `mapstructure:"failover_cooldown" yaml:"failover_cooldown"`

	// 负载均衡配置
	LoadBalanceEnabled bool   `mapstructure:"load_balance_enabled" yaml:"load_balance_enabled"`
	LoadBalanceMethod  string `mapstructure:"load_balance_method" yaml:"load_balance_method"` // round_robin, least_conn, weighted

	// 监控配置
	MetricsEnabled  bool `mapstructure:"metrics_enabled" yaml:"metrics_enabled"`
	MetricsInterval int  `mapstructure:"metrics_interval" yaml:"metrics_interval"`
}

// IsMultiTunnelMode 检查是否启用多隧道模式
func (c *ClientConfig) IsMultiTunnelMode() bool {
	return len(c.Tunnels) > 0
}

// GetPrimaryTunnel 获取主隧道配置（兼容性）
func (c *ClientConfig) GetPrimaryTunnel() *TunnelConfig {
	if c.IsMultiTunnelMode() {
		// 返回优先级最高的启用隧道
		var primary *TunnelConfig
		for _, tunnel := range c.Tunnels {
			if tunnel.Enabled && (primary == nil || tunnel.Priority < primary.Priority) {
				primary = tunnel
			}
		}
		return primary
	}

	// 兼容模式：从单隧道配置创建TunnelConfig
	return &TunnelConfig{
		Name:              "default",
		Server:            c.ServerHost,
		Port:              c.ServerPort,
		Protocol:          "tcp",
		Priority:          1,
		Enabled:           true,
		AuthToken:         c.AuthToken,
		EnableTLS:         c.EnableTLS,
		TLSCertFile:       c.TLSCertFile,
		TLSKeyFile:        c.TLSKeyFile,
		TLSCAFile:         c.TLSCAFile,
		TLSSkipVerify:     c.TLSSkipVerify,
		EnableEncryption:  c.EnableEncryption,
		EncryptionKey:     c.EncryptionKey,
		ConnectTimeout:    c.ConnectTimeout,
		HeartbeatInterval: c.HeartbeatInterval,
		ReconnectDelay:    c.ReconnectDelay,
		MaxReconnectDelay: c.MaxReconnectDelay,
		MaxReconnectTries: c.MaxReconnectTries,
	}
}

// GetEnabledTunnels 获取所有启用的隧道
func (c *ClientConfig) GetEnabledTunnels() []*TunnelConfig {
	if !c.IsMultiTunnelMode() {
		return []*TunnelConfig{c.GetPrimaryTunnel()}
	}

	var enabled []*TunnelConfig
	for _, tunnel := range c.Tunnels {
		if tunnel.Enabled {
			enabled = append(enabled, tunnel)
		}
	}
	return enabled
}

// DefaultTunnelConfig 返回默认隧道配置
func DefaultTunnelConfig() *TunnelConfig {
	return &TunnelConfig{
		Name:              "default",
		Server:            "localhost",
		Port:              8080,
		Protocol:          "tcp",
		Priority:          1,
		Enabled:           true,
		AuthToken:         "default-token",
		EnableTLS:         false,
		TLSCertFile:       "certs/client.crt",
		TLSKeyFile:        "certs/client.key",
		TLSCAFile:         "certs/ca.crt",
		TLSSkipVerify:     false,
		EnableEncryption:  false,
		EncryptionKey:     "default-encryption-key-change-in-production",
		ConnectTimeout:    10,
		HeartbeatInterval: 30,
		ReconnectDelay:    5,
		MaxReconnectDelay: 60,
		MaxReconnectTries: 10,
	}
}

// DefaultRoutingConfig 返回默认路由配置
func DefaultRoutingConfig() *RoutingConfig {
	return &RoutingConfig{
		Strategy: "priority",
		Rules: []*RoutingRule{
			{
				Name:        "default",
				Destination: "0.0.0.0/0",
				Action:      "tunnel",
				Priority:    100,
			},
		},
	}
}

// DefaultControllerConfig 返回默认控制器配置
func DefaultControllerConfig() *ControllerConfig {
	return &ControllerConfig{
		HealthCheckInterval: 30,
		HealthCheckTimeout:  5,
		FailoverEnabled:     true,
		FailoverThreshold:   3,
		FailoverCooldown:    60,
		LoadBalanceEnabled:  false,
		LoadBalanceMethod:   "round_robin",
		MetricsEnabled:      true,
		MetricsInterval:     60,
	}
}
