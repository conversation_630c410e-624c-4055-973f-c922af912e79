package config

import (
	"testing"
)

// TestEncryptionConfigValidation 测试加密配置验证
func TestEncryptionConfigValidation(t *testing.T) {
	validator := NewConfigValidator()

	t.Run("ValidEncryptionConfig", func(t *testing.T) {
		// 有效的加密配置
		cfg := &ClientConfig{
			ServerHost:       "localhost",
			ServerPort:       8080,
			EnableEncryption: true,
			EncryptionKey:    "this-is-a-secure-encryption-key-32-chars",
		}

		if err := validator.ValidateClientConfig(cfg); err != nil {
			t.Errorf("Valid encryption config should pass validation: %v", err)
		}
	})

	t.Run("EmptyEncryptionKey", func(t *testing.T) {
		// 空的加密密钥
		cfg := &ClientConfig{
			ServerHost:       "localhost",
			ServerPort:       8080,
			EnableEncryption: true,
			EncryptionKey:    "",
		}

		if err := validator.ValidateClientConfig(cfg); err == nil {
			t.Error("Empty encryption key should fail validation")
		}
	})

	t.Run("ShortEncryptionKey", func(t *testing.T) {
		// 过短的加密密钥
		cfg := &ClientConfig{
			ServerHost:       "localhost",
			ServerPort:       8080,
			EnableEncryption: true,
			EncryptionKey:    "short",
		}

		if err := validator.ValidateClientConfig(cfg); err == nil {
			t.Error("Short encryption key should fail validation")
		}
	})

	t.Run("DefaultEncryptionKey", func(t *testing.T) {
		// 默认的加密密钥
		cfg := &ClientConfig{
			ServerHost:       "localhost",
			ServerPort:       8080,
			EnableEncryption: true,
			EncryptionKey:    "your-encryption-key-change-in-production",
		}

		if err := validator.ValidateClientConfig(cfg); err == nil {
			t.Error("Default encryption key should fail validation")
		}
	})

	t.Run("EncryptionDisabled", func(t *testing.T) {
		// 禁用加密
		cfg := &ClientConfig{
			ServerHost:       "localhost",
			ServerPort:       8080,
			EnableEncryption: false,
			EncryptionKey:    "", // 禁用时可以为空
		}

		if err := validator.ValidateClientConfig(cfg); err != nil {
			t.Errorf("Disabled encryption should pass validation: %v", err)
		}
	})
}

// TestTunnelEncryptionValidation 测试隧道加密配置验证
func TestTunnelEncryptionValidation(t *testing.T) {
	validator := NewConfigValidator()

	t.Run("ValidTunnelEncryption", func(t *testing.T) {
		cfg := &ClientConfig{
			Tunnels: []*TunnelConfig{
				{
					Name:             "test",
					Server:           "localhost",
					Port:             8080,
					Protocol:         "tcp",
					Priority:         1,
					Enabled:          true,
					EnableEncryption: true,
					EncryptionKey:    "secure-tunnel-encryption-key-32-chars",
				},
			},
			Routing: DefaultRoutingConfig(),
		}

		if err := validator.ValidateClientConfig(cfg); err != nil {
			t.Errorf("Valid tunnel encryption should pass validation: %v", err)
		}
	})

	t.Run("EmptyTunnelEncryptionKey", func(t *testing.T) {
		cfg := &ClientConfig{
			Tunnels: []*TunnelConfig{
				{
					Name:             "test",
					Server:           "localhost",
					Port:             8080,
					Protocol:         "tcp",
					Priority:         1,
					Enabled:          true,
					EnableEncryption: true,
					EncryptionKey:    "", // 空密钥
				},
			},
			Routing: DefaultRoutingConfig(),
		}

		if err := validator.ValidateClientConfig(cfg); err == nil {
			t.Error("Empty tunnel encryption key should fail validation")
		}
	})

	t.Run("ShortTunnelEncryptionKey", func(t *testing.T) {
		cfg := &ClientConfig{
			Tunnels: []*TunnelConfig{
				{
					Name:             "test",
					Server:           "localhost",
					Port:             8080,
					Protocol:         "tcp",
					Priority:         1,
					Enabled:          true,
					EnableEncryption: true,
					EncryptionKey:    "short", // 过短密钥
				},
			},
			Routing: DefaultRoutingConfig(),
		}

		if err := validator.ValidateClientConfig(cfg); err == nil {
			t.Error("Short tunnel encryption key should fail validation")
		}
	})
}

// TestServerEncryptionValidation 测试服务端加密配置验证
func TestServerEncryptionValidation(t *testing.T) {
	validator := NewConfigValidator()

	t.Run("ValidServerEncryption", func(t *testing.T) {
		cfg := &ServerConfig{
			Host: "localhost",
			Port: 8080,
			Security: &SecurityConfig{
				EnableEncryption: true,
				EncryptionKey:    "secure-server-encryption-key-32-chars",
			},
		}

		if err := validator.ValidateServerConfig(cfg); err != nil {
			t.Errorf("Valid server encryption should pass validation: %v", err)
		}
	})

	t.Run("EmptyServerEncryptionKey", func(t *testing.T) {
		cfg := &ServerConfig{
			Host: "localhost",
			Port: 8080,
			Security: &SecurityConfig{
				EnableEncryption: true,
				EncryptionKey:    "",
			},
		}

		if err := validator.ValidateServerConfig(cfg); err == nil {
			t.Error("Empty server encryption key should fail validation")
		}
	})

	t.Run("DefaultServerEncryptionKey", func(t *testing.T) {
		cfg := &ServerConfig{
			Host: "localhost",
			Port: 8080,
			Security: &SecurityConfig{
				EnableEncryption: true,
				EncryptionKey:    "your-encryption-key-change-in-production",
			},
		}

		if err := validator.ValidateServerConfig(cfg); err == nil {
			t.Error("Default server encryption key should fail validation")
		}
	})
}

// TestEncryptionKeyConsistency 测试加密密钥一致性
func TestEncryptionKeyConsistency(t *testing.T) {
	// 这个测试模拟客户端和服务端配置的一致性检查
	clientKey := "consistent-encryption-key-32-chars"
	serverKey := "consistent-encryption-key-32-chars"

	if clientKey != serverKey {
		t.Error("Client and server encryption keys should be consistent")
	}

	// 测试不一致的情况
	inconsistentServerKey := "different-encryption-key-32-chars"
	if clientKey == inconsistentServerKey {
		t.Error("This test should detect inconsistent keys")
	}
}
