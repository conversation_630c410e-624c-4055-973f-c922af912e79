package config

import (
	"cyber-bastion/pkg/logger"
	"os"
	"path/filepath"
	"testing"
)

func TestDefaultServerConfig(t *testing.T) {
	config := DefaultServerConfig()

	if config.Host != "0.0.0.0" {
		t.<PERSON><PERSON><PERSON>("Expected host '0.0.0.0', got '%s'", config.Host)
	}
	if config.Port != 8080 {
		t.<PERSON><PERSON><PERSON>("Expected port 8080, got %d", config.Port)
	}
	if config.ReadTimeout != 30 {
		t.<PERSON><PERSON><PERSON>("Expected read timeout 30, got %d", config.ReadTimeout)
	}
	if config.WriteTimeout != 30 {
		t.<PERSON>rrorf("Expected write timeout 30, got %d", config.WriteTimeout)
	}
	if config.MaxClients != 100 {
		t.<PERSON><PERSON>rf("Expected max clients 100, got %d", config.MaxClients)
	}
	if config.AuthToken != "default-token" {
		t.<PERSON>rrorf("Expected auth token 'default-token', got '%s'", config.AuthToken)
	}
	if config.Logger == nil {
		t.<PERSON>rror("Expected logger config to be non-nil")
	}
}

func TestDefaultClientConfig(t *testing.T) {
	config := DefaultClientConfig()

	if config.ServerHost != "localhost" {
		t.Errorf("Expected server host 'localhost', got '%s'", config.ServerHost)
	}
	if config.ServerPort != 8080 {
		t.Errorf("Expected server port 8080, got %d", config.ServerPort)
	}
	if config.ConnectTimeout != 10 {
		t.Errorf("Expected connect timeout 10, got %d", config.ConnectTimeout)
	}
	if config.HeartbeatInterval != 30 {
		t.Errorf("Expected heartbeat interval 30, got %d", config.HeartbeatInterval)
	}
	if config.ReconnectDelay != 5 {
		t.Errorf("Expected reconnect delay 5, got %d", config.ReconnectDelay)
	}
	if config.AuthToken != "default-token" {
		t.Errorf("Expected auth token 'default-token', got '%s'", config.AuthToken)
	}
	if config.Logger == nil {
		t.Error("Expected logger config to be non-nil")
	}
}

func TestLoadServerConfigWithFile(t *testing.T) {
	// Create temporary config file
	tempDir := t.TempDir()
	configFile := filepath.Join(tempDir, "server.yaml")

	configContent := `
host: "127.0.0.1"
port: 9090
read_timeout: 60
write_timeout: 60
max_clients: 200
auth_token: "test-token"
logger:
  level: "debug"
  format: "console"
  output: "stderr"
`

	if err := os.WriteFile(configFile, []byte(configContent), 0644); err != nil {
		t.Fatalf("Failed to write config file: %v", err)
	}

	config, err := LoadServerConfig(configFile)
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	if config.Host != "127.0.0.1" {
		t.Errorf("Expected host '127.0.0.1', got '%s'", config.Host)
	}
	if config.Port != 9090 {
		t.Errorf("Expected port 9090, got %d", config.Port)
	}
	if config.ReadTimeout != 60 {
		t.Errorf("Expected read timeout 60, got %d", config.ReadTimeout)
	}
	if config.WriteTimeout != 60 {
		t.Errorf("Expected write timeout 60, got %d", config.WriteTimeout)
	}
	if config.MaxClients != 200 {
		t.Errorf("Expected max clients 200, got %d", config.MaxClients)
	}
	if config.AuthToken != "test-token" {
		t.Errorf("Expected auth token 'test-token', got '%s'", config.AuthToken)
	}
	if config.Logger.Level != "debug" {
		t.Errorf("Expected log level 'debug', got '%s'", config.Logger.Level)
	}
	if config.Logger.Format != "console" {
		t.Errorf("Expected log format 'console', got '%s'", config.Logger.Format)
	}
	if config.Logger.Output != "stderr" {
		t.Errorf("Expected log output 'stderr', got '%s'", config.Logger.Output)
	}
}

func TestLoadClientConfigWithFile(t *testing.T) {
	// Create temporary config file
	tempDir := t.TempDir()
	configFile := filepath.Join(tempDir, "client.yaml")

	configContent := `
server_host: "*************"
server_port: 9090
connect_timeout: 20
heartbeat_interval: 60
reconnect_delay: 10
auth_token: "test-token"
logger:
  level: "debug"
  format: "json"
  output: "stdout"
`

	if err := os.WriteFile(configFile, []byte(configContent), 0644); err != nil {
		t.Fatalf("Failed to write config file: %v", err)
	}

	config, err := LoadClientConfig(configFile)
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	if config.ServerHost != "*************" {
		t.Errorf("Expected server host '*************', got '%s'", config.ServerHost)
	}
	if config.ServerPort != 9090 {
		t.Errorf("Expected server port 9090, got %d", config.ServerPort)
	}
	if config.ConnectTimeout != 20 {
		t.Errorf("Expected connect timeout 20, got %d", config.ConnectTimeout)
	}
	if config.HeartbeatInterval != 60 {
		t.Errorf("Expected heartbeat interval 60, got %d", config.HeartbeatInterval)
	}
	if config.ReconnectDelay != 10 {
		t.Errorf("Expected reconnect delay 10, got %d", config.ReconnectDelay)
	}
	if config.AuthToken != "test-token" {
		t.Errorf("Expected auth token 'test-token', got '%s'", config.AuthToken)
	}
	if config.Logger.Level != "debug" {
		t.Errorf("Expected log level 'debug', got '%s'", config.Logger.Level)
	}
}

func TestLoadServerConfigWithoutFile(t *testing.T) {
	// Load config without specifying a file (should use defaults)
	config, err := LoadServerConfig("")
	if err != nil {
		t.Fatalf("Failed to load default config: %v", err)
	}

	// Should have default values
	if config.Host != "0.0.0.0" {
		t.Errorf("Expected default host '0.0.0.0', got '%s'", config.Host)
	}
	if config.Port != 8080 {
		t.Errorf("Expected default port 8080, got %d", config.Port)
	}
}

func TestLoadClientConfigWithoutFile(t *testing.T) {
	// Load config without specifying a file (should use defaults)
	config, err := LoadClientConfig("")
	if err != nil {
		t.Fatalf("Failed to load default config: %v", err)
	}

	// Should have default values
	if config.ServerHost != "localhost" {
		t.Errorf("Expected default server host 'localhost', got '%s'", config.ServerHost)
	}
	if config.ServerPort != 8080 {
		t.Errorf("Expected default server port 8080, got %d", config.ServerPort)
	}
}

func TestLoadServerConfigWithEnvironmentVariables(t *testing.T) {
	// Set environment variables
	os.Setenv("CYBER_BASTION_SERVER_HOST", "env-host")
	os.Setenv("CYBER_BASTION_SERVER_PORT", "7777")
	os.Setenv("CYBER_BASTION_SERVER_AUTH_TOKEN", "env-token")
	defer func() {
		os.Unsetenv("CYBER_BASTION_SERVER_HOST")
		os.Unsetenv("CYBER_BASTION_SERVER_PORT")
		os.Unsetenv("CYBER_BASTION_SERVER_AUTH_TOKEN")
	}()

	config, err := LoadServerConfig("")
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	if config.Host != "env-host" {
		t.Errorf("Expected host from env 'env-host', got '%s'", config.Host)
	}
	if config.Port != 7777 {
		t.Errorf("Expected port from env 7777, got %d", config.Port)
	}
	if config.AuthToken != "env-token" {
		t.Errorf("Expected auth token from env 'env-token', got '%s'", config.AuthToken)
	}
}

func TestLoadClientConfigWithEnvironmentVariables(t *testing.T) {
	// Set environment variables
	os.Setenv("CYBER_BASTION_CLIENT_SERVER_HOST", "env-client-host")
	os.Setenv("CYBER_BASTION_CLIENT_SERVER_PORT", "6666")
	os.Setenv("CYBER_BASTION_CLIENT_AUTH_TOKEN", "env-client-token")
	defer func() {
		os.Unsetenv("CYBER_BASTION_CLIENT_SERVER_HOST")
		os.Unsetenv("CYBER_BASTION_CLIENT_SERVER_PORT")
		os.Unsetenv("CYBER_BASTION_CLIENT_AUTH_TOKEN")
	}()

	config, err := LoadClientConfig("")
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	if config.ServerHost != "env-client-host" {
		t.Errorf("Expected server host from env 'env-client-host', got '%s'", config.ServerHost)
	}
	if config.ServerPort != 6666 {
		t.Errorf("Expected server port from env 6666, got %d", config.ServerPort)
	}
	if config.AuthToken != "env-client-token" {
		t.Errorf("Expected auth token from env 'env-client-token', got '%s'", config.AuthToken)
	}
}

func TestLoadConfigWithInvalidFile(t *testing.T) {
	// Try to load non-existent file
	_, err := LoadServerConfig("/non/existent/file.yaml")
	if err == nil {
		t.Error("Expected error for non-existent config file")
	}
}

func TestLoadConfigWithInvalidYAML(t *testing.T) {
	// Create temporary invalid YAML file
	tempDir := t.TempDir()
	configFile := filepath.Join(tempDir, "invalid.yaml")

	invalidContent := `
host: "test"
port: invalid_port
[invalid yaml
`

	if err := os.WriteFile(configFile, []byte(invalidContent), 0644); err != nil {
		t.Fatalf("Failed to write invalid config file: %v", err)
	}

	_, err := LoadServerConfig(configFile)
	if err == nil {
		t.Error("Expected error for invalid YAML config file")
	}
}

func TestConfigLoggerIntegration(t *testing.T) {
	config := DefaultServerConfig()

	// Test that logger config is properly integrated
	if config.Logger == nil {
		t.Fatal("Logger config should not be nil")
	}

	// Test logger config defaults
	expectedLogger := logger.DefaultConfig()
	if config.Logger.Level != expectedLogger.Level {
		t.Errorf("Expected logger level '%s', got '%s'", expectedLogger.Level, config.Logger.Level)
	}
	if config.Logger.Format != expectedLogger.Format {
		t.Errorf("Expected logger format '%s', got '%s'", expectedLogger.Format, config.Logger.Format)
	}
	if config.Logger.Output != expectedLogger.Output {
		t.Errorf("Expected logger output '%s', got '%s'", expectedLogger.Output, config.Logger.Output)
	}
}
