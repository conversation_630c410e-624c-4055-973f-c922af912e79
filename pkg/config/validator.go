package config

import (
	"fmt"
	"net"
	"strings"
)

// ConfigValidator 配置验证器
type ConfigValidator struct{}

// NewConfigValidator 创建配置验证器
func NewConfigValidator() *ConfigValidator {
	return &ConfigValidator{}
}

// ValidateClientConfig 验证客户端配置
func (v *ConfigValidator) ValidateClientConfig(cfg *ClientConfig) error {
	// 验证基本配置
	if err := v.validateBasicClientConfig(cfg); err != nil {
		return fmt.Errorf("basic config validation failed: %w", err)
	}

	// 如果是多隧道模式，验证隧道配置
	if cfg.IsMultiTunnelMode() {
		if err := v.validateTunnelConfigs(cfg.Tunnels); err != nil {
			return fmt.Errorf("tunnel config validation failed: %w", err)
		}

		// 验证路由配置
		if cfg.Routing != nil {
			if err := v.validateRoutingConfig(cfg.Routing); err != nil {
				return fmt.Errorf("routing config validation failed: %w", err)
			}
		}

		// 验证控制器配置
		if cfg.Controller != nil {
			if err := v.validateControllerConfig(cfg.Controller); err != nil {
				return fmt.Errorf("controller config validation failed: %w", err)
			}
		}
	}

	return nil
}

// ValidateServerConfig 验证服务器配置
func (v *ConfigValidator) ValidateServerConfig(cfg *ServerConfig) error {
	// 验证基本配置
	if cfg.Host == "" {
		return fmt.Errorf("server host cannot be empty")
	}

	if cfg.Port <= 0 || cfg.Port > 65535 {
		return fmt.Errorf("invalid server port: %d", cfg.Port)
	}

	// 验证安全配置
	if cfg.Security != nil {
		if err := v.validateSecurityConfig(cfg.Security); err != nil {
			return fmt.Errorf("security config validation failed: %w", err)
		}
	}

	// 验证转发配置
	if cfg.Forwarding != nil {
		if err := v.validateForwardingConfig(cfg.Forwarding); err != nil {
			return fmt.Errorf("forwarding config validation failed: %w", err)
		}
	}

	return nil
}

// validateBasicClientConfig 验证基本客户端配置
func (v *ConfigValidator) validateBasicClientConfig(cfg *ClientConfig) error {
	if !cfg.IsMultiTunnelMode() {
		// 单隧道模式验证
		if cfg.ServerHost == "" {
			return fmt.Errorf("server host cannot be empty")
		}

		if cfg.ServerPort <= 0 || cfg.ServerPort > 65535 {
			return fmt.Errorf("invalid server port: %d", cfg.ServerPort)
		}
	}

	// 验证超时配置
	if cfg.ConnectTimeout < 0 {
		return fmt.Errorf("connect timeout cannot be negative")
	}

	if cfg.HeartbeatInterval < 0 {
		return fmt.Errorf("heartbeat interval cannot be negative")
	}

	// 验证TUN配置
	if cfg.Tun != nil && cfg.Tun.Enabled {
		if err := v.validateTunConfig(cfg.Tun); err != nil {
			return fmt.Errorf("TUN config validation failed: %w", err)
		}
	}

	// 验证加密配置
	if cfg.EnableEncryption {
		if cfg.EncryptionKey == "" {
			return fmt.Errorf("encryption key cannot be empty when encryption is enabled")
		}
		if len(cfg.EncryptionKey) < 16 {
			return fmt.Errorf("encryption key should be at least 16 characters long")
		}
		if cfg.EncryptionKey == "your-encryption-key-change-in-production" {
			return fmt.Errorf("please change the default encryption key for security")
		}
	}

	return nil
}

// validateTunnelConfigs 验证隧道配置
func (v *ConfigValidator) validateTunnelConfigs(tunnels []*TunnelConfig) error {
	if len(tunnels) == 0 {
		return fmt.Errorf("at least one tunnel must be configured")
	}

	names := make(map[string]bool)
	enabledCount := 0

	for i, tunnel := range tunnels {
		// 验证隧道名称唯一性
		if tunnel.Name == "" {
			return fmt.Errorf("tunnel %d: name cannot be empty", i)
		}

		if names[tunnel.Name] {
			return fmt.Errorf("duplicate tunnel name: %s", tunnel.Name)
		}
		names[tunnel.Name] = true

		// 验证服务器地址
		if tunnel.Server == "" {
			return fmt.Errorf("tunnel %s: server cannot be empty", tunnel.Name)
		}

		// 验证端口
		if tunnel.Port <= 0 || tunnel.Port > 65535 {
			return fmt.Errorf("tunnel %s: invalid port %d", tunnel.Name, tunnel.Port)
		}

		// 验证协议
		if !v.isValidProtocol(tunnel.Protocol) {
			return fmt.Errorf("tunnel %s: unsupported protocol %s", tunnel.Name, tunnel.Protocol)
		}

		// 验证优先级
		if tunnel.Priority < 0 {
			return fmt.Errorf("tunnel %s: priority cannot be negative", tunnel.Name)
		}

		// 统计启用的隧道
		if tunnel.Enabled {
			enabledCount++
		}

		// 验证TLS配置
		if tunnel.EnableTLS {
			if err := v.validateTLSFiles(tunnel.TLSCertFile, tunnel.TLSKeyFile, tunnel.TLSCAFile); err != nil {
				return fmt.Errorf("tunnel %s: TLS validation failed: %w", tunnel.Name, err)
			}
		}

		// 验证加密配置
		if tunnel.EnableEncryption {
			if tunnel.EncryptionKey == "" {
				return fmt.Errorf("tunnel %s: encryption key cannot be empty when encryption is enabled", tunnel.Name)
			}
			if len(tunnel.EncryptionKey) < 16 {
				return fmt.Errorf("tunnel %s: encryption key should be at least 16 characters long", tunnel.Name)
			}
		}
	}

	if enabledCount == 0 {
		return fmt.Errorf("at least one tunnel must be enabled")
	}

	return nil
}

// validateRoutingConfig 验证路由配置
func (v *ConfigValidator) validateRoutingConfig(cfg *RoutingConfig) error {
	// 验证策略
	validStrategies := []string{"priority", "round_robin", "load_balance", "failover"}
	if !v.contains(validStrategies, cfg.Strategy) {
		return fmt.Errorf("invalid routing strategy: %s", cfg.Strategy)
	}

	// 验证路由规则
	if len(cfg.Rules) == 0 {
		return fmt.Errorf("at least one routing rule must be configured")
	}

	names := make(map[string]bool)
	for i, rule := range cfg.Rules {
		// 验证规则名称
		if rule.Name == "" {
			return fmt.Errorf("rule %d: name cannot be empty", i)
		}

		if names[rule.Name] {
			return fmt.Errorf("duplicate rule name: %s", rule.Name)
		}
		names[rule.Name] = true

		// 验证目标地址
		if err := v.validateCIDR(rule.Destination); err != nil {
			return fmt.Errorf("rule %s: invalid destination %s: %w", rule.Name, rule.Destination, err)
		}

		// 验证动作
		validActions := []string{"tunnel", "direct", "block"}
		if !v.contains(validActions, rule.Action) {
			return fmt.Errorf("rule %s: invalid action %s", rule.Name, rule.Action)
		}

		// 验证优先级
		if rule.Priority < 0 {
			return fmt.Errorf("rule %s: priority cannot be negative", rule.Name)
		}
	}

	return nil
}

// validateControllerConfig 验证控制器配置
func (v *ConfigValidator) validateControllerConfig(cfg *ControllerConfig) error {
	if cfg.HealthCheckInterval < 0 {
		return fmt.Errorf("health check interval cannot be negative")
	}

	if cfg.HealthCheckTimeout < 0 {
		return fmt.Errorf("health check timeout cannot be negative")
	}

	if cfg.FailoverThreshold < 0 {
		return fmt.Errorf("failover threshold cannot be negative")
	}

	if cfg.FailoverCooldown < 0 {
		return fmt.Errorf("failover cooldown cannot be negative")
	}

	if cfg.MetricsInterval < 0 {
		return fmt.Errorf("metrics interval cannot be negative")
	}

	// 验证负载均衡方法
	if cfg.LoadBalanceEnabled {
		validMethods := []string{"round_robin", "least_conn", "weighted"}
		if !v.contains(validMethods, cfg.LoadBalanceMethod) {
			return fmt.Errorf("invalid load balance method: %s", cfg.LoadBalanceMethod)
		}
	}

	return nil
}

// validateSecurityConfig 验证安全配置
func (v *ConfigValidator) validateSecurityConfig(cfg *SecurityConfig) error {
	// 验证IP过滤配置
	if cfg.EnableIPFilter {
		for _, ip := range cfg.AllowedIPs {
			if err := v.validateCIDR(ip); err != nil {
				return fmt.Errorf("invalid allowed IP %s: %w", ip, err)
			}
		}

		for _, ip := range cfg.BlockedIPs {
			if err := v.validateCIDR(ip); err != nil {
				return fmt.Errorf("invalid blocked IP %s: %w", ip, err)
			}
		}
	}

	// 验证连接限制
	if cfg.MaxConnPerIP < 0 {
		return fmt.Errorf("max connections per IP cannot be negative")
	}

	// 验证速率限制
	if cfg.RateLimitEnabled && cfg.RateLimitPerMin < 0 {
		return fmt.Errorf("rate limit per minute cannot be negative")
	}

	// 验证TLS配置
	if cfg.EnableTLS {
		if err := v.validateTLSFiles(cfg.TLSCertFile, cfg.TLSKeyFile, cfg.TLSCAFile); err != nil {
			return fmt.Errorf("TLS validation failed: %w", err)
		}
	}

	// 验证加密配置
	if cfg.EnableEncryption {
		if cfg.EncryptionKey == "" {
			return fmt.Errorf("encryption key cannot be empty when encryption is enabled")
		}
		if len(cfg.EncryptionKey) < 16 {
			return fmt.Errorf("encryption key should be at least 16 characters long")
		}
		if cfg.EncryptionKey == "your-encryption-key-change-in-production" {
			return fmt.Errorf("please change the default encryption key for security")
		}
	}

	return nil
}

// validateForwardingConfig 验证转发配置
func (v *ConfigValidator) validateForwardingConfig(cfg *ForwardingConfig) error {
	// 验证网络配置
	for _, network := range cfg.AllowedNetworks {
		if err := v.validateCIDR(network); err != nil {
			return fmt.Errorf("invalid allowed network %s: %w", network, err)
		}
	}

	for _, network := range cfg.BlockedNetworks {
		if err := v.validateCIDR(network); err != nil {
			return fmt.Errorf("invalid blocked network %s: %w", network, err)
		}
	}

	return nil
}

// validateTunConfig 验证TUN配置
func (v *ConfigValidator) validateTunConfig(cfg *TunConfig) error {
	if cfg.Name == "" {
		return fmt.Errorf("TUN interface name cannot be empty")
	}

	if cfg.IP == "" {
		return fmt.Errorf("TUN IP address cannot be empty")
	}

	if net.ParseIP(cfg.IP) == nil {
		return fmt.Errorf("invalid TUN IP address: %s", cfg.IP)
	}

	if cfg.MTU <= 0 {
		return fmt.Errorf("TUN MTU must be positive")
	}

	return nil
}

// validateCIDR 验证CIDR格式
func (v *ConfigValidator) validateCIDR(cidr string) error {
	// 尝试解析为CIDR
	_, _, err := net.ParseCIDR(cidr)
	if err != nil {
		// 尝试解析为单个IP
		if net.ParseIP(cidr) == nil {
			return fmt.Errorf("invalid IP or CIDR format")
		}
	}
	return nil
}

// validateTLSFiles 验证TLS文件配置
func (v *ConfigValidator) validateTLSFiles(certFile, keyFile, caFile string) error {
	if certFile == "" {
		return fmt.Errorf("TLS certificate file cannot be empty")
	}

	if keyFile == "" {
		return fmt.Errorf("TLS key file cannot be empty")
	}

	// CA文件是可选的
	return nil
}

// isValidProtocol 检查协议是否有效
func (v *ConfigValidator) isValidProtocol(protocol string) bool {
	validProtocols := []string{"tcp", "websocket", "quic"}
	return v.contains(validProtocols, protocol)
}

// contains 检查切片是否包含指定元素
func (v *ConfigValidator) contains(slice []string, item string) bool {
	for _, s := range slice {
		if strings.EqualFold(s, item) {
			return true
		}
	}
	return false
}
