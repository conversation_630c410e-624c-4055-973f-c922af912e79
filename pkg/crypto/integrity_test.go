package crypto

import (
	"bytes"
	"testing"
)

func TestHMACChecker(t *testing.T) {
	key := []byte("test-hmac-key-1234567890")
	checker := NewHMACChecker(key)

	data := []byte("Hello, World! This is test data for HMAC.")

	// 测试签名
	signature, err := checker.Sign(data)
	if err != nil {
		t.Fatalf("HMAC signing failed: %v", err)
	}

	if len(signature) == 0 {
		t.<PERSON><PERSON>r("HMAC signature is empty")
	}

	// 测试验证
	err = checker.Verify(data, signature)
	if err != nil {
		t.Fatalf("HMAC verification failed: %v", err)
	}
}

func TestHMACCheckerWrongSignature(t *testing.T) {
	key := []byte("test-hmac-key")
	checker := NewHMACChecker(key)

	data := []byte("test data")
	wrongSignature := []byte("wrong signature")

	// 验证错误签名应该失败
	err := checker.Verify(data, wrongSignature)
	if err == nil {
		t.Error("Expected HMAC verification to fail with wrong signature")
	}
}

func TestHMACCheckerDifferentKeys(t *testing.T) {
	data := []byte("test data")

	checker1 := NewHMACChecker([]byte("key1"))
	checker2 := NewHMACChecker([]byte("key2"))

	signature, err := checker1.Sign(data)
	if err != nil {
		t.Fatalf("HMAC signing failed: %v", err)
	}

	// 使用不同密钥验证应该失败
	err = checker2.Verify(data, signature)
	if err == nil {
		t.Error("Expected HMAC verification to fail with different key")
	}
}

func TestHMACCheckerEmptyKey(t *testing.T) {
	checker := NewHMACChecker([]byte{})
	data := []byte("test data")

	// 空密钥应该返回错误
	_, err := checker.Sign(data)
	if err == nil {
		t.Error("Expected error with empty HMAC key")
	}

	err = checker.Verify(data, []byte("signature"))
	if err == nil {
		t.Error("Expected error with empty HMAC key")
	}
}

func TestNoOpChecker(t *testing.T) {
	checker := NewNoOpChecker()
	data := []byte("test data")

	// 测试签名
	signature, err := checker.Sign(data)
	if err != nil {
		t.Fatalf("NoOp signing failed: %v", err)
	}

	if signature != nil {
		t.Error("NoOp checker should return nil signature")
	}

	// 测试验证
	err = checker.Verify(data, []byte("any signature"))
	if err != nil {
		t.Fatalf("NoOp verification failed: %v", err)
	}
}

func TestSecureMessage(t *testing.T) {
	key := []byte("test-key-for-secure-message")
	checker := NewHMACChecker(key)
	data := []byte("This is secure message data")

	// 创建安全消息
	secureMsg, err := CreateSecureMessage(data, checker)
	if err != nil {
		t.Fatalf("Failed to create secure message: %v", err)
	}

	if !bytes.Equal(secureMsg.Data, data) {
		t.Error("Secure message data doesn't match original")
	}

	if len(secureMsg.Signature) == 0 {
		t.Error("Secure message signature is empty")
	}

	// 验证安全消息
	err = VerifySecureMessage(secureMsg, checker)
	if err != nil {
		t.Fatalf("Failed to verify secure message: %v", err)
	}
}

func TestSecureMessageTampering(t *testing.T) {
	key := []byte("test-key")
	checker := NewHMACChecker(key)
	data := []byte("original data")

	secureMsg, err := CreateSecureMessage(data, checker)
	if err != nil {
		t.Fatalf("Failed to create secure message: %v", err)
	}

	// 篡改数据
	secureMsg.Data[0] = secureMsg.Data[0] ^ 0xFF

	// 验证应该失败
	err = VerifySecureMessage(secureMsg, checker)
	if err == nil {
		t.Error("Expected verification to fail with tampered data")
	}
}

func TestCombinedCrypto(t *testing.T) {
	encKey := "encryption-key-1234567890"
	hmacKey := []byte("hmac-key-1234567890")

	encryptor := NewAESEncryptor(encKey)
	checker := NewHMACChecker(hmacKey)
	combined := NewCombinedCrypto(encryptor, checker)

	originalData := []byte("This is test data for combined crypto")

	// 保护数据
	protected, err := combined.Protect(originalData)
	if err != nil {
		t.Fatalf("Failed to protect data: %v", err)
	}

	// 保护后的数据应该与原始数据不同
	if bytes.Equal(originalData, protected) {
		t.Error("Protected data should be different from original")
	}

	// 解保护数据
	unprotected, err := combined.Unprotect(protected)
	if err != nil {
		t.Fatalf("Failed to unprotect data: %v", err)
	}

	// 解保护后的数据应该与原始数据相同
	if !bytes.Equal(originalData, unprotected) {
		t.Error("Unprotected data doesn't match original")
	}
}

func TestCombinedCryptoTampering(t *testing.T) {
	encKey := "encryption-key"
	hmacKey := []byte("hmac-key")

	encryptor := NewAESEncryptor(encKey)
	checker := NewHMACChecker(hmacKey)
	combined := NewCombinedCrypto(encryptor, checker)

	originalData := []byte("test data")

	protected, err := combined.Protect(originalData)
	if err != nil {
		t.Fatalf("Failed to protect data: %v", err)
	}

	// 篡改保护后的数据
	if len(protected) > 0 {
		protected[len(protected)-1] = protected[len(protected)-1] ^ 0xFF
	}

	// 解保护应该失败
	_, err = combined.Unprotect(protected)
	if err == nil {
		t.Error("Expected unprotect to fail with tampered data")
	}
}

func TestSecureMessageSerialization(t *testing.T) {
	msg := &SecureMessage{
		Data:      []byte("test data"),
		Signature: []byte("test signature"),
	}

	// 序列化
	serialized, err := serializeSecureMessage(msg)
	if err != nil {
		t.Fatalf("Failed to serialize secure message: %v", err)
	}

	// 反序列化
	deserialized, err := deserializeSecureMessage(serialized)
	if err != nil {
		t.Fatalf("Failed to deserialize secure message: %v", err)
	}

	// 检查数据是否一致
	if !bytes.Equal(msg.Data, deserialized.Data) {
		t.Error("Deserialized data doesn't match original")
	}

	if !bytes.Equal(msg.Signature, deserialized.Signature) {
		t.Error("Deserialized signature doesn't match original")
	}
}

func TestSecureMessageSerializationInvalidData(t *testing.T) {
	// 测试无效数据
	invalidData := []byte{1, 2, 3} // 太短

	_, err := deserializeSecureMessage(invalidData)
	if err == nil {
		t.Error("Expected error with invalid data")
	}
}
