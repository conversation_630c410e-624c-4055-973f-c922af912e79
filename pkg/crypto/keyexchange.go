package crypto

import (
	"crypto/rand"
	"crypto/sha256"
	"fmt"
	"math/big"
)

// KeyExchanger 密钥交换接口
type KeyExchanger interface {
	GenerateKeyPair() error
	GetPublicKey() []byte
	ComputeSharedSecret(peerPublicKey []byte) ([]byte, error)
}

// DHKeyExchanger <PERSON><PERSON><PERSON>-<PERSON>man密钥交换器
type DHKeyExchanger struct {
	p       *big.Int // 大素数
	g       *big.Int // 生成元
	private *big.Int // 私钥
	public  *big.Int // 公钥
}

// NewDHKeyExchanger 创建DH密钥交换器
func NewDHKeyExchanger() *DHKeyExchanger {
	// 使用RFC 3526中的2048位MODP组
	// 这是一个安全的预定义素数
	pHex := "FFFFFFFFFFFFFFFFC90FDAA22168C234C4C6628B80DC1CD129024E088A67CC74020BBEA63B139B22514A08798E3404DDEF9519B3CD3A431B302B0A6DF25F14374FE1356D6D51C245E485B576625E7EC6F44C42E9A637ED6B0BFF5CB6F406B7EDEE386BFB5A899FA5AE9F24117C4B1FE649286651ECE45B3DC2007CB8A163BF0598DA48361C55D39A69163FA8FD24CF5F83655D23DCA3AD961C62F356208552BB9ED529077096966D670C354E4ABC9804F1746C08CA18217C32905E462E36CE3BE39E772C180E86039B2783A2EC07A28FB5C55DF06F4C52C9DE2BCBF6955817183995497CEA956AE515D2261898FA051015728E5A8AACAA68FFFFFFFFFFFFFFFF"

	p := new(big.Int)
	p.SetString(pHex, 16)

	g := big.NewInt(2)

	return &DHKeyExchanger{
		p: p,
		g: g,
	}
}

// GenerateKeyPair 生成密钥对
func (dh *DHKeyExchanger) GenerateKeyPair() error {
	// 生成私钥 (1 < private < p-1)
	max := new(big.Int).Sub(dh.p, big.NewInt(2))
	private, err := rand.Int(rand.Reader, max)
	if err != nil {
		return fmt.Errorf("failed to generate private key: %w", err)
	}
	dh.private = new(big.Int).Add(private, big.NewInt(1))

	// 计算公钥 public = g^private mod p
	dh.public = new(big.Int).Exp(dh.g, dh.private, dh.p)

	return nil
}

// GetPublicKey 获取公钥
func (dh *DHKeyExchanger) GetPublicKey() []byte {
	if dh.public == nil {
		return nil
	}
	return dh.public.Bytes()
}

// ComputeSharedSecret 计算共享密钥
func (dh *DHKeyExchanger) ComputeSharedSecret(peerPublicKey []byte) ([]byte, error) {
	if dh.private == nil {
		return nil, fmt.Errorf("private key not generated")
	}

	peerPub := new(big.Int).SetBytes(peerPublicKey)

	// 验证对方公钥的有效性
	if peerPub.Cmp(big.NewInt(1)) <= 0 || peerPub.Cmp(new(big.Int).Sub(dh.p, big.NewInt(1))) >= 0 {
		return nil, fmt.Errorf("invalid peer public key")
	}

	// 计算共享密钥 shared = peerPublic^private mod p
	shared := new(big.Int).Exp(peerPub, dh.private, dh.p)

	// 使用SHA256对共享密钥进行哈希，得到固定长度的密钥
	hash := sha256.Sum256(shared.Bytes())
	return hash[:], nil
}

// SimpleKeyExchanger 简单密钥交换器（用于测试）
type SimpleKeyExchanger struct {
	key []byte
}

// NewSimpleKeyExchanger 创建简单密钥交换器
func NewSimpleKeyExchanger(key []byte) *SimpleKeyExchanger {
	return &SimpleKeyExchanger{key: key}
}

// GenerateKeyPair 生成密钥对（简单实现）
func (s *SimpleKeyExchanger) GenerateKeyPair() error {
	if len(s.key) == 0 {
		// 生成随机密钥
		s.key = make([]byte, 32)
		_, err := rand.Read(s.key)
		return err
	}
	return nil
}

// GetPublicKey 获取公钥（简单实现）
func (s *SimpleKeyExchanger) GetPublicKey() []byte {
	return s.key
}

// ComputeSharedSecret 计算共享密钥（简单实现）
func (s *SimpleKeyExchanger) ComputeSharedSecret(peerPublicKey []byte) ([]byte, error) {
	// 简单的XOR操作（仅用于测试）
	if len(peerPublicKey) != len(s.key) {
		return nil, fmt.Errorf("key length mismatch")
	}

	result := make([]byte, len(s.key))
	for i := range s.key {
		result[i] = s.key[i] ^ peerPublicKey[i]
	}

	// 使用SHA256确保结果长度一致
	hash := sha256.Sum256(result)
	return hash[:], nil
}
