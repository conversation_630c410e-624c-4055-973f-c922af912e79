package crypto

import (
	"bytes"
	"testing"
)

func TestDHKeyExchanger(t *testing.T) {
	// 创建两个DH密钥交换器
	alice := NewDHKeyExchanger()
	bob := NewDHKeyExchanger()

	// 生成密钥对
	err := alice.GenerateKeyPair()
	if err != nil {
		t.Fatalf("Alice failed to generate key pair: %v", err)
	}

	err = bob.GenerateKeyPair()
	if err != nil {
		t.Fatalf("Bob failed to generate key pair: %v", err)
	}

	// 获取公钥
	alicePublic := alice.GetPublicKey()
	bobPublic := bob.GetPublicKey()

	if len(alicePublic) == 0 {
		t.<PERSON>rror("Alice's public key is empty")
	}
	if len(bobPublic) == 0 {
		t.<PERSON>rror("Bob's public key is empty")
	}

	// 计算共享密钥
	aliceShared, err := alice.ComputeSharedSecret(bobPublic)
	if err != nil {
		t.Fatalf("Alice failed to compute shared secret: %v", err)
	}

	bobShared, err := bob.ComputeSharedSecret(alicePublic)
	if err != nil {
		t.Fatalf("Bob failed to compute shared secret: %v", err)
	}

	// 共享密钥应该相同
	if !bytes.Equal(aliceShared, bobShared) {
		t.Error("Shared secrets don't match")
	}

	// 共享密钥应该是32字节（SHA256输出）
	if len(aliceShared) != 32 {
		t.Errorf("Expected shared secret length 32, got %d", len(aliceShared))
	}
}

func TestDHKeyExchangerInvalidPeerKey(t *testing.T) {
	dh := NewDHKeyExchanger()
	err := dh.GenerateKeyPair()
	if err != nil {
		t.Fatalf("Failed to generate key pair: %v", err)
	}

	// 测试无效的对方公钥
	invalidKeys := [][]byte{
		{0},                // 太小
		{1},                // 等于1
		make([]byte, 1000), // 太大（全零）
	}

	for i, invalidKey := range invalidKeys {
		_, err := dh.ComputeSharedSecret(invalidKey)
		if err == nil {
			t.Errorf("Test %d: Expected error for invalid peer key, but got none", i)
		}
	}
}

func TestDHKeyExchangerNoPrivateKey(t *testing.T) {
	dh := NewDHKeyExchanger()
	// 不生成密钥对

	peerKey := []byte{1, 2, 3, 4}
	_, err := dh.ComputeSharedSecret(peerKey)
	if err == nil {
		t.Error("Expected error when computing shared secret without private key")
	}
}

func TestSimpleKeyExchanger(t *testing.T) {
	// 使用相同长度的密钥
	key1 := []byte("test-************************************") // 32字节
	key2 := []byte("test-key-abcdefghijklmnopqrstuvwxyz123456") // 32字节

	alice := NewSimpleKeyExchanger(key1)
	bob := NewSimpleKeyExchanger(key2)

	// 生成密钥对
	err := alice.GenerateKeyPair()
	if err != nil {
		t.Fatalf("Alice failed to generate key pair: %v", err)
	}

	err = bob.GenerateKeyPair()
	if err != nil {
		t.Fatalf("Bob failed to generate key pair: %v", err)
	}

	// 获取公钥
	alicePublic := alice.GetPublicKey()
	bobPublic := bob.GetPublicKey()

	// 计算共享密钥
	aliceShared, err := alice.ComputeSharedSecret(bobPublic)
	if err != nil {
		t.Fatalf("Alice failed to compute shared secret: %v", err)
	}

	bobShared, err := bob.ComputeSharedSecret(alicePublic)
	if err != nil {
		t.Fatalf("Bob failed to compute shared secret: %v", err)
	}

	// 共享密钥应该相同
	if !bytes.Equal(aliceShared, bobShared) {
		t.Error("Shared secrets don't match")
	}
}

func TestSimpleKeyExchangerRandomKey(t *testing.T) {
	// 测试随机密钥生成
	alice := NewSimpleKeyExchanger(nil)
	bob := NewSimpleKeyExchanger(nil)

	err := alice.GenerateKeyPair()
	if err != nil {
		t.Fatalf("Alice failed to generate random key: %v", err)
	}

	err = bob.GenerateKeyPair()
	if err != nil {
		t.Fatalf("Bob failed to generate random key: %v", err)
	}

	aliceKey := alice.GetPublicKey()
	bobKey := bob.GetPublicKey()

	if len(aliceKey) != 32 {
		t.Errorf("Expected Alice's key length 32, got %d", len(aliceKey))
	}

	if len(bobKey) != 32 {
		t.Errorf("Expected Bob's key length 32, got %d", len(bobKey))
	}

	// 随机生成的密钥应该不同
	if bytes.Equal(aliceKey, bobKey) {
		t.Error("Random keys should be different")
	}
}

func TestSimpleKeyExchangerKeyLengthMismatch(t *testing.T) {
	alice := NewSimpleKeyExchanger([]byte("short"))
	bob := NewSimpleKeyExchanger([]byte("much-longer-key"))

	err := alice.GenerateKeyPair()
	if err != nil {
		t.Fatalf("Alice failed to generate key pair: %v", err)
	}

	err = bob.GenerateKeyPair()
	if err != nil {
		t.Fatalf("Bob failed to generate key pair: %v", err)
	}

	bobPublic := bob.GetPublicKey()
	_, err = alice.ComputeSharedSecret(bobPublic)
	if err == nil {
		t.Error("Expected error for key length mismatch")
	}
}
