package crypto

import (
	"crypto/hmac"
	"crypto/sha256"
	"fmt"
)

// IntegrityChecker 完整性检查器接口
type IntegrityChecker interface {
	Sign(data []byte) ([]byte, error)
	Verify(data []byte, signature []byte) error
}

// HMACChecker HMAC完整性检查器
type HMACChecker struct {
	key []byte
}

// NewHMACChecker 创建HMAC检查器
func NewHMACChecker(key []byte) *HMACChecker {
	return &HMACChecker{
		key: key,
	}
}

// Sign 对数据进行HMAC签名
func (h *HMACChecker) Sign(data []byte) ([]byte, error) {
	if len(h.key) == 0 {
		return nil, fmt.Errorf("HMAC key is empty")
	}

	mac := hmac.New(sha256.New, h.key)
	mac.Write(data)
	return mac.Sum(nil), nil
}

// Verify 验证HMAC签名
func (h *HMACChecker) Verify(data []byte, signature []byte) error {
	if len(h.key) == 0 {
		return fmt.Errorf("HMAC key is empty")
	}

	expectedSignature, err := h.Sign(data)
	if err != nil {
		return fmt.Errorf("failed to compute expected signature: %w", err)
	}

	if !hmac.Equal(signature, expectedSignature) {
		return fmt.Errorf("HMAC verification failed")
	}

	return nil
}

// NoOpChecker 无操作完整性检查器（用于禁用完整性检查）
type NoOpChecker struct{}

// NewNoOpChecker 创建无操作检查器
func NewNoOpChecker() *NoOpChecker {
	return &NoOpChecker{}
}

// Sign 不进行签名
func (n *NoOpChecker) Sign(data []byte) ([]byte, error) {
	return nil, nil
}

// Verify 不进行验证
func (n *NoOpChecker) Verify(data []byte, signature []byte) error {
	return nil
}

// SecureMessage 安全消息结构（包含数据和HMAC）
type SecureMessage struct {
	Data      []byte `json:"data"`
	Signature []byte `json:"signature"`
}

// CreateSecureMessage 创建安全消息
func CreateSecureMessage(data []byte, checker IntegrityChecker) (*SecureMessage, error) {
	signature, err := checker.Sign(data)
	if err != nil {
		return nil, fmt.Errorf("failed to sign message: %w", err)
	}

	return &SecureMessage{
		Data:      data,
		Signature: signature,
	}, nil
}

// VerifySecureMessage 验证安全消息
func VerifySecureMessage(msg *SecureMessage, checker IntegrityChecker) error {
	return checker.Verify(msg.Data, msg.Signature)
}

// CombinedCrypto 组合加密和完整性检查
type CombinedCrypto struct {
	encryptor Encryptor
	checker   IntegrityChecker
}

// NewCombinedCrypto 创建组合加密器
func NewCombinedCrypto(encryptor Encryptor, checker IntegrityChecker) *CombinedCrypto {
	return &CombinedCrypto{
		encryptor: encryptor,
		checker:   checker,
	}
}

// Protect 保护数据（加密+签名）
func (c *CombinedCrypto) Protect(data []byte) ([]byte, error) {
	// 先加密
	encrypted, err := c.encryptor.Encrypt(data)
	if err != nil {
		return nil, fmt.Errorf("encryption failed: %w", err)
	}

	// 再签名
	signature, err := c.checker.Sign(encrypted)
	if err != nil {
		return nil, fmt.Errorf("signing failed: %w", err)
	}

	// 组合加密数据和签名
	secureMsg := &SecureMessage{
		Data:      encrypted,
		Signature: signature,
	}

	// 序列化安全消息
	return serializeSecureMessage(secureMsg)
}

// Unprotect 解保护数据（验证+解密）
func (c *CombinedCrypto) Unprotect(protectedData []byte) ([]byte, error) {
	// 反序列化安全消息
	secureMsg, err := deserializeSecureMessage(protectedData)
	if err != nil {
		return nil, fmt.Errorf("failed to deserialize secure message: %w", err)
	}

	// 验证签名
	err = c.checker.Verify(secureMsg.Data, secureMsg.Signature)
	if err != nil {
		return nil, fmt.Errorf("signature verification failed: %w", err)
	}

	// 解密数据
	decrypted, err := c.encryptor.Decrypt(secureMsg.Data)
	if err != nil {
		return nil, fmt.Errorf("decryption failed: %w", err)
	}

	return decrypted, nil
}

// 简单的序列化/反序列化函数（实际应用中可能使用更复杂的格式）
func serializeSecureMessage(msg *SecureMessage) ([]byte, error) {
	// 格式: [4字节签名长度][签名][数据]
	sigLen := len(msg.Signature)
	result := make([]byte, 4+sigLen+len(msg.Data))

	// 写入签名长度
	result[0] = byte(sigLen >> 24)
	result[1] = byte(sigLen >> 16)
	result[2] = byte(sigLen >> 8)
	result[3] = byte(sigLen)

	// 写入签名
	copy(result[4:4+sigLen], msg.Signature)

	// 写入数据
	copy(result[4+sigLen:], msg.Data)

	return result, nil
}

func deserializeSecureMessage(data []byte) (*SecureMessage, error) {
	if len(data) < 4 {
		return nil, fmt.Errorf("data too short")
	}

	// 读取签名长度
	sigLen := int(data[0])<<24 | int(data[1])<<16 | int(data[2])<<8 | int(data[3])

	if len(data) < 4+sigLen {
		return nil, fmt.Errorf("data too short for signature")
	}

	// 提取签名和数据
	signature := make([]byte, sigLen)
	copy(signature, data[4:4+sigLen])

	msgData := make([]byte, len(data)-4-sigLen)
	copy(msgData, data[4+sigLen:])

	return &SecureMessage{
		Data:      msgData,
		Signature: signature,
	}, nil
}
