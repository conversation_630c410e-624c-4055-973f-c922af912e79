package crypto

import (
	"bytes"
	"testing"
)

func TestAESEncryptor(t *testing.T) {
	password := "test-password-123"
	encryptor := NewAESEncryptor(password)

	testData := []byte("Hello, World! This is a test message for encryption.")

	// 测试加密
	encrypted, err := encryptor.Encrypt(testData)
	if err != nil {
		t.Fatalf("Encryption failed: %v", err)
	}

	// 加密后的数据应该与原始数据不同
	if bytes.Equal(testData, encrypted) {
		t.Error("Encrypted data should be different from original data")
	}

	// 测试解密
	decrypted, err := encryptor.Decrypt(encrypted)
	if err != nil {
		t.Fatalf("Decryption failed: %v", err)
	}

	// 解密后的数据应该与原始数据相同
	if !bytes.Equal(testData, decrypted) {
		t.<PERSON>("Decrypted data doesn't match original. Expected: %s, Got: %s", testData, decrypted)
	}
}

func TestAESEncryptorEmptyData(t *testing.T) {
	encryptor := NewAESEncryptor("test-password")

	// 测试空数据
	encrypted, err := encryptor.Encrypt([]byte{})
	if err != nil {
		t.Fatalf("Encryption of empty data failed: %v", err)
	}

	decrypted, err := encryptor.Decrypt(encrypted)
	if err != nil {
		t.Fatalf("Decryption of empty data failed: %v", err)
	}

	if len(decrypted) != 0 {
		t.Error("Decrypted empty data should be empty")
	}
}

func TestAESEncryptorDifferentPasswords(t *testing.T) {
	data := []byte("test data")

	encryptor1 := NewAESEncryptor("password1")
	encryptor2 := NewAESEncryptor("password2")

	encrypted, err := encryptor1.Encrypt(data)
	if err != nil {
		t.Fatalf("Encryption failed: %v", err)
	}

	// 使用不同密码的加密器解密应该失败
	_, err = encryptor2.Decrypt(encrypted)
	if err == nil {
		t.Error("Decryption with wrong password should fail")
	}
}

func TestNoOpEncryptor(t *testing.T) {
	encryptor := NewNoOpEncryptor()
	testData := []byte("test data")

	// 测试"加密"
	encrypted, err := encryptor.Encrypt(testData)
	if err != nil {
		t.Fatalf("NoOp encryption failed: %v", err)
	}

	// 数据应该保持不变
	if !bytes.Equal(testData, encrypted) {
		t.Error("NoOp encryptor should not modify data")
	}

	// 测试"解密"
	decrypted, err := encryptor.Decrypt(encrypted)
	if err != nil {
		t.Fatalf("NoOp decryption failed: %v", err)
	}

	// 数据应该保持不变
	if !bytes.Equal(testData, decrypted) {
		t.Error("NoOp decryptor should not modify data")
	}
}

func TestAESEncryptorRandomness(t *testing.T) {
	encryptor := NewAESEncryptor("test-password")
	data := []byte("same data")

	// 多次加密相同数据应该产生不同结果（由于随机nonce）
	encrypted1, err := encryptor.Encrypt(data)
	if err != nil {
		t.Fatalf("First encryption failed: %v", err)
	}

	encrypted2, err := encryptor.Encrypt(data)
	if err != nil {
		t.Fatalf("Second encryption failed: %v", err)
	}

	if bytes.Equal(encrypted1, encrypted2) {
		t.Error("Multiple encryptions of same data should produce different results")
	}

	// 但解密结果应该相同
	decrypted1, err := encryptor.Decrypt(encrypted1)
	if err != nil {
		t.Fatalf("First decryption failed: %v", err)
	}

	decrypted2, err := encryptor.Decrypt(encrypted2)
	if err != nil {
		t.Fatalf("Second decryption failed: %v", err)
	}

	if !bytes.Equal(decrypted1, decrypted2) {
		t.Error("Decrypted data should be the same")
	}

	if !bytes.Equal(data, decrypted1) {
		t.Error("Decrypted data should match original")
	}
}
