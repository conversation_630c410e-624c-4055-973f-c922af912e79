package logger

import (
	"encoding/json"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

func TestDefaultConfig(t *testing.T) {
	config := DefaultConfig()

	if config.Level != "info" {
		t.<PERSON>rf("Expected level 'info', got '%s'", config.Level)
	}
	if config.Format != "json" {
		t.<PERSON>("Expected format 'json', got '%s'", config.Format)
	}
	if config.Output != "stdout" {
		t.<PERSON>rf("Expected output 'stdout', got '%s'", config.Output)
	}
	if config.MaxSize != 100 {
		t.<PERSON>("Expected max size 100, got %d", config.MaxSize)
	}
	if config.MaxBackups != 3 {
		t.<PERSON><PERSON>rf("Expected max backups 3, got %d", config.MaxBackups)
	}
	if config.MaxAge != 28 {
		t.<PERSON><PERSON><PERSON>("Expected max age 28, got %d", config.MaxAge)
	}
	if !config.Compress {
		t.<PERSON>r("Expected compress to be true")
	}
}

func TestNewLoggerWithJSONFormat(t *testing.T) {
	config := &Config{
		Level:  "info",
		Format: "json",
		Output: "stdout",
	}

	logger, err := NewLogger(config)
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	if logger == nil {
		t.Fatal("Logger should not be nil")
	}
}

func TestNewLoggerWithConsoleFormat(t *testing.T) {
	config := &Config{
		Level:  "debug",
		Format: "console",
		Output: "stdout",
	}

	logger, err := NewLogger(config)
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	if logger == nil {
		t.Fatal("Logger should not be nil")
	}
}

func TestNewLoggerWithInvalidLevel(t *testing.T) {
	config := &Config{
		Level:  "invalid",
		Format: "json",
		Output: "stdout",
	}

	_, err := NewLogger(config)
	if err == nil {
		t.Error("Expected error for invalid log level")
	}
}

func TestNewLoggerWithFileOutput(t *testing.T) {
	tempDir := t.TempDir()
	logFile := filepath.Join(tempDir, "test.log")

	config := &Config{
		Level:  "info",
		Format: "json",
		Output: logFile,
	}

	logger, err := NewLogger(config)
	if err != nil {
		t.Fatalf("Failed to create logger with file output: %v", err)
	}

	if logger == nil {
		t.Fatal("Logger should not be nil")
	}

	// Test logging to file
	logger.Info("test message")
	logger.Sync()

	// Check if file was created and contains log
	if _, err := os.Stat(logFile); os.IsNotExist(err) {
		t.Error("Log file was not created")
	}
}

func TestNewLoggerWithStderrOutput(t *testing.T) {
	config := &Config{
		Level:  "warn",
		Format: "console",
		Output: "stderr",
	}

	logger, err := NewLogger(config)
	if err != nil {
		t.Fatalf("Failed to create logger with stderr output: %v", err)
	}

	if logger == nil {
		t.Fatal("Logger should not be nil")
	}
}

func TestNewDevelopmentLogger(t *testing.T) {
	logger, err := NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create development logger: %v", err)
	}

	if logger == nil {
		t.Fatal("Development logger should not be nil")
	}

	// Test that it can log at debug level
	logger.Debug("debug message")
	logger.Info("info message")
}

func TestNewProductionLogger(t *testing.T) {
	logger, err := NewProductionLogger()
	if err != nil {
		t.Fatalf("Failed to create production logger: %v", err)
	}

	if logger == nil {
		t.Fatal("Production logger should not be nil")
	}

	// Test logging
	logger.Info("info message")
	logger.Error("error message")
}

func TestLoggerLevels(t *testing.T) {
	testCases := []struct {
		level    string
		expected zapcore.Level
	}{
		{"debug", zapcore.DebugLevel},
		{"info", zapcore.InfoLevel},
		{"warn", zapcore.WarnLevel},
		{"error", zapcore.ErrorLevel},
	}

	for _, tc := range testCases {
		t.Run(tc.level, func(t *testing.T) {
			config := &Config{
				Level:  tc.level,
				Format: "json",
				Output: "stdout",
			}

			logger, err := NewLogger(config)
			if err != nil {
				t.Fatalf("Failed to create logger for level %s: %v", tc.level, err)
			}

			if logger == nil {
				t.Fatal("Logger should not be nil")
			}

			// Test that the logger was created successfully
			// We can't easily test the actual level without accessing internals
			// but we can verify the logger works
			logger.Info("test message")
		})
	}
}

func TestLoggerOutput(t *testing.T) {
	// Create a custom config that writes to our buffer
	tempDir := t.TempDir()
	logFile := filepath.Join(tempDir, "test.log")

	config := &Config{
		Level:  "info",
		Format: "json",
		Output: logFile,
	}

	logger, err := NewLogger(config)
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	// Log a test message
	testMessage := "test log message"
	logger.Info(testMessage)
	logger.Sync()

	// Read the log file
	content, err := os.ReadFile(logFile)
	if err != nil {
		t.Fatalf("Failed to read log file: %v", err)
	}

	// Parse JSON log entry
	var logEntry map[string]interface{}
	if err := json.Unmarshal(content, &logEntry); err != nil {
		t.Fatalf("Failed to parse JSON log: %v", err)
	}

	// Check log content
	if logEntry["msg"] != testMessage {
		t.Errorf("Expected message '%s', got '%v'", testMessage, logEntry["msg"])
	}
	if logEntry["level"] != "info" {
		t.Errorf("Expected level 'info', got '%v'", logEntry["level"])
	}
}

func TestLoggerFormats(t *testing.T) {
	testCases := []struct {
		format   string
		expected string
	}{
		{"json", "json"},
		{"console", "console"},
	}

	for _, tc := range testCases {
		t.Run(tc.format, func(t *testing.T) {
			tempDir := t.TempDir()
			logFile := filepath.Join(tempDir, "test.log")

			config := &Config{
				Level:  "info",
				Format: tc.format,
				Output: logFile,
			}

			logger, err := NewLogger(config)
			if err != nil {
				t.Fatalf("Failed to create logger with format %s: %v", tc.format, err)
			}

			// Log a test message
			logger.Info("test message")
			logger.Sync()

			// Read the log file
			content, err := os.ReadFile(logFile)
			if err != nil {
				t.Fatalf("Failed to read log file: %v", err)
			}

			contentStr := string(content)
			if tc.format == "json" {
				// Should be valid JSON
				var logEntry map[string]interface{}
				if err := json.Unmarshal(content, &logEntry); err != nil {
					t.Errorf("Expected valid JSON for format %s, got: %s", tc.format, contentStr)
				}
			} else {
				// Console format should contain the message
				if !strings.Contains(contentStr, "test message") {
					t.Errorf("Expected console format to contain message, got: %s", contentStr)
				}
			}
		})
	}
}

func TestLoggerWithFields(t *testing.T) {
	tempDir := t.TempDir()
	logFile := filepath.Join(tempDir, "test.log")

	config := &Config{
		Level:  "info",
		Format: "json",
		Output: logFile,
	}

	logger, err := NewLogger(config)
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	// Log with fields
	logger.Info("test message with fields",
		zap.String("key1", "value1"),
		zap.Int("key2", 42),
		zap.Bool("key3", true))
	logger.Sync()

	// Read and parse log
	content, err := os.ReadFile(logFile)
	if err != nil {
		t.Fatalf("Failed to read log file: %v", err)
	}

	var logEntry map[string]interface{}
	if err := json.Unmarshal(content, &logEntry); err != nil {
		t.Fatalf("Failed to parse JSON log: %v", err)
	}

	// Check fields
	if logEntry["key1"] != "value1" {
		t.Errorf("Expected key1='value1', got '%v'", logEntry["key1"])
	}
	if logEntry["key2"] != float64(42) { // JSON numbers are float64
		t.Errorf("Expected key2=42, got '%v'", logEntry["key2"])
	}
	if logEntry["key3"] != true {
		t.Errorf("Expected key3=true, got '%v'", logEntry["key3"])
	}
}

func TestLoggerErrorHandling(t *testing.T) {
	// Test with invalid file path
	config := &Config{
		Level:  "info",
		Format: "json",
		Output: "/invalid/path/that/does/not/exist/test.log",
	}

	_, err := NewLogger(config)
	if err == nil {
		t.Error("Expected error for invalid file path")
	}
}
