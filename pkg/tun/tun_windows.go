//go:build windows

package tun

import (
	"fmt"
	"os/exec"

	"github.com/songgao/water"
	"go.uber.org/zap"
)

// configureWindows 在Windows系统上配置TUN接口
func (t *TunInterface) configureWindows() error {
	ifaceName := t.iface.Name()

	// 在Windows上，接口名称可能是GUID格式
	// 设置IP地址
	cmd := exec.Command("netsh", "interface", "ip", "set", "address",
		fmt.Sprintf("name=%s", ifaceName), "static", t.config.IP, t.config.Netmask)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to set IP address: %w", err)
	}

	// 设置MTU
	cmd = exec.Command("netsh", "interface", "ipv4", "set", "subinterface",
		fmt.Sprintf("\"%s\"", ifaceName), fmt.Sprintf("mtu=%d", t.config.MTU))
	if err := cmd.Run(); err != nil {
		// MTU设置失败不是致命错误，记录警告
		t.logger.Warn("Failed to set MTU on Windows",
			zap.String("interface", ifaceName),
			zap.Error(err))
	}

	t.logger.Info("TUN interface configured on Windows",
		zap.String("interface", ifaceName),
		zap.String("ip", t.config.IP),
		zap.String("netmask", t.config.Netmask),
		zap.Int("mtu", t.config.MTU))

	return nil
}

// configureLinux 在Windows上不支持Linux配置
func (t *TunInterface) configureLinux() error {
	return fmt.Errorf("Linux configuration not supported on Windows")
}

// configureDarwin 在Windows上不支持macOS配置
func (t *TunInterface) configureDarwin() error {
	return fmt.Errorf("macOS configuration not supported on Windows")
}

// createTunInterface 在Windows上创建TUN接口
func createTunInterface(config *Config) (*water.Interface, error) {
	tunConfig := water.Config{
		DeviceType: water.TUN,
		PlatformSpecificParams: water.PlatformSpecificParams{
			InterfaceName: config.Name,
		},
	}
	return water.New(tunConfig)
}
