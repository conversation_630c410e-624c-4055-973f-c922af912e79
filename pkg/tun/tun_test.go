package tun

import (
	"net"
	"testing"

	"go.uber.org/zap"
)

func TestDefaultConfig(t *testing.T) {
	config := DefaultConfig()

	if config.Name != "cyber-tun" {
		t.<PERSON><PERSON>("Expected name 'cyber-tun', got '%s'", config.Name)
	}

	if config.IP != "********" {
		t.<PERSON><PERSON>("Expected IP '********', got '%s'", config.IP)
	}

	if config.Netmask != "*************" {
		t.<PERSON><PERSON>("Expected netmask '*************', got '%s'", config.Netmask)
	}

	if config.MTU != 1500 {
		t.<PERSON><PERSON>("Expected MTU 1500, got %d", config.MTU)
	}

	if config.Enabled {
		t.<PERSON>r("Expected Enabled to be false by default")
	}
}

func TestNewTunInterface_Disabled(t *testing.T) {
	logger := zap.NewNop()
	config := DefaultConfig()
	config.Enabled = false

	_, err := NewTunInterface(config, logger)
	if err == nil {
		t.Error("Expected error when TUN interface is disabled")
	}

	expectedError := "TUN interface is disabled"
	if err.Error() != expectedError {
		t.Errorf("Expected error '%s', got '%s'", expectedError, err.Error())
	}
}

func TestGetInterfaceIP(t *testing.T) {
	logger := zap.NewNop()
	config := &Config{
		Name:    "test-tun",
		IP:      "***********",
		Netmask: "*************",
		MTU:     1500,
		Enabled: true,
	}

	// 创建TUN接口实例（不实际创建接口）
	tun := &TunInterface{
		config: config,
		logger: logger,
	}

	ip := tun.GetInterfaceIP()
	if ip.String() != "***********" {
		t.Errorf("Expected IP '***********', got '%s'", ip.String())
	}
}

func TestGetInterfaceNetwork(t *testing.T) {
	logger := zap.NewNop()
	config := &Config{
		Name:    "test-tun",
		IP:      "***********",
		Netmask: "*************",
		MTU:     1500,
		Enabled: true,
	}

	tun := &TunInterface{
		config: config,
		logger: logger,
	}

	network, err := tun.GetInterfaceNetwork()
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}

	if network.IP.String() != "***********" {
		t.Errorf("Expected network IP '***********', got '%s'", network.IP.String())
	}

	expectedMask := "ffffff00"
	if network.Mask.String() != expectedMask {
		t.Errorf("Expected mask '%s', got '%s'", expectedMask, network.Mask.String())
	}
}

func TestGetInterfaceNetwork_InvalidIP(t *testing.T) {
	logger := zap.NewNop()
	config := &Config{
		Name:    "test-tun",
		IP:      "invalid-ip",
		Netmask: "*************",
		MTU:     1500,
		Enabled: true,
	}

	tun := &TunInterface{
		config: config,
		logger: logger,
	}

	_, err := tun.GetInterfaceNetwork()
	if err == nil {
		t.Error("Expected error for invalid IP address")
	}
}

func TestGetInterfaceNetwork_InvalidNetmask(t *testing.T) {
	logger := zap.NewNop()
	config := &Config{
		Name:    "test-tun",
		IP:      "***********",
		Netmask: "invalid-netmask",
		MTU:     1500,
		Enabled: true,
	}

	tun := &TunInterface{
		config: config,
		logger: logger,
	}

	_, err := tun.GetInterfaceNetwork()
	if err == nil {
		t.Error("Expected error for invalid netmask")
	}
}

func TestIsPacketForInterface(t *testing.T) {
	logger := zap.NewNop()
	config := &Config{
		Name:    "test-tun",
		IP:      "***********",
		Netmask: "*************",
		MTU:     1500,
		Enabled: true,
	}

	tun := &TunInterface{
		config: config,
		logger: logger,
	}

	// 创建一个IPv4数据包，目标IP为***********
	packet := make([]byte, 20)
	packet[0] = 0x45 // IPv4, header length 20
	// 设置目标IP (***********)
	packet[16] = 192
	packet[17] = 168
	packet[18] = 1
	packet[19] = 1

	if !tun.IsPacketForInterface(packet) {
		t.Error("Expected packet to be for interface")
	}

	// 测试不同的目标IP
	packet[19] = 2 // 改为***********
	if tun.IsPacketForInterface(packet) {
		t.Error("Expected packet not to be for interface")
	}
}

func TestIsPacketForInterface_TooShort(t *testing.T) {
	logger := zap.NewNop()
	config := &Config{
		Name:    "test-tun",
		IP:      "***********",
		Netmask: "*************",
		MTU:     1500,
		Enabled: true,
	}

	tun := &TunInterface{
		config: config,
		logger: logger,
	}

	// 太短的数据包
	packet := make([]byte, 10)
	if tun.IsPacketForInterface(packet) {
		t.Error("Expected short packet not to be for interface")
	}
}

func TestIsPacketForInterface_NotIPv4(t *testing.T) {
	logger := zap.NewNop()
	config := &Config{
		Name:    "test-tun",
		IP:      "***********",
		Netmask: "*************",
		MTU:     1500,
		Enabled: true,
	}

	tun := &TunInterface{
		config: config,
		logger: logger,
	}

	// IPv6数据包（但接口配置为IPv4）
	packet := make([]byte, 40)
	packet[0] = 0x60 // IPv6

	if tun.IsPacketForInterface(packet) {
		t.Error("Expected IPv6 packet not to be for IPv4 interface")
	}
}

func TestIsPacketForInterface_IPv6(t *testing.T) {
	logger := zap.NewNop()
	config := &Config{
		Name:    "test-tun",
		IP:      "2001:db8::1",
		Netmask: "ffff:ffff:ffff:ffff::",
		MTU:     1500,
		Enabled: true,
	}

	tun := &TunInterface{
		config: config,
		logger: logger,
	}

	// 创建一个IPv6数据包，目标IP为2001:db8::1
	packet := make([]byte, 40)
	packet[0] = 0x60 // IPv6
	// 设置目标IP (2001:db8::1)
	copy(packet[24:40], []byte{0x20, 0x01, 0x0d, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01})

	if !tun.IsPacketForInterface(packet) {
		t.Error("Expected IPv6 packet to be for interface")
	}

	// 测试不同的目标IP
	copy(packet[24:40], []byte{0x20, 0x01, 0x0d, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02})
	if tun.IsPacketForInterface(packet) {
		t.Error("Expected IPv6 packet not to be for interface")
	}
}

func TestGetPacketVersion(t *testing.T) {
	// IPv4 packet
	ipv4Packet := make([]byte, 20)
	ipv4Packet[0] = 0x45 // IPv4, header length 20

	if version := GetPacketVersion(ipv4Packet); version != 4 {
		t.Errorf("Expected IPv4 version 4, got %d", version)
	}

	// IPv6 packet
	ipv6Packet := make([]byte, 40)
	ipv6Packet[0] = 0x60 // IPv6

	if version := GetPacketVersion(ipv6Packet); version != 6 {
		t.Errorf("Expected IPv6 version 6, got %d", version)
	}

	// Empty packet
	if version := GetPacketVersion([]byte{}); version != 0 {
		t.Errorf("Expected version 0 for empty packet, got %d", version)
	}
}

func TestGetDestinationIP(t *testing.T) {
	// IPv4 packet
	ipv4Packet := make([]byte, 20)
	ipv4Packet[0] = 0x45 // IPv4
	ipv4Packet[16] = 192
	ipv4Packet[17] = 168
	ipv4Packet[18] = 1
	ipv4Packet[19] = 1

	dstIP := GetDestinationIP(ipv4Packet)
	expectedIP := net.ParseIP("***********")
	if !dstIP.Equal(expectedIP) {
		t.Errorf("Expected IPv4 destination %s, got %s", expectedIP, dstIP)
	}

	// IPv6 packet
	ipv6Packet := make([]byte, 40)
	ipv6Packet[0] = 0x60 // IPv6
	copy(ipv6Packet[24:40], []byte{0x20, 0x01, 0x0d, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01})

	dstIP = GetDestinationIP(ipv6Packet)
	expectedIP = net.ParseIP("2001:db8::1")
	if !dstIP.Equal(expectedIP) {
		t.Errorf("Expected IPv6 destination %s, got %s", expectedIP, dstIP)
	}
}

func TestGetSourceIP(t *testing.T) {
	// IPv4 packet
	ipv4Packet := make([]byte, 20)
	ipv4Packet[0] = 0x45 // IPv4
	// 设置源IP (********)
	ipv4Packet[12] = 10
	ipv4Packet[13] = 0
	ipv4Packet[14] = 0
	ipv4Packet[15] = 1

	srcIP := GetSourceIP(ipv4Packet)
	expectedIP := net.ParseIP("********")
	if !srcIP.Equal(expectedIP) {
		t.Errorf("Expected IPv4 source %s, got %s", expectedIP, srcIP)
	}

	// IPv6 packet
	ipv6Packet := make([]byte, 40)
	ipv6Packet[0] = 0x60 // IPv6
	// 设置源IP (2001:db8::2)
	copy(ipv6Packet[8:24], []byte{0x20, 0x01, 0x0d, 0xb8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02})

	srcIP = GetSourceIP(ipv6Packet)
	expectedIP = net.ParseIP("2001:db8::2")
	if !srcIP.Equal(expectedIP) {
		t.Errorf("Expected IPv6 source %s, got %s", expectedIP, srcIP)
	}
}

func TestGetIPv4SourceIP(t *testing.T) {
	// IPv4 packet
	packet := make([]byte, 20)
	packet[0] = 0x45 // IPv4
	packet[12] = 172
	packet[13] = 16
	packet[14] = 0
	packet[15] = 100

	srcIP := GetIPv4SourceIP(packet)
	expectedIP := net.ParseIP("************")
	if !srcIP.Equal(expectedIP) {
		t.Errorf("Expected IPv4 source %s, got %s", expectedIP, srcIP)
	}

	// 测试包太短的情况
	shortPacket := make([]byte, 10)
	srcIP = GetIPv4SourceIP(shortPacket)
	if srcIP != nil {
		t.Errorf("Expected nil for short packet, got %s", srcIP)
	}
}

func TestGetIPv6SourceIP(t *testing.T) {
	// IPv6 packet
	packet := make([]byte, 40)
	packet[0] = 0x60 // IPv6
	// 设置源IP (fe80::1)
	copy(packet[8:24], []byte{0xfe, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01})

	srcIP := GetIPv6SourceIP(packet)
	expectedIP := net.ParseIP("fe80::1")
	if !srcIP.Equal(expectedIP) {
		t.Errorf("Expected IPv6 source %s, got %s", expectedIP, srcIP)
	}

	// 测试包太短的情况
	shortPacket := make([]byte, 20)
	srcIP = GetIPv6SourceIP(shortPacket)
	if srcIP != nil {
		t.Errorf("Expected nil for short packet, got %s", srcIP)
	}
}

func TestGetIPv4Protocol(t *testing.T) {
	// TCP packet
	packet := make([]byte, 20)
	packet[0] = 0x45 // IPv4
	packet[9] = 6    // TCP

	protocol := GetIPv4Protocol(packet)
	if protocol != 6 {
		t.Errorf("Expected protocol 6 (TCP), got %d", protocol)
	}

	// UDP packet
	packet[9] = 17 // UDP
	protocol = GetIPv4Protocol(packet)
	if protocol != 17 {
		t.Errorf("Expected protocol 17 (UDP), got %d", protocol)
	}

	// 测试包太短的情况
	shortPacket := make([]byte, 10)
	protocol = GetIPv4Protocol(shortPacket)
	if protocol != 0 {
		t.Errorf("Expected protocol 0 for short packet, got %d", protocol)
	}
}

func TestGetIPv6NextHeader(t *testing.T) {
	// TCP packet
	packet := make([]byte, 40)
	packet[0] = 0x60 // IPv6
	packet[6] = 6    // TCP

	nextHeader := GetIPv6NextHeader(packet)
	if nextHeader != 6 {
		t.Errorf("Expected next header 6 (TCP), got %d", nextHeader)
	}

	// ICMPv6 packet
	packet[6] = 58 // ICMPv6
	nextHeader = GetIPv6NextHeader(packet)
	if nextHeader != 58 {
		t.Errorf("Expected next header 58 (ICMPv6), got %d", nextHeader)
	}

	// 测试包太短的情况
	shortPacket := make([]byte, 30)
	nextHeader = GetIPv6NextHeader(shortPacket)
	if nextHeader != 0 {
		t.Errorf("Expected next header 0 for short packet, got %d", nextHeader)
	}
}

func TestGetProtocol(t *testing.T) {
	// IPv4 TCP packet
	ipv4Packet := make([]byte, 20)
	ipv4Packet[0] = 0x45 // IPv4
	ipv4Packet[9] = 6    // TCP

	protocol := GetProtocol(ipv4Packet)
	if protocol != 6 {
		t.Errorf("Expected protocol 6 for IPv4 TCP, got %d", protocol)
	}

	// IPv6 UDP packet
	ipv6Packet := make([]byte, 40)
	ipv6Packet[0] = 0x60 // IPv6
	ipv6Packet[6] = 17   // UDP

	protocol = GetProtocol(ipv6Packet)
	if protocol != 17 {
		t.Errorf("Expected protocol 17 for IPv6 UDP, got %d", protocol)
	}

	// 无效版本
	invalidPacket := make([]byte, 20)
	invalidPacket[0] = 0x30 // 无效版本

	protocol = GetProtocol(invalidPacket)
	if protocol != 0 {
		t.Errorf("Expected protocol 0 for invalid version, got %d", protocol)
	}
}

func TestProtocolToString(t *testing.T) {
	testCases := []struct {
		protocol uint8
		isIPv6   bool
		expected string
	}{
		// IPv4 protocols
		{1, false, "ICMP"},
		{6, false, "TCP"},
		{17, false, "UDP"},
		{2, false, "IGMP"},
		{47, false, "GRE"},
		{50, false, "ESP"},
		{255, false, "Unknown-IPv4-255"},

		// IPv6 protocols
		{6, true, "TCP"},
		{17, true, "UDP"},
		{58, true, "ICMPv6"},
		{1, true, "ICMP"},
		{43, true, "IPv6-Route"},
		{44, true, "IPv6-Frag"},
		{59, true, "IPv6-NoNxt"},
		{255, true, "Unknown-IPv6-255"},
	}

	for _, tc := range testCases {
		result := ProtocolToString(tc.protocol, tc.isIPv6)
		if result != tc.expected {
			t.Errorf("ProtocolToString(%d, %v) = %s, expected %s", tc.protocol, tc.isIPv6, result, tc.expected)
		}
	}
}

func TestGetProtocolString(t *testing.T) {
	// IPv4 TCP packet
	ipv4Packet := make([]byte, 20)
	ipv4Packet[0] = 0x45 // IPv4
	ipv4Packet[9] = 6    // TCP

	protocolStr := GetProtocolString(ipv4Packet)
	if protocolStr != "TCP" {
		t.Errorf("Expected 'TCP' for IPv4 TCP packet, got '%s'", protocolStr)
	}

	// IPv6 ICMPv6 packet
	ipv6Packet := make([]byte, 40)
	ipv6Packet[0] = 0x60 // IPv6
	ipv6Packet[6] = 58   // ICMPv6

	protocolStr = GetProtocolString(ipv6Packet)
	if protocolStr != "ICMPv6" {
		t.Errorf("Expected 'ICMPv6' for IPv6 ICMPv6 packet, got '%s'", protocolStr)
	}

	// IPv4 UDP packet
	ipv4Packet[9] = 17 // UDP
	protocolStr = GetProtocolString(ipv4Packet)
	if protocolStr != "UDP" {
		t.Errorf("Expected 'UDP' for IPv4 UDP packet, got '%s'", protocolStr)
	}

	// IPv4 unknown protocol
	ipv4Packet[9] = 255 // Unknown
	protocolStr = GetProtocolString(ipv4Packet)
	if protocolStr != "Unknown-IPv4-255" {
		t.Errorf("Expected 'Unknown-IPv4-255' for unknown IPv4 protocol, got '%s'", protocolStr)
	}
}
