package tun

import (
	"fmt"
	"net"
	"runtime"

	"github.com/songgao/water"
	"go.uber.org/zap"
)

// TunInterface TUN接口管理器
type TunInterface struct {
	iface  *water.Interface
	logger *zap.Logger
	config *Config
}

// Config TUN接口配置
type Config struct {
	// 接口名称
	Name string `mapstructure:"name" yaml:"name"`
	// IP地址
	IP string `mapstructure:"ip" yaml:"ip"`
	// 子网掩码
	Netmask string `mapstructure:"netmask" yaml:"netmask"`
	// MTU大小
	MTU int `mapstructure:"mtu" yaml:"mtu"`
	// 是否启用
	Enabled bool `mapstructure:"enabled" yaml:"enabled"`
}

// DefaultConfig 返回默认TUN配置
func DefaultConfig() *Config {
	return &Config{
		Name:    "cyber-tun",
		IP:      "********",
		Netmask: "*************",
		MTU:     1500,
		Enabled: false,
	}
}

// NewTunInterface 创建新的TUN接口
func NewTunInterface(config *Config, logger *zap.Logger) (*TunInterface, error) {
	if !config.Enabled {
		return nil, fmt.Errorf("TUN interface is disabled")
	}

	// 创建TUN接口
	iface, err := createTunInterface(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create TUN interface: %w", err)
	}

	tun := &TunInterface{
		iface:  iface,
		logger: logger,
		config: config,
	}

	logger.Info("TUN interface created",
		zap.String("name", iface.Name()),
		zap.String("ip", config.IP),
		zap.String("netmask", config.Netmask),
		zap.Int("mtu", config.MTU))

	return tun, nil
}

// Start 启动TUN接口
func (t *TunInterface) Start() error {
	if err := t.configureInterface(); err != nil {
		return fmt.Errorf("failed to configure TUN interface: %w", err)
	}

	t.logger.Info("TUN interface started", zap.String("name", t.iface.Name()))
	return nil
}

// Stop 停止TUN接口
func (t *TunInterface) Stop() error {
	if t.iface != nil {
		if err := t.iface.Close(); err != nil {
			return fmt.Errorf("failed to close TUN interface: %w", err)
		}
		t.logger.Info("TUN interface stopped", zap.String("name", t.iface.Name()))
	}
	return nil
}

// Read 从TUN接口读取数据包
func (t *TunInterface) Read(buffer []byte) (int, error) {
	return t.iface.Read(buffer)
}

// Write 向TUN接口写入数据包
func (t *TunInterface) Write(packet []byte) (int, error) {
	return t.iface.Write(packet)
}

// Name 获取接口名称
func (t *TunInterface) Name() string {
	if t.iface != nil {
		return t.iface.Name()
	}
	return t.config.Name
}

// configureInterface 配置TUN接口
func (t *TunInterface) configureInterface() error {
	switch runtime.GOOS {
	case "linux":
		return t.configureLinux()
	case "darwin":
		return t.configureDarwin()
	case "windows":
		return t.configureWindows()
	default:
		return fmt.Errorf("unsupported operating system: %s", runtime.GOOS)
	}
}

// GetInterfaceIP 获取接口IP地址
func (t *TunInterface) GetInterfaceIP() net.IP {
	return net.ParseIP(t.config.IP)
}

// GetInterfaceNetwork 获取接口网络
func (t *TunInterface) GetInterfaceNetwork() (*net.IPNet, error) {
	ip := net.ParseIP(t.config.IP)
	if ip == nil {
		return nil, fmt.Errorf("invalid IP address: %s", t.config.IP)
	}

	mask := net.ParseIP(t.config.Netmask)
	if mask == nil {
		return nil, fmt.Errorf("invalid netmask: %s", t.config.Netmask)
	}

	// 将IPv4掩码转换为net.IPMask
	var ipMask net.IPMask
	if ip.To4() != nil {
		ipMask = net.IPv4Mask(mask[12], mask[13], mask[14], mask[15])
	} else {
		ipMask = net.IPMask(mask)
	}

	return &net.IPNet{
		IP:   ip,
		Mask: ipMask,
	}, nil
}

// IsPacketForInterface 检查数据包是否是发往本接口的
func (t *TunInterface) IsPacketForInterface(packet []byte) bool {
	if len(packet) < 20 { // 最小IP头部长度
		return false
	}

	// 解析IP头部
	version := packet[0] >> 4
	switch version {
	case 4:
		return t.isIPv4PacketForInterface(packet)
	case 6:
		return t.isIPv6PacketForInterface(packet)
	default:
		return false // 不支持的IP版本
	}
}

// isIPv4PacketForInterface 检查IPv4数据包是否是发往本接口的
func (t *TunInterface) isIPv4PacketForInterface(packet []byte) bool {
	if len(packet) < 20 { // 最小IPv4头部长度
		return false
	}

	// 获取目标IP地址
	dstIP := net.IP(packet[16:20])

	// 检查是否是本接口的IP
	interfaceIP := t.GetInterfaceIP()
	return dstIP.Equal(interfaceIP)
}

// isIPv6PacketForInterface 检查IPv6数据包是否是发往本接口的
func (t *TunInterface) isIPv6PacketForInterface(packet []byte) bool {
	if len(packet) < 40 { // 最小IPv6头部长度
		return false
	}

	// 获取目标IP地址 (IPv6地址在字节24-39)
	dstIP := net.IP(packet[24:40])

	// 检查是否是本接口的IP
	interfaceIP := t.GetInterfaceIP()
	return dstIP.Equal(interfaceIP)
}

// GetPacketVersion 获取数据包的IP版本
func GetPacketVersion(packet []byte) int {
	if len(packet) < 1 {
		return 0
	}
	return int(packet[0] >> 4)
}

// GetIPv4DestinationIP 获取IPv4数据包的目标IP地址
func GetIPv4DestinationIP(packet []byte) net.IP {
	if len(packet) < 20 {
		return nil
	}
	return net.IP(packet[16:20])
}

// GetIPv6DestinationIP 获取IPv6数据包的目标IP地址
func GetIPv6DestinationIP(packet []byte) net.IP {
	if len(packet) < 40 {
		return nil
	}
	return net.IP(packet[24:40])
}

// GetDestinationIP 获取数据包的目标IP地址（自动检测IPv4/IPv6）
func GetDestinationIP(packet []byte) net.IP {
	version := GetPacketVersion(packet)
	switch version {
	case 4:
		return GetIPv4DestinationIP(packet)
	case 6:
		return GetIPv6DestinationIP(packet)
	default:
		return nil
	}
}

// GetIPv4SourceIP 获取IPv4数据包的源IP地址
func GetIPv4SourceIP(packet []byte) net.IP {
	if len(packet) < 20 {
		return nil
	}
	return net.IP(packet[12:16])
}

// GetIPv6SourceIP 获取IPv6数据包的源IP地址
func GetIPv6SourceIP(packet []byte) net.IP {
	if len(packet) < 40 {
		return nil
	}
	return net.IP(packet[8:24])
}

// GetSourceIP 获取数据包的源IP地址（自动检测IPv4/IPv6）
func GetSourceIP(packet []byte) net.IP {
	version := GetPacketVersion(packet)
	switch version {
	case 4:
		return GetIPv4SourceIP(packet)
	case 6:
		return GetIPv6SourceIP(packet)
	default:
		return nil
	}
}

// GetIPv4Protocol 获取IPv4数据包的协议类型
func GetIPv4Protocol(packet []byte) uint8 {
	if len(packet) < 20 {
		return 0
	}
	return packet[9]
}

// GetIPv6NextHeader 获取IPv6数据包的下一个头部类型
func GetIPv6NextHeader(packet []byte) uint8 {
	if len(packet) < 40 {
		return 0
	}
	return packet[6]
}

// GetProtocol 获取数据包的协议类型（自动检测IPv4/IPv6）
func GetProtocol(packet []byte) uint8 {
	version := GetPacketVersion(packet)
	switch version {
	case 4:
		return GetIPv4Protocol(packet)
	case 6:
		return GetIPv6NextHeader(packet)
	default:
		return 0
	}
}

// ProtocolToString 将协议号转换为可读的协议名称
func ProtocolToString(protocol uint8, isIPv6 bool) string {
	if isIPv6 {
		// IPv6 Next Header types
		switch protocol {
		case 0:
			return "IPv6-Hop-by-Hop"
		case 1:
			return "ICMP"
		case 2:
			return "IGMP"
		case 4:
			return "IPv4"
		case 6:
			return "TCP"
		case 17:
			return "UDP"
		case 41:
			return "IPv6"
		case 43:
			return "IPv6-Route"
		case 44:
			return "IPv6-Frag"
		case 58:
			return "ICMPv6"
		case 59:
			return "IPv6-NoNxt"
		case 60:
			return "IPv6-Opts"
		default:
			return fmt.Sprintf("Unknown-IPv6-%d", protocol)
		}
	} else {
		// IPv4 Protocol types
		switch protocol {
		case 1:
			return "ICMP"
		case 2:
			return "IGMP"
		case 4:
			return "IPv4"
		case 6:
			return "TCP"
		case 17:
			return "UDP"
		case 41:
			return "IPv6"
		case 47:
			return "GRE"
		case 50:
			return "ESP"
		case 51:
			return "AH"
		case 89:
			return "OSPF"
		case 132:
			return "SCTP"
		default:
			return fmt.Sprintf("Unknown-IPv4-%d", protocol)
		}
	}
}

// GetProtocolString 获取数据包的协议名称（自动检测IPv4/IPv6）
func GetProtocolString(packet []byte) string {
	version := GetPacketVersion(packet)
	protocol := GetProtocol(packet)
	return ProtocolToString(protocol, version == 6)
}
