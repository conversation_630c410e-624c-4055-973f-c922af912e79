//go:build !darwin && !linux && !windows

package tun

import (
	"fmt"
	"runtime"

	"github.com/songgao/water"
)

// configureLinux 在不支持的平台上的Linux配置方法
func (t *TunInterface) configureLinux() error {
	return fmt.Errorf("Linux TUN interface configuration not supported on %s", runtime.GOOS)
}

// configureDarwin 在不支持的平台上的macOS配置方法
func (t *TunInterface) configureDarwin() error {
	return fmt.Errorf("macOS TUN interface configuration not supported on %s", runtime.GOOS)
}

// configureWindows 在不支持的平台上的Windows配置方法
func (t *TunInterface) configureWindows() error {
	return fmt.Errorf("Windows TUN interface configuration not supported on %s", runtime.GOOS)
}

// createTunInterface 在不支持的平台上创建TUN接口
func createTunInterface(config *Config) (*water.Interface, error) {
	return nil, fmt.E<PERSON>rf("TUN interface not supported on %s", runtime.GOOS)
}
