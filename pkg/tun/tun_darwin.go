//go:build darwin

package tun

import (
	"fmt"
	"os/exec"
	"strconv"

	"github.com/songgao/water"
	"go.uber.org/zap"
)

// configureDarwin 在macOS系统上配置TUN接口
func (t *TunInterface) configureDarwin() error {
	ifaceName := t.iface.Name()

	// 设置IP地址
	cmd := exec.Command("ifconfig", ifaceName, t.config.IP, t.config.Netmask)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to set IP address: %w", err)
	}

	// 启用接口
	cmd = exec.Command("ifconfig", ifaceName, "up")
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to bring up interface: %w", err)
	}

	// 设置MTU
	cmd = exec.Command("ifconfig", ifaceName, "mtu", strconv.Itoa(t.config.MTU))
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to set MTU: %w", err)
	}

	t.logger.Info("TUN interface configured on macOS",
		zap.String("interface", ifaceName),
		zap.String("ip", t.config.IP),
		zap.String("netmask", t.config.Netmask),
		zap.Int("mtu", t.config.MTU))

	return nil
}

// configureLinux 在macOS上不支持Linux配置
func (t *TunInterface) configureLinux() error {
	return fmt.Errorf("Linux configuration not supported on macOS")
}

// configureWindows 在macOS上不支持Windows配置
func (t *TunInterface) configureWindows() error {
	return fmt.Errorf("Windows configuration not supported on macOS")
}

// createTunInterface 在macOS上创建TUN接口
func createTunInterface(config *Config) (*water.Interface, error) {
	tunConfig := water.Config{
		DeviceType: water.TUN,
		PlatformSpecificParams: water.PlatformSpecificParams{
			Name: config.Name,
		},
	}
	return water.New(tunConfig)
}
