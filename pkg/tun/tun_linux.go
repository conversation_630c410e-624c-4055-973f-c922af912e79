//go:build linux

package tun

import (
	"fmt"
	"os/exec"
	"strconv"

	"github.com/songgao/water"
	"go.uber.org/zap"
)

// configureLinux 在Linux系统上配置TUN接口
func (t *TunInterface) configureLinux() error {
	ifaceName := t.iface.Name()

	// 设置IP地址
	cmd := exec.Command("ip", "addr", "add", fmt.Sprintf("%s/%s", t.config.IP, t.getNetmaskCIDR()), "dev", ifaceName)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to set IP address: %w", err)
	}

	// 启用接口
	cmd = exec.Command("ip", "link", "set", "dev", ifaceName, "up")
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to bring up interface: %w", err)
	}

	// 设置MTU
	cmd = exec.Command("ip", "link", "set", "dev", ifaceName, "mtu", strconv.Itoa(t.config.MTU))
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to set MTU: %w", err)
	}

	t.logger.Info("TUN interface configured on Linux",
		zap.String("interface", ifaceName),
		zap.String("ip", t.config.IP),
		zap.Int("mtu", t.config.MTU))

	return nil
}

// getNetmaskCIDR 将子网掩码转换为CIDR格式
func (t *TunInterface) getNetmaskCIDR() string {
	switch t.config.Netmask {
	case "*************":
		return "24"
	case "***********":
		return "16"
	case "*********":
		return "8"
	case "***************":
		return "25"
	case "***************":
		return "26"
	case "***************":
		return "27"
	case "***************":
		return "28"
	case "***************":
		return "29"
	case "***************":
		return "30"
	default:
		return "24" // 默认值
	}
}

// configureDarwin 在Linux上不支持macOS配置
func (t *TunInterface) configureDarwin() error {
	return fmt.Errorf("macOS configuration not supported on Linux")
}

// configureWindows 在Linux上不支持Windows配置
func (t *TunInterface) configureWindows() error {
	return fmt.Errorf("Windows configuration not supported on Linux")
}

// createTunInterface 在Linux上创建TUN接口
func createTunInterface(config *Config) (*water.Interface, error) {
	tunConfig := water.Config{
		DeviceType: water.TUN,
		PlatformSpecificParams: water.PlatformSpecificParams{
			Name: config.Name,
		},
	}
	return water.New(tunConfig)
}
