package transport

import (
	"fmt"

	"go.uber.org/zap"
)

// DefaultTransportFactory 默认传输工厂
type DefaultTransportFactory struct {
	logger *zap.Logger
}

// NewTransportFactory 创建传输工厂
func NewTransportFactory(logger *zap.Logger) TransportFactory {
	return &DefaultTransportFactory{
		logger: logger,
	}
}

// CreateTransport 创建传输实例
func (f *DefaultTransportFactory) CreateTransport(config *TransportConfig) (Transport, error) {
	switch config.Protocol {
	case "tcp":
		return NewTCPTransport(config, f.logger), nil
		
	case "websocket":
		return NewWebSocketTransport(config, f.logger), nil
		
	case "quic":
		// TODO: 实现QUIC传输
		return nil, fmt.Errorf("QUIC transport not implemented yet")
		
	default:
		return nil, fmt.Errorf("unsupported transport protocol: %s", config.Protocol)
	}
}

// SupportedProtocols 获取支持的协议列表
func (f *DefaultTransportFactory) SupportedProtocols() []string {
	return []string{"tcp", "websocket"}
}

// CreateTransportFromConfig 从配置创建传输
func CreateTransportFromConfig(protocol, address string, enableTLS bool, logger *zap.Logger) (Transport, error) {
	config := &TransportConfig{
		Protocol:  protocol,
		Address:   address,
		EnableTLS: enableTLS,
	}
	
	factory := NewTransportFactory(logger)
	return factory.CreateTransport(config)
}
