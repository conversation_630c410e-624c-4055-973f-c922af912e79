package transport

import (
	"context"
	"time"
)

// Transport 传输协议接口
type Transport interface {
	// Connect 连接到服务器
	Connect(ctx context.Context, addr string) error
	
	// Disconnect 断开连接
	Disconnect() error
	
	// Send 发送数据
	Send(data []byte) error
	
	// Receive 接收数据
	Receive() ([]byte, error)
	
	// IsConnected 检查是否已连接
	IsConnected() bool
	
	// GetMetrics 获取传输指标
	GetMetrics() *TransportMetrics
	
	// Close 关闭传输
	Close() error
}

// TransportConfig 传输配置
type TransportConfig struct {
	// 基本配置
	Protocol string `json:"protocol"` // tcp, websocket, quic
	Address  string `json:"address"`
	
	// 超时配置
	ConnectTimeout time.Duration `json:"connect_timeout"`
	ReadTimeout    time.Duration `json:"read_timeout"`
	WriteTimeout   time.Duration `json:"write_timeout"`
	
	// TLS配置
	EnableTLS     bool   `json:"enable_tls"`
	TLSCertFile   string `json:"tls_cert_file"`
	TLSKeyFile    string `json:"tls_key_file"`
	TLSCAFile     string `json:"tls_ca_file"`
	TLSSkipVerify bool   `json:"tls_skip_verify"`
	
	// 加密配置
	EnableEncryption bool   `json:"enable_encryption"`
	EncryptionKey    string `json:"encryption_key"`
	
	// 协议特定配置
	WebSocketPath string            `json:"websocket_path,omitempty"`
	Headers       map[string]string `json:"headers,omitempty"`
	
	// QUIC配置
	QUICConfig *QUICConfig `json:"quic_config,omitempty"`
}

// QUICConfig QUIC特定配置
type QUICConfig struct {
	MaxIdleTimeout  time.Duration `json:"max_idle_timeout"`
	MaxStreamCount  int64         `json:"max_stream_count"`
	KeepAlivePeriod time.Duration `json:"keep_alive_period"`
}

// TransportMetrics 传输指标
type TransportMetrics struct {
	// 连接指标
	TotalConnections  int64         `json:"total_connections"`
	FailedConnections int64         `json:"failed_connections"`
	ConnectionTime    time.Duration `json:"connection_time"`
	LastConnectTime   time.Time     `json:"last_connect_time"`
	
	// 数据传输指标
	BytesSent     int64 `json:"bytes_sent"`
	BytesReceived int64 `json:"bytes_received"`
	PacketsSent   int64 `json:"packets_sent"`
	PacketsRecv   int64 `json:"packets_received"`
	
	// 性能指标
	AverageLatency time.Duration `json:"average_latency"`
	LastSendTime   time.Time     `json:"last_send_time"`
	LastRecvTime   time.Time     `json:"last_recv_time"`
	
	// 错误指标
	SendErrors    int64 `json:"send_errors"`
	ReceiveErrors int64 `json:"receive_errors"`
	TimeoutErrors int64 `json:"timeout_errors"`
}

// TransportFactory 传输工厂接口
type TransportFactory interface {
	// CreateTransport 创建传输实例
	CreateTransport(config *TransportConfig) (Transport, error)
	
	// SupportedProtocols 获取支持的协议列表
	SupportedProtocols() []string
}

// TransportType 传输类型
type TransportType string

const (
	TransportTypeTCP       TransportType = "tcp"
	TransportTypeWebSocket TransportType = "websocket"
	TransportTypeQUIC      TransportType = "quic"
)

// TransportError 传输错误
type TransportError struct {
	Type    string `json:"type"`
	Message string `json:"message"`
	Code    int    `json:"code,omitempty"`
}

func (e *TransportError) Error() string {
	return e.Message
}

// NewTransportError 创建传输错误
func NewTransportError(errType, message string) *TransportError {
	return &TransportError{
		Type:    errType,
		Message: message,
	}
}

// IsTemporaryError 检查是否是临时错误
func IsTemporaryError(err error) bool {
	if transportErr, ok := err.(*TransportError); ok {
		return transportErr.Type == "temporary" || transportErr.Type == "timeout"
	}
	return false
}

// IsPermanentError 检查是否是永久错误
func IsPermanentError(err error) bool {
	if transportErr, ok := err.(*TransportError); ok {
		return transportErr.Type == "permanent" || transportErr.Type == "auth_failed"
	}
	return false
}
