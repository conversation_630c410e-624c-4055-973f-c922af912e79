package transport

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"fmt"
	"net"
	"os"
	"sync"
	"time"

	"go.uber.org/zap"
)

// TCPTransport TCP传输实现
type TCPTransport struct {
	config    *TransportConfig
	logger    *zap.Logger
	mu        sync.RWMutex
	
	conn      net.Conn
	connected bool
	metrics   *TransportMetrics
}

// NewTCPTransport 创建TCP传输
func NewTCPTransport(config *TransportConfig, logger *zap.Logger) *TCPTransport {
	return &TCPTransport{
		config:  config,
		logger:  logger,
		metrics: &TransportMetrics{},
	}
}

// Connect 连接到服务器
func (t *TCPTransport) Connect(ctx context.Context, addr string) error {
	t.mu.Lock()
	defer t.mu.Unlock()
	
	if t.connected {
		return nil
	}
	
	startTime := time.Now()
	t.logger.Info("Connecting to server", zap.String("address", addr))
	
	// 设置连接超时
	timeout := t.config.ConnectTimeout
	if timeout <= 0 {
		timeout = 10 * time.Second
	}
	
	var conn net.Conn
	var err error
	
	if t.config.EnableTLS {
		// TLS连接
		conn, err = t.connectTLS(ctx, addr, timeout)
	} else {
		// 普通TCP连接
		conn, err = net.DialTimeout("tcp", addr, timeout)
	}
	
	if err != nil {
		t.metrics.FailedConnections++
		return NewTransportError("connection_failed", fmt.Sprintf("failed to connect: %v", err))
	}
	
	t.conn = conn
	t.connected = true
	t.metrics.TotalConnections++
	t.metrics.ConnectionTime = time.Since(startTime)
	t.metrics.LastConnectTime = time.Now()
	
	t.logger.Info("Connected to server successfully", 
		zap.String("address", addr),
		zap.Duration("connection_time", t.metrics.ConnectionTime))
	
	return nil
}

// Disconnect 断开连接
func (t *TCPTransport) Disconnect() error {
	t.mu.Lock()
	defer t.mu.Unlock()
	
	if !t.connected || t.conn == nil {
		return nil
	}
	
	t.logger.Info("Disconnecting from server")
	
	err := t.conn.Close()
	t.conn = nil
	t.connected = false
	
	if err != nil {
		return NewTransportError("disconnect_failed", fmt.Sprintf("failed to disconnect: %v", err))
	}
	
	t.logger.Info("Disconnected from server")
	return nil
}

// Send 发送数据
func (t *TCPTransport) Send(data []byte) error {
	t.mu.RLock()
	conn := t.conn
	connected := t.connected
	t.mu.RUnlock()
	
	if !connected || conn == nil {
		t.metrics.SendErrors++
		return NewTransportError("not_connected", "transport not connected")
	}
	
	// 设置写超时
	if t.config.WriteTimeout > 0 {
		conn.SetWriteDeadline(time.Now().Add(t.config.WriteTimeout))
	}
	
	startTime := time.Now()
	n, err := conn.Write(data)
	
	if err != nil {
		t.metrics.SendErrors++
		return NewTransportError("send_failed", fmt.Sprintf("failed to send data: %v", err))
	}
	
	t.mu.Lock()
	t.metrics.BytesSent += int64(n)
	t.metrics.PacketsSent++
	t.metrics.LastSendTime = time.Now()
	
	// 更新平均延迟
	latency := time.Since(startTime)
	if t.metrics.PacketsSent == 1 {
		t.metrics.AverageLatency = latency
	} else {
		t.metrics.AverageLatency = (t.metrics.AverageLatency + latency) / 2
	}
	t.mu.Unlock()
	
	return nil
}

// Receive 接收数据
func (t *TCPTransport) Receive() ([]byte, error) {
	t.mu.RLock()
	conn := t.conn
	connected := t.connected
	t.mu.RUnlock()
	
	if !connected || conn == nil {
		t.metrics.ReceiveErrors++
		return nil, NewTransportError("not_connected", "transport not connected")
	}
	
	// 设置读超时
	if t.config.ReadTimeout > 0 {
		conn.SetReadDeadline(time.Now().Add(t.config.ReadTimeout))
	}
	
	buffer := make([]byte, 4096)
	n, err := conn.Read(buffer)
	
	if err != nil {
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			t.metrics.TimeoutErrors++
			return nil, NewTransportError("timeout", "read timeout")
		}
		t.metrics.ReceiveErrors++
		return nil, NewTransportError("receive_failed", fmt.Sprintf("failed to receive data: %v", err))
	}
	
	t.mu.Lock()
	t.metrics.BytesReceived += int64(n)
	t.metrics.PacketsRecv++
	t.metrics.LastRecvTime = time.Now()
	t.mu.Unlock()
	
	return buffer[:n], nil
}

// IsConnected 检查是否已连接
func (t *TCPTransport) IsConnected() bool {
	t.mu.RLock()
	defer t.mu.RUnlock()
	return t.connected
}

// GetMetrics 获取传输指标
func (t *TCPTransport) GetMetrics() *TransportMetrics {
	t.mu.RLock()
	defer t.mu.RUnlock()
	
	// 复制指标
	metrics := *t.metrics
	return &metrics
}

// Close 关闭传输
func (t *TCPTransport) Close() error {
	return t.Disconnect()
}

// connectTLS 创建TLS连接
func (t *TCPTransport) connectTLS(ctx context.Context, addr string, timeout time.Duration) (net.Conn, error) {
	// 创建TLS配置
	tlsConfig := &tls.Config{
		InsecureSkipVerify: t.config.TLSSkipVerify,
	}
	
	// 加载CA证书
	if t.config.TLSCAFile != "" {
		caCert, err := os.ReadFile(t.config.TLSCAFile)
		if err != nil {
			return nil, fmt.Errorf("failed to read CA certificate: %w", err)
		}
		
		caCertPool := x509.NewCertPool()
		if !caCertPool.AppendCertsFromPEM(caCert) {
			return nil, fmt.Errorf("failed to parse CA certificate")
		}
		tlsConfig.RootCAs = caCertPool
	}
	
	// 加载客户端证书
	if t.config.TLSCertFile != "" && t.config.TLSKeyFile != "" {
		cert, err := tls.LoadX509KeyPair(t.config.TLSCertFile, t.config.TLSKeyFile)
		if err != nil {
			return nil, fmt.Errorf("failed to load client certificate: %w", err)
		}
		tlsConfig.Certificates = []tls.Certificate{cert}
	}
	
	// 创建TLS连接
	dialer := &net.Dialer{Timeout: timeout}
	conn, err := tls.DialWithDialer(dialer, "tcp", addr, tlsConfig)
	if err != nil {
		return nil, fmt.Errorf("TLS connection failed: %w", err)
	}
	
	return conn, nil
}
