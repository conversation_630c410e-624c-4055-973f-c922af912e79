package transport

import (
	"context"
	"crypto/tls"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"go.uber.org/zap"
)

// WebSocketTransport WebSocket传输实现
type WebSocketTransport struct {
	config *TransportConfig
	logger *zap.Logger
	mu     sync.RWMutex

	conn      *websocket.Conn
	connected bool
	metrics   *TransportMetrics
}

// NewWebSocketTransport 创建WebSocket传输
func NewWebSocketTransport(config *TransportConfig, logger *zap.Logger) *WebSocketTransport {
	return &WebSocketTransport{
		config:  config,
		logger:  logger,
		metrics: &TransportMetrics{},
	}
}

// Connect 连接到服务器
func (w *WebSocketTransport) Connect(ctx context.Context, addr string) error {
	w.mu.Lock()
	defer w.mu.Unlock()

	if w.connected {
		return nil
	}

	startTime := time.Now()
	w.logger.Info("Connecting to WebSocket server", zap.String("address", addr))

	// 构建WebSocket URL
	wsURL, err := w.buildWebSocketURL(addr)
	if err != nil {
		w.metrics.FailedConnections++
		return NewTransportError("invalid_url", fmt.Sprintf("invalid WebSocket URL: %v", err))
	}

	// 创建WebSocket拨号器
	dialer := &websocket.Dialer{
		HandshakeTimeout: w.config.ConnectTimeout,
		Subprotocols:     []string{"cyber-bastion"},
	}

	// 设置TLS配置
	if w.config.EnableTLS {
		dialer.TLSClientConfig = &tls.Config{
			InsecureSkipVerify: w.config.TLSSkipVerify,
		}
	}

	// 设置请求头
	headers := http.Header{}
	for key, value := range w.config.Headers {
		headers.Set(key, value)
	}

	// 连接WebSocket
	conn, resp, err := dialer.DialContext(ctx, wsURL.String(), headers)
	if err != nil {
		w.metrics.FailedConnections++
		if resp != nil {
			return NewTransportError("connection_failed",
				fmt.Sprintf("WebSocket connection failed (HTTP %d): %v", resp.StatusCode, err))
		}
		return NewTransportError("connection_failed", fmt.Sprintf("WebSocket connection failed: %v", err))
	}

	if resp != nil {
		resp.Body.Close()
	}

	w.conn = conn
	w.connected = true
	w.metrics.TotalConnections++
	w.metrics.ConnectionTime = time.Since(startTime)
	w.metrics.LastConnectTime = time.Now()

	w.logger.Info("Connected to WebSocket server successfully",
		zap.String("address", addr),
		zap.Duration("connection_time", w.metrics.ConnectionTime))

	return nil
}

// Disconnect 断开连接
func (w *WebSocketTransport) Disconnect() error {
	w.mu.Lock()
	defer w.mu.Unlock()

	if !w.connected || w.conn == nil {
		return nil
	}

	w.logger.Info("Disconnecting from WebSocket server")

	// 发送关闭消息
	err := w.conn.WriteMessage(websocket.CloseMessage,
		websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""))
	if err != nil {
		w.logger.Warn("Failed to send close message", zap.Error(err))
	}

	// 关闭连接
	err = w.conn.Close()
	w.conn = nil
	w.connected = false

	if err != nil {
		return NewTransportError("disconnect_failed", fmt.Sprintf("failed to disconnect: %v", err))
	}

	w.logger.Info("Disconnected from WebSocket server")
	return nil
}

// Send 发送数据
func (w *WebSocketTransport) Send(data []byte) error {
	w.mu.RLock()
	conn := w.conn
	connected := w.connected
	w.mu.RUnlock()

	if !connected || conn == nil {
		w.metrics.SendErrors++
		return NewTransportError("not_connected", "WebSocket not connected")
	}

	// 设置写超时
	if w.config.WriteTimeout > 0 {
		conn.SetWriteDeadline(time.Now().Add(w.config.WriteTimeout))
	}

	startTime := time.Now()
	err := conn.WriteMessage(websocket.BinaryMessage, data)

	if err != nil {
		w.metrics.SendErrors++
		return NewTransportError("send_failed", fmt.Sprintf("failed to send WebSocket message: %v", err))
	}

	w.mu.Lock()
	w.metrics.BytesSent += int64(len(data))
	w.metrics.PacketsSent++
	w.metrics.LastSendTime = time.Now()

	// 更新平均延迟
	latency := time.Since(startTime)
	if w.metrics.PacketsSent == 1 {
		w.metrics.AverageLatency = latency
	} else {
		w.metrics.AverageLatency = (w.metrics.AverageLatency + latency) / 2
	}
	w.mu.Unlock()

	return nil
}

// Receive 接收数据
func (w *WebSocketTransport) Receive() ([]byte, error) {
	w.mu.RLock()
	conn := w.conn
	connected := w.connected
	w.mu.RUnlock()

	if !connected || conn == nil {
		w.metrics.ReceiveErrors++
		return nil, NewTransportError("not_connected", "WebSocket not connected")
	}

	// 设置读超时
	if w.config.ReadTimeout > 0 {
		conn.SetReadDeadline(time.Now().Add(w.config.ReadTimeout))
	}

	messageType, data, err := conn.ReadMessage()
	if err != nil {
		if websocket.IsCloseError(err, websocket.CloseNormalClosure, websocket.CloseGoingAway) {
			w.mu.Lock()
			w.connected = false
			w.mu.Unlock()
			return nil, NewTransportError("connection_closed", "WebSocket connection closed")
		}

		w.metrics.ReceiveErrors++
		return nil, NewTransportError("receive_failed", fmt.Sprintf("failed to receive WebSocket message: %v", err))
	}

	// 只处理二进制消息
	if messageType != websocket.BinaryMessage {
		w.logger.Warn("Received non-binary WebSocket message", zap.Int("type", messageType))
		return nil, NewTransportError("invalid_message", "received non-binary message")
	}

	w.mu.Lock()
	w.metrics.BytesReceived += int64(len(data))
	w.metrics.PacketsRecv++
	w.metrics.LastRecvTime = time.Now()
	w.mu.Unlock()

	return data, nil
}

// IsConnected 检查是否已连接
func (w *WebSocketTransport) IsConnected() bool {
	w.mu.RLock()
	defer w.mu.RUnlock()
	return w.connected
}

// GetMetrics 获取传输指标
func (w *WebSocketTransport) GetMetrics() *TransportMetrics {
	w.mu.RLock()
	defer w.mu.RUnlock()

	// 复制指标
	metrics := *w.metrics
	return &metrics
}

// Close 关闭传输
func (w *WebSocketTransport) Close() error {
	return w.Disconnect()
}

// buildWebSocketURL 构建WebSocket URL
func (w *WebSocketTransport) buildWebSocketURL(addr string) (*url.URL, error) {
	var scheme string
	if w.config.EnableTLS {
		scheme = "wss"
	} else {
		scheme = "ws"
	}

	// 解析地址
	host, port, err := parseAddress(addr)
	if err != nil {
		return nil, err
	}

	// 构建URL
	wsURL := &url.URL{
		Scheme: scheme,
		Host:   fmt.Sprintf("%s:%s", host, port),
		Path:   w.config.WebSocketPath,
	}

	if wsURL.Path == "" {
		wsURL.Path = "/ws"
	}

	return wsURL, nil
}

// parseAddress 解析地址
func parseAddress(addr string) (host, port string, err error) {
	if host, port, err = net.SplitHostPort(addr); err != nil {
		return "", "", fmt.Errorf("invalid address format: %s", addr)
	}
	return host, port, nil
}
