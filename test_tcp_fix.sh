#!/bin/bash

# TCP修复测试脚本

echo "🔧 TCP修复测试开始..."

# 清理之前的进程
echo "清理之前的进程..."
pkill -f cyber-bastion-server || true
pkill -f cyber-bastion-client || true
sleep 2

# 启动服务器
echo "启动服务器..."
./bin/cyber-bastion-server -c configs/server-single.yaml -l debug > server_test_new.log 2>&1 &
SERVER_PID=$!
echo "服务器PID: $SERVER_PID"

# 等待服务器启动
sleep 3

# 启动客户端
echo "启动客户端..."
./bin/cyber-bastion-client -c configs/client-single-server.yaml -l debug > client_test_new.log 2>&1 &
CLIENT_PID=$!
echo "客户端PID: $CLIENT_PID"

# 等待客户端连接
sleep 5

echo "等待测试运行..."
sleep 15

# 停止进程
echo "停止测试进程..."
kill $CLIENT_PID 2>/dev/null || true
kill $SERVER_PID 2>/dev/null || true

# 等待进程完全停止
sleep 2

echo "🔍 分析测试结果..."

echo "=== 服务器日志 (最后50行) ==="
tail -50 server_test_new.log

echo ""
echo "=== 客户端日志 (最后50行) ==="
tail -50 client_test_new.log

echo ""
echo "🔧 TCP修复测试完成！"
echo "请检查上述日志以确认TCP连接是否正常工作。"
