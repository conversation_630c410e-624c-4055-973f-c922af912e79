# Cyber Bastion

A high-performance TCP client-server application built with Go, featuring authentication, heartbeat monitoring, and automatic reconnection capabilities.

## Features

- **TCP Long Connection**: Persistent TCP connections between client and server
- **TLS Encryption**: Optional TLS encryption for secure communication
- **HTTP Masquerading**: Returns HTTP 200 responses to standard HTTP/HTTPS requests for security protection
- **Transparent Forwarding**: TUN interface-based transparent packet forwarding supporting all protocols
- **Authentication**: Token-based authentication system
- **Heartbeat Monitoring**: Automatic heartbeat to detect connection issues
- **Auto Reconnection**: C<PERSON> automatically reconnects on connection loss
- **Structured Logging**: Using zap for high-performance structured logging
- **Configuration Management**: YAML-based configuration with environment variable support
- **CLI Interface**: Command-line interface using cobra
- **Graceful Shutdown**: Proper signal handling and graceful shutdown
- **Cross-Platform**: Support for multiple platforms and architectures

## Supported Platforms

| Platform | Architecture | Status |
|----------|-------------|--------|
| Linux | amd64 | ✅ Supported |
| Linux | arm64 | ✅ Supported |
| macOS | amd64 (Intel) | ✅ Supported |
| macOS | arm64 (Apple Silicon) | ✅ Supported |
| Windows | amd64 | ✅ Supported |

## Architecture

```
cyber-bastion/
├── cmd/                    # Application entry points
│   ├── server/            # Server application
│   └── client/            # Client application
├── internal/              # Private application code
│   ├── server/            # Server implementation
│   └── client/            # Client implementation
├── pkg/                   # Public library code
│   ├── protocol/          # Communication protocol
│   ├── config/            # Configuration management
│   ├── tun/               # TUN interface management
│   └── logger/            # Logging utilities
├── configs/               # Configuration files
├── docs/                  # Documentation
└── scripts/               # Build and deployment scripts
```

## Quick Start

### Prerequisites

- Go 1.23.0 or later
- Make (optional, for using Makefile)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd cyber-bastion
```

2. Install dependencies:
```bash
go mod tidy
```

3. Build the applications:
```bash
make build
```

### Running the Applications

#### Start the Server

```bash
# Using Makefile
make run-server

# Or directly
./bin/server --config configs/server.yaml
```

#### Start the Client

```bash
# Using Makefile (test mode)
make run-client

# Interactive mode
make run-client-interactive

# Or directly
./bin/client --config configs/client.yaml --interactive
```

## TLS Security

Cyber Bastion supports TLS encryption for secure communication between server and client.

### Quick TLS Setup

Generate certificates and enable TLS:

```bash
# Generate certificates and enable TLS automatically
make certs-enable

# Run server with TLS
make run-server-tls

# Run client with TLS
make run-client-tls
```

### Certificate Management

```bash
# Generate certificates for development
make certs-quick

# Generate certificates with full options
make certs

# Verify existing certificates
make certs-verify

# Clean certificates
make certs-clean
```

For detailed TLS configuration and certificate management, see [TLS_CERTIFICATES.md](docs/TLS_CERTIFICATES.md).

## HTTP Masquerading

Cyber Bastion includes HTTP masquerading functionality to protect the server from detection and probing attacks. When standard HTTP or HTTPS requests are received, the server responds with HTTP 200 status and JSON data, making it appear as a normal web server.

### Features

- **Automatic Detection**: Detects HTTP/HTTPS requests before protocol processing
- **Standard Response**: Returns HTTP 200 with JSON data for all HTTP methods
- **Security Logging**: Records all HTTP attempts as security events
- **TLS Compatible**: Works with both HTTP and HTTPS requests

### Testing HTTP Masquerading

```bash
# Test HTTP masquerading
./scripts/test-http-masquerade.sh

# Manual testing
curl http://localhost:8080
curl https://localhost:8080 -k
```

For detailed information about HTTP masquerading, see [HTTP_MASQUERADING.md](docs/HTTP_MASQUERADING.md).

## Transparent Forwarding

Cyber Bastion supports transparent packet forwarding using TUN interfaces, allowing it to intercept and forward network traffic of all protocols through encrypted channels.

### Features

- **TUN Interface**: Creates and manages TUN interfaces for packet capture
- **Protocol Agnostic**: Supports forwarding of all IP protocols (TCP, UDP, ICMP, etc.)
- **Encrypted Transport**: All forwarded packets are encrypted using the existing encryption mechanism
- **Configurable Filtering**: Server-side filtering for allowed/blocked destination networks
- **Interface Binding**: Optional binding to specific network interfaces on the server side
- **Cross-Platform**: Supports Linux, macOS, and Windows

### Client Configuration

Enable transparent forwarding on the client by configuring the TUN interface:

```yaml
# Client configuration (configs/client.yaml)
tun:
  enabled: true              # Enable transparent forwarding
  name: "cyber-tun"          # TUN interface name
  ip: "********"            # TUN interface IP address
  netmask: "*************"   # TUN interface netmask
  mtu: 1500                  # TUN interface MTU size
```

### Server Configuration

Configure the server to handle forwarded packets:

```yaml
# Server configuration (configs/server.yaml)
forwarding:
  enabled: true              # Enable transparent packet forwarding
  bind_interface: false      # Bind forwarding to specific network interface
  interface_name: ""         # Network interface name (empty = use default routing)
  allowed_networks: []       # Allowed destination networks (empty = allow all)
    # - "***********/16"
    # - "10.0.0.0/8"
  blocked_networks:          # Blocked destination networks
    - "*********/8"         # Block localhost
    - "***********/16"      # Block link-local addresses
```

### Usage Example

1. **Start the server** with forwarding enabled:
```bash
./bin/server --config configs/server.yaml
```

2. **Start the client** with TUN interface enabled:
```bash
# Note: TUN interface creation requires root privileges
sudo ./bin/client --config configs/client.yaml
```

3. **Route traffic** through the TUN interface:
```bash
# Example: Route specific traffic through the TUN interface
sudo ip route add *************/24 dev cyber-tun
```

### Security Considerations

- **Root Privileges**: TUN interface creation requires root/administrator privileges
- **Network Filtering**: Configure `allowed_networks` and `blocked_networks` appropriately
- **Firewall Rules**: Ensure proper firewall configuration on both client and server
- **Encryption**: All forwarded traffic is encrypted using the configured encryption method

For detailed information about transparent forwarding, see [TRANSPARENT_FORWARDING.md](docs/TRANSPARENT_FORWARDING.md).

## Configuration

### Server Configuration (configs/server.yaml)

```yaml
host: "0.0.0.0"
port: 8080
read_timeout: 30
write_timeout: 30
max_clients: 100
auth_token: "cyber-bastion-secret-token"

logger:
  level: "info"
  format: "json"
  output: "stdout"
```

### Client Configuration (configs/client.yaml)

```yaml
server_host: "localhost"
server_port: 8080
connect_timeout: 10
heartbeat_interval: 30
reconnect_delay: 5
auth_token: "cyber-bastion-secret-token"

# Transparent forwarding settings (optional)
tun:
  enabled: false           # Enable transparent forwarding via TUN interface
  name: "cyber-tun"        # TUN interface name
  ip: "********"          # TUN interface IP address
  netmask: "*************" # TUN interface netmask
  mtu: 1500               # TUN interface MTU size

logger:
  level: "info"
  format: "console"
  output: "stdout"
```

### Environment Variables

You can override configuration values using environment variables with the prefix `CYBER_BASTION_SERVER_` or `CYBER_BASTION_CLIENT_`:

```bash
export CYBER_BASTION_SERVER_PORT=9090
export CYBER_BASTION_CLIENT_SERVER_HOST=*************
```

## Command Line Options

### Server

```bash
./bin/server [flags]

Flags:
  -c, --config string     config file path
  -H, --host string       server host
  -l, --log-level string  log level (debug, info, warn, error)
  -p, --port int          server port
```

### Client

```bash
./bin/client [flags]

Flags:
  -c, --config string     config file path
  -i, --interactive       run in interactive mode
  -l, --log-level string  log level (debug, info, warn, error)
  -p, --port int          server port
  -s, --server string     server host
```

## Protocol

The application uses a custom TCP protocol with the following message format:

```
[4 bytes length][JSON message]
```

### Message Types

- `MessageTypeAuth`: Authentication message
- `MessageTypeHeartbeat`: Heartbeat message
- `MessageTypeData`: Data message
- `MessageTypeResponse`: Response message
- `MessageTypeError`: Error message
- `MessageTypeTunData`: TUN interface data packet message

### Message Structure

```json
{
  "type": 1,
  "id": "message-id",
  "timestamp": 1640995200,
  "data": "base64-encoded-data"
}
```

## Development

### Building

```bash
# Build all binaries (recommended for most cases)
make build

# Build for all platforms (Linux, macOS, Windows - both amd64 and arm64 where supported)
make build-all

# Build for specific platforms
make build-linux      # Linux amd64 and arm64
make build-darwin     # macOS amd64 and arm64 (Intel and Apple Silicon)
make build-windows    # Windows amd64

# Build with Git version information (requires clean Git repo)
make build-with-vcs

# Build all platforms with Git version information
make build-all-with-vcs

# Clean build artifacts
make clean

# Diagnose Git-related build issues
make diagnose-git

# Show information about built binaries
make show-build-info

# Package release archives
make package-release VERSION=1.0.0

# Complete release workflow (build + package)
make release VERSION=1.0.0
```

#### Build Issues

If you encounter Git-related build errors like:
```
error obtaining VCS status: exit status 128
Use -buildvcs=false to disable VCS stamping.
```

**Solutions:**
1. Use the default build commands (`make build` or `make build-all`) which automatically disable VCS stamping
2. Run `make diagnose-git` to get detailed diagnosis and recommendations
3. For builds with version information, ensure your Git repository is clean and use `make build-with-vcs`

### Testing

```bash
# Run tests
make test

# Run tests with coverage
make test-coverage
```

### Code Quality

```bash
# Format code
make fmt

# Lint code (requires golangci-lint)
make lint

# Tidy dependencies
make tidy
```

## Deployment

### Binary Deployment

1. Build the binaries:
```bash
make build-all
```

2. Copy the appropriate binary and configuration files to your target system.

3. Run the applications with the desired configuration.

### Docker Deployment

```bash
# Build Docker images
make docker-build

# Run server
docker run -p 8080:8080 -v $(pwd)/configs:/app/configs cyber-bastion-server

# Run client
docker run --network host -v $(pwd)/configs:/app/configs cyber-bastion-client
```

## Monitoring and Logging

The application provides structured logging with configurable levels and formats:

- **Levels**: debug, info, warn, error
- **Formats**: json (for production), console (for development)
- **Outputs**: stdout, stderr, or file path

### Log Examples

```json
{
  "level": "info",
  "timestamp": "2023-12-31T12:00:00Z",
  "caller": "server/server.go:123",
  "msg": "Client connected",
  "client_id": "*************:54321"
}
```

## Troubleshooting

### Common Issues

1. **Connection Refused**: Check if the server is running and the port is correct.
2. **Authentication Failed**: Verify that the auth_token matches between client and server.
3. **Connection Timeout**: Check network connectivity and firewall settings.

### Debug Mode

Run applications with debug logging for detailed information:

```bash
./bin/server --log-level debug
./bin/client --log-level debug
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run tests and ensure they pass
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
