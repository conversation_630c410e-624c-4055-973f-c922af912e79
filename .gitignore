# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Build artifacts
/bin/
/dist/
/build/

# Release artifacts
/releases/

# Coverage reports
coverage.html
coverage.out
*.cover

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
/tmp/

# Configuration files with sensitive data (keep templates)
# Uncomment if you have sensitive config files
# config.yaml
# *.env
# .env.*

# Local development files
.local/
local/

# Backup files
*.bak
*.backup

# Archive files
*.tar.gz
*.zip
*.rar
*.7z

# Debug files
debug
*.pprof

# Air live reload tool
tmp/

# GoLand
.idea/

# VS Code
.vscode/

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Vim
[._]*.s[a-v][a-z]
[._]*.sw[a-p]
[._]s[a-rt-v][a-z]
[._]ss[a-gi-z]
[._]sw[a-p]

# certs
/certs/*
