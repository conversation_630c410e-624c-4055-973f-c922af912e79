package test

import (
	"fmt"
	"sync"
	"testing"
	"time"

	"cyber-bastion/internal/client"
	"cyber-bastion/internal/server"
	"cyber-bastion/pkg/config"
	"cyber-bastion/pkg/logger"
)

func TestClientServerIntegration(t *testing.T) {
	// Create server config
	serverCfg := &config.ServerConfig{
		Host:         "127.0.0.1",
		Port:         18080, // Use fixed port for testing
		ReadTimeout:  30,
		WriteTimeout: 30,
		MaxClients:   10,
		AuthToken:    "integration-test-token",
		Logger:       logger.DefaultConfig(),
	}

	// Create client config
	clientCfg := &config.ClientConfig{
		ServerHost:        "127.0.0.1",
		ServerPort:        18080, // Match server port
		ConnectTimeout:    5,
		HeartbeatInterval: 5,
		ReconnectDelay:    2,
		AuthToken:         "integration-test-token",
		Logger:            logger.DefaultConfig(),
	}

	// Create loggers
	serverLog, err := logger.NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create server logger: %v", err)
	}

	clientLog, err := logger.NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create client logger: %v", err)
	}

	// Create server
	srv := server.NewServer(serverCfg, serverLog)

	// Start server in goroutine
	serverDone := make(chan error, 1)
	go func() {
		serverDone <- srv.Start()
	}()

	// Give server time to start
	time.Sleep(100 * time.Millisecond)

	// Create and start client
	cli := client.NewClient(clientCfg, clientLog)

	err = cli.Start()
	if err != nil {
		t.Fatalf("Failed to start client: %v", err)
	}

	// Wait for client to connect and authenticate
	time.Sleep(500 * time.Millisecond)

	// Verify client is connected and authorized
	if !cli.IsConnected() {
		t.Error("Expected client to be connected")
	}
	if !cli.IsAuthorized() {
		t.Error("Expected client to be authorized")
	}

	// Test sending data
	testData := []byte("integration test data")
	err = cli.SendData(testData)
	if err != nil {
		t.Errorf("Failed to send data: %v", err)
	}

	// Give time for message processing
	time.Sleep(100 * time.Millisecond)

	// Stop client
	err = cli.Stop()
	if err != nil {
		t.Errorf("Failed to stop client: %v", err)
	}

	// Stop server
	err = srv.Stop()
	if err != nil {
		t.Errorf("Failed to stop server: %v", err)
	}

	// Wait for server to finish
	select {
	case err := <-serverDone:
		if err != nil && err.Error() != "context canceled" {
			t.Errorf("Server error: %v", err)
		}
	case <-time.After(2 * time.Second):
		t.Error("Server shutdown timed out")
	}
}

func TestMultipleClientsIntegration(t *testing.T) {
	// Create server config
	serverCfg := &config.ServerConfig{
		Host:         "127.0.0.1",
		Port:         18081, // Use fixed port for testing
		ReadTimeout:  30,
		WriteTimeout: 30,
		MaxClients:   5,
		AuthToken:    "multi-client-test-token",
		Logger:       logger.DefaultConfig(),
	}

	// Create logger
	serverLog, err := logger.NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create server logger: %v", err)
	}

	// Create server
	srv := server.NewServer(serverCfg, serverLog)

	// Start server in goroutine
	serverDone := make(chan error, 1)
	go func() {
		serverDone <- srv.Start()
	}()

	// Give server time to start
	time.Sleep(100 * time.Millisecond)

	// Create multiple clients
	numClients := 3
	clients := make([]*client.Client, numClients)
	var wg sync.WaitGroup

	for i := 0; i < numClients; i++ {
		clientCfg := &config.ClientConfig{
			ServerHost:        "127.0.0.1",
			ServerPort:        18081, // Match server port
			ConnectTimeout:    5,
			HeartbeatInterval: 10,
			ReconnectDelay:    2,
			AuthToken:         "multi-client-test-token",
			Logger:            logger.DefaultConfig(),
		}

		clientLog, err := logger.NewDevelopmentLogger()
		if err != nil {
			t.Fatalf("Failed to create client logger %d: %v", i, err)
		}

		clients[i] = client.NewClient(clientCfg, clientLog)

		wg.Add(1)
		go func(idx int, cli *client.Client) {
			defer wg.Done()

			// Start client
			err := cli.Start()
			if err != nil {
				t.Errorf("Failed to start client %d: %v", idx, err)
				return
			}

			// Wait for connection
			time.Sleep(200 * time.Millisecond)

			// Send test data
			testData := []byte(fmt.Sprintf("test data from client %d", idx))
			err = cli.SendData(testData)
			if err != nil {
				t.Errorf("Failed to send data from client %d: %v", idx, err)
			}

			// Keep client running for a bit
			time.Sleep(500 * time.Millisecond)

			// Stop client
			err = cli.Stop()
			if err != nil {
				t.Errorf("Failed to stop client %d: %v", idx, err)
			}
		}(i, clients[i])
	}

	// Wait for all clients to finish
	wg.Wait()

	// Stop server
	err = srv.Stop()
	if err != nil {
		t.Errorf("Failed to stop server: %v", err)
	}

	// Wait for server to finish
	select {
	case err := <-serverDone:
		if err != nil && err.Error() != "context canceled" {
			t.Errorf("Server error: %v", err)
		}
	case <-time.After(2 * time.Second):
		t.Error("Server shutdown timed out")
	}
}

func TestClientReconnectionIntegration(t *testing.T) {
	// Create server config
	serverCfg := &config.ServerConfig{
		Host:         "127.0.0.1",
		Port:         18082, // Use fixed port for testing
		ReadTimeout:  30,
		WriteTimeout: 30,
		MaxClients:   10,
		AuthToken:    "reconnection-test-token",
		Logger:       logger.DefaultConfig(),
	}

	// Create client config with short reconnect delay
	clientCfg := &config.ClientConfig{
		ServerHost:        "127.0.0.1",
		ServerPort:        18082, // Match server port
		ConnectTimeout:    5,
		HeartbeatInterval: 2,
		ReconnectDelay:    1, // Short delay for testing
		AuthToken:         "reconnection-test-token",
		Logger:            logger.DefaultConfig(),
	}

	// Create loggers
	serverLog, err := logger.NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create server logger: %v", err)
	}

	clientLog, err := logger.NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create client logger: %v", err)
	}

	// Create and start first server
	srv1 := server.NewServer(serverCfg, serverLog)

	serverDone1 := make(chan error, 1)
	go func() {
		serverDone1 <- srv1.Start()
	}()

	// Give server time to start
	time.Sleep(100 * time.Millisecond)

	// Create and start client
	cli := client.NewClient(clientCfg, clientLog)

	err = cli.Start()
	if err != nil {
		t.Fatalf("Failed to start client: %v", err)
	}

	// Wait for initial connection
	time.Sleep(300 * time.Millisecond)

	// Verify client is connected
	if !cli.IsConnected() {
		t.Error("Expected client to be connected initially")
	}

	// Stop first server (simulate server restart)
	err = srv1.Stop()
	if err != nil {
		t.Errorf("Failed to stop first server: %v", err)
	}

	// Wait for server to stop
	select {
	case <-serverDone1:
	case <-time.After(2 * time.Second):
		t.Error("First server shutdown timed out")
	}

	// Give client time to detect disconnection
	time.Sleep(500 * time.Millisecond)

	// Start second server (simulate server restart)
	srv2 := server.NewServer(serverCfg, serverLog)

	serverDone2 := make(chan error, 1)
	go func() {
		serverDone2 <- srv2.Start()
	}()

	// Give server time to start
	time.Sleep(100 * time.Millisecond)

	// Wait for client to reconnect
	time.Sleep(2 * time.Second)

	// Client should eventually reconnect
	// Note: This test might be flaky depending on timing
	// In a real scenario, you'd want more robust reconnection testing

	// Stop client
	err = cli.Stop()
	if err != nil {
		t.Errorf("Failed to stop client: %v", err)
	}

	// Stop second server
	err = srv2.Stop()
	if err != nil {
		t.Errorf("Failed to stop second server: %v", err)
	}

	// Wait for second server to finish
	select {
	case <-serverDone2:
	case <-time.After(2 * time.Second):
		t.Error("Second server shutdown timed out")
	}
}

func TestAuthenticationFailureIntegration(t *testing.T) {
	// Create server config
	serverCfg := &config.ServerConfig{
		Host:         "127.0.0.1",
		Port:         18083, // Use fixed port for testing
		ReadTimeout:  30,
		WriteTimeout: 30,
		MaxClients:   10,
		AuthToken:    "correct-token",
		Logger:       logger.DefaultConfig(),
	}

	// Create client config with wrong token
	clientCfg := &config.ClientConfig{
		ServerHost:        "127.0.0.1",
		ServerPort:        18083, // Match server port
		ConnectTimeout:    5,
		HeartbeatInterval: 10,
		ReconnectDelay:    2,
		AuthToken:         "wrong-token", // Wrong token
		Logger:            logger.DefaultConfig(),
	}

	// Create loggers
	serverLog, err := logger.NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create server logger: %v", err)
	}

	clientLog, err := logger.NewDevelopmentLogger()
	if err != nil {
		t.Fatalf("Failed to create client logger: %v", err)
	}

	// Create and start server
	srv := server.NewServer(serverCfg, serverLog)

	serverDone := make(chan error, 1)
	go func() {
		serverDone <- srv.Start()
	}()

	// Give server time to start
	time.Sleep(100 * time.Millisecond)

	// Create client with wrong token
	cli := client.NewClient(clientCfg, clientLog)

	// Start should fail due to authentication failure
	err = cli.Start()
	if err == nil {
		t.Error("Expected authentication failure, but client started successfully")
	}

	// Client should not be authorized
	if cli.IsAuthorized() {
		t.Error("Expected client to be unauthorized with wrong token")
	}

	// Stop client (should handle gracefully even if not properly started)
	cli.Stop()

	// Stop server
	err = srv.Stop()
	if err != nil {
		t.Errorf("Failed to stop server: %v", err)
	}

	// Wait for server to finish
	select {
	case <-serverDone:
	case <-time.After(2 * time.Second):
		t.Error("Server shutdown timed out")
	}
}
