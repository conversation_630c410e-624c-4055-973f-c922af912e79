package integration

import (
	"context"
	"testing"
	"time"

	"cyber-bastion/internal/client/controller"
	"cyber-bastion/internal/server/listener"
	serverv2 "cyber-bastion/internal/server/v2"
	"cyber-bastion/pkg/config"
	"cyber-bastion/pkg/transport"

	"go.uber.org/zap"
)

// TestNewArchitectureIntegration 测试新架构集成
func TestNewArchitectureIntegration(t *testing.T) {
	logger := zap.NewNop()

	t.Run("ConfigValidation", func(t *testing.T) {
		testConfigValidation(t, logger)
	})

	t.Run("TransportLayer", func(t *testing.T) {
		testTransportLayer(t, logger)
	})

	t.Run("ClientController", func(t *testing.T) {
		testClientController(t, logger)
	})

	t.Run("ServerListener", func(t *testing.T) {
		testServerListener(t, logger)
	})

	t.Run("ServerV2", func(t *testing.T) {
		testServerV2(t, logger)
	})
}

// testConfigValidation 测试配置验证
func testConfigValidation(t *testing.T, logger *zap.Logger) {
	validator := config.NewConfigValidator()

	// 测试客户端配置验证
	t.Run("ClientConfig", func(t *testing.T) {
		// 有效的单隧道配置
		singleTunnelConfig := &config.ClientConfig{
			ServerHost: "localhost",
			ServerPort: 8080,
		}

		if err := validator.ValidateClientConfig(singleTunnelConfig); err != nil {
			t.Errorf("Valid single tunnel config should pass validation: %v", err)
		}

		// 有效的多隧道配置
		multiTunnelConfig := &config.ClientConfig{
			Tunnels: []*config.TunnelConfig{
				{
					Name:     "primary",
					Server:   "localhost",
					Port:     8080,
					Protocol: "tcp",
					Priority: 1,
					Enabled:  true,
				},
			},
			Routing: config.DefaultRoutingConfig(),
		}

		if err := validator.ValidateClientConfig(multiTunnelConfig); err != nil {
			t.Errorf("Valid multi tunnel config should pass validation: %v", err)
		}

		// 无效配置：空隧道列表
		invalidConfig := &config.ClientConfig{
			Tunnels: []*config.TunnelConfig{},
		}

		if err := validator.ValidateClientConfig(invalidConfig); err == nil {
			t.Error("Empty tunnel list should fail validation")
		}
	})

	// 测试服务器配置验证
	t.Run("ServerConfig", func(t *testing.T) {
		validConfig := &config.ServerConfig{
			Host: "localhost",
			Port: 8080,
		}

		if err := validator.ValidateServerConfig(validConfig); err != nil {
			t.Errorf("Valid server config should pass validation: %v", err)
		}

		invalidConfig := &config.ServerConfig{
			Host: "",
			Port: -1,
		}

		if err := validator.ValidateServerConfig(invalidConfig); err == nil {
			t.Error("Invalid server config should fail validation")
		}
	})
}

// testTransportLayer 测试传输层
func testTransportLayer(t *testing.T, logger *zap.Logger) {
	factory := transport.NewTransportFactory(logger)

	t.Run("SupportedProtocols", func(t *testing.T) {
		protocols := factory.SupportedProtocols()
		if len(protocols) == 0 {
			t.Error("Factory should support at least one protocol")
		}

		expectedProtocols := []string{"tcp", "websocket"}
		for _, expected := range expectedProtocols {
			found := false
			for _, protocol := range protocols {
				if protocol == expected {
					found = true
					break
				}
			}
			if !found {
				t.Errorf("Expected protocol %s not found in supported protocols", expected)
			}
		}
	})

	t.Run("CreateTCPTransport", func(t *testing.T) {
		config := &transport.TransportConfig{
			Protocol: "tcp",
			Address:  "localhost:8080",
		}

		transport, err := factory.CreateTransport(config)
		if err != nil {
			t.Fatalf("Failed to create TCP transport: %v", err)
		}

		if transport == nil {
			t.Error("Transport should not be nil")
		}

		// 测试基本方法
		if transport.IsConnected() {
			t.Error("New transport should not be connected")
		}

		metrics := transport.GetMetrics()
		if metrics == nil {
			t.Error("Metrics should not be nil")
		}
	})

	t.Run("CreateWebSocketTransport", func(t *testing.T) {
		config := &transport.TransportConfig{
			Protocol: "websocket",
			Address:  "localhost:8080",
		}

		transport, err := factory.CreateTransport(config)
		if err != nil {
			t.Fatalf("Failed to create WebSocket transport: %v", err)
		}

		if transport == nil {
			t.Error("Transport should not be nil")
		}
	})

	t.Run("UnsupportedProtocol", func(t *testing.T) {
		config := &transport.TransportConfig{
			Protocol: "unsupported",
			Address:  "localhost:8080",
		}

		_, err := factory.CreateTransport(config)
		if err == nil {
			t.Error("Should fail to create transport for unsupported protocol")
		}
	})
}

// testClientController 测试客户端控制器
func testClientController(t *testing.T, logger *zap.Logger) {
	// 创建测试配置
	cfg := &config.ClientConfig{
		Tunnels: []*config.TunnelConfig{
			{
				Name:     "test",
				Server:   "localhost",
				Port:     8080,
				Protocol: "tcp",
				Priority: 1,
				Enabled:  true,
			},
		},
		Routing:    config.DefaultRoutingConfig(),
		Controller: config.DefaultControllerConfig(),
	}

	t.Run("CreateController", func(t *testing.T) {
		controller, err := controller.NewController(cfg, logger)
		if err != nil {
			t.Fatalf("Failed to create controller: %v", err)
		}

		if controller == nil {
			t.Error("Controller should not be nil")
		}

		// 测试状态
		status := controller.GetStatus()
		if status == nil {
			t.Error("Status should not be nil")
		}

		if status.State != "stopped" {
			t.Errorf("Initial state should be 'stopped', got '%s'", status.State)
		}
	})

	t.Run("ControllerComponents", func(t *testing.T) {
		controller, err := controller.NewController(cfg, logger)
		if err != nil {
			t.Fatalf("Failed to create controller: %v", err)
		}

		// 测试组件
		tunnelManager := controller.GetTunnelManager()
		if tunnelManager == nil {
			t.Error("Tunnel manager should not be nil")
		}

		router := controller.GetRouter()
		if router == nil {
			t.Error("Router should not be nil")
		}

		forwarder := controller.GetForwarder()
		if forwarder == nil {
			t.Error("Forwarder should not be nil")
		}
	})
}

// testServerListener 测试服务器监听器
func testServerListener(t *testing.T, logger *zap.Logger) {
	manager := listener.NewListenerManager(logger)

	t.Run("CreateListenerManager", func(t *testing.T) {
		if manager == nil {
			t.Error("Listener manager should not be nil")
		}

		listeners := manager.GetListeners()
		if listeners == nil {
			t.Error("Listeners map should not be nil")
		}

		if len(listeners) != 0 {
			t.Error("Initial listeners should be empty")
		}
	})

	t.Run("AddTCPListener", func(t *testing.T) {
		config := &listener.ListenerConfig{
			Protocol: "tcp",
			Address:  "127.0.0.1",
			Port:     0, // 使用随机端口
		}

		err := manager.AddListener(config)
		if err != nil {
			t.Fatalf("Failed to add TCP listener: %v", err)
		}

		listeners := manager.GetListeners()
		if len(listeners) != 1 {
			t.Errorf("Expected 1 listener, got %d", len(listeners))
		}
	})

	t.Run("GetMetrics", func(t *testing.T) {
		metrics := manager.GetMetrics()
		if metrics == nil {
			t.Error("Metrics should not be nil")
		}

		if metrics.ListenerCount != 1 {
			t.Errorf("Expected 1 listener in metrics, got %d", metrics.ListenerCount)
		}
	})
}

// testServerV2 测试服务器v2
func testServerV2(t *testing.T, logger *zap.Logger) {
	cfg := &config.ServerConfig{
		Host: "127.0.0.1",
		Port: 0, // 使用随机端口
	}

	t.Run("CreateServerV2", func(t *testing.T) {
		server, err := serverv2.NewServer(cfg, logger)
		if err != nil {
			t.Fatalf("Failed to create server v2: %v", err)
		}

		if server == nil {
			t.Error("Server should not be nil")
		}

		// 测试初始状态
		metrics := server.GetMetrics()
		if metrics == nil {
			t.Error("Metrics should not be nil")
		}

		if metrics.ActiveConnections != 0 {
			t.Error("Initial active connections should be 0")
		}
	})

	t.Run("ServerLifecycle", func(t *testing.T) {
		server, err := serverv2.NewServer(cfg, logger)
		if err != nil {
			t.Fatalf("Failed to create server v2: %v", err)
		}

		// 测试启动
		ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
		defer cancel()

		// 注意：这里可能会因为端口冲突而失败，这在测试环境中是正常的
		err = server.Start()
		if err != nil {
			t.Logf("Server start failed (expected in test environment): %v", err)
		} else {
			// 如果启动成功，测试停止
			defer func() {
				if stopErr := server.Stop(); stopErr != nil {
					t.Errorf("Failed to stop server: %v", stopErr)
				}
			}()

			// 等待一小段时间让服务器完全启动
			time.Sleep(time.Millisecond * 100)

			// 测试指标
			metrics := server.GetMetrics()
			if metrics.StartTime.IsZero() {
				t.Error("Start time should be set")
			}
		}

		select {
		case <-ctx.Done():
			// 测试超时，这是正常的
		default:
		}
	})
}

// BenchmarkNewArchitecture 性能基准测试
func BenchmarkNewArchitecture(b *testing.B) {
	logger := zap.NewNop()

	b.Run("ConfigValidation", func(b *testing.B) {
		validator := config.NewConfigValidator()
		cfg := &config.ClientConfig{
			ServerHost: "localhost",
			ServerPort: 8080,
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			validator.ValidateClientConfig(cfg)
		}
	})

	b.Run("TransportCreation", func(b *testing.B) {
		factory := transport.NewTransportFactory(logger)
		config := &transport.TransportConfig{
			Protocol: "tcp",
			Address:  "localhost:8080",
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			transport, _ := factory.CreateTransport(config)
			if transport != nil {
				transport.Close()
			}
		}
	})
}
